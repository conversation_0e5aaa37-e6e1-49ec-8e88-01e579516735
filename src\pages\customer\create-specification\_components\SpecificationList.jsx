import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Eye, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function SpecificationList({ specifications, onRemoveSpecification }) {
  const navigate = useNavigate();
  return (
    <Card>
      <CardHeader>
        <CardTitle>Specifications</CardTitle>
      </CardHeader>
      <CardContent>
        {specifications.map((spec, index) => (
          <div
            key={index}
            className="flex items-center justify-between py-2 border-b last:border-b-0"
          >
            <div>
              <p className="font-semibold">{spec.product_name}</p>
              <p className="text-sm text-gray-500">
                {spec.category} - {spec.subcategory}
              </p>
            </div>
            <div className="flex gap-2">
              <div
                onClick={() => navigate(`/customer/1/specification/${spec.id}`)}
                passHref
              >
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Review
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveSpecification(spec.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}

        <div className=" flex justify-start gap-8 mt-8">
          <Button
            className="bg-white text-primary border border-primary w-40 hover:text-white hover:bg-primary"
            onClick={() => navigate(`/customer/1/create-specification`)}
          >
            Add more specification
          </Button>
          <Button className="bg-white text-primary border border-primary w-40 hover:text-white hover:bg-primary">
            Finalise
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
