import basicprice from "@/assets/png/basicprice.png";
import premiumprice from "@/assets/png/premiumprice.png";
import platinumprice from "@/assets/png/platinumprice.png";
import { Button } from "@/components/ui/button";
import { Lu<PERSON>he<PERSON> } from "react-icons/lu";
import { Link } from "react-router-dom";

const PricingSection = () => {
  return (
    <div className="mt-20 px-4 sm:px-8 xl:px-32 bg-darkLight py-10">
      <span className="font-nunito font-semibold text-heading6 text-green1 w-full text-center flex justify-center">{`Pricing`}</span>
      <div className="flex flex-col lg:flex-row gap-10 lg:justify-between items-center w-full">
        <div className="w-full mt-9 flex flex-col justify-center items-center">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy leading-[2.875rem] text-center">{`Our Pricing Plans`}</h1>
        </div>
      </div>
      <div className="flex flex-row flex-wrap items-center justify-center gap-4 lg:gap-10 mt-6">
        <div className="shadow-custom-shadow rounded-3xl border py-6 px-8 pb-16">
          <div className="flex items-center gap-3">
            <img
              loading="lazy"
              src={basicprice}
              alt="basic price"
              className="w-[2.296rem] h-[2.296rem]"
            />
            <div>
              <span className="font-poppins text-textSmall text-navy">{`For individual`}</span>
              <h2 className="font-nunito text-heading6 font-bold text-navy">{`Basic`}</h2>
            </div>
          </div>
          <div className="flex gap-3 items-center my-5">
            <span className="font-nunito text-heading4 font-extrabold leading-[2.875rem]">{`NPR 99`}</span>
            <span>{`/month`}</span>
          </div>
          <div className="w-full text-center">
            <Link to="/customer/signup">
              <Button className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-12 rounded-xl tracking-wider">
                Get Started
              </Button>
            </Link>
          </div>
          <div className="mt-5">
            <span className="font-poppins text-textSmall">{`Includes`}</span>
            <div className="grid gap-2 mt-4">
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`All analytics features`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Up to 250,000 tracked visits`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Normal support`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Up to 3 team members`}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="shadow-custom-shadow rounded-3xl border-[3px] border-green2 ">
          <div className="bg-green2 rounded-t-[1.3rem] py-2 text-center font-semibold text-textSmall tracking-wider">
            Most Popular
          </div>
          <div className="py-6 px-8 pb-16">
            <div className="flex items-center gap-3">
              <img
                loading="lazy"
                src={premiumprice}
                alt="premium price"
                className="w-[2.296rem] h-[2.296rem]"
              />
              <div>
                <span className="font-poppins text-textSmall text-navy">{`For startups`}</span>
                <h2 className="font-nunito text-heading6 font-bold text-navy">{`Premium`}</h2>
              </div>
            </div>
            <div className="flex gap-3 items-center my-5">
              <span className="font-nunito text-heading4 font-extrabold leading-[2.875rem]">{`NPR 199`}</span>
              <span>{`/month`}</span>
            </div>
            <div className="w-full text-center">
              <Link to="/customer/signup">
                <Button className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-12 rounded-xl tracking-wider">
                  Get Started
                </Button>
              </Link>
            </div>
            <div className="mt-5">
              <span className="font-poppins text-textSmall">{`Includes`}</span>
              <div className="grid gap-2 mt-4">
                <div className="flex items-center gap-3">
                  <LuCheck className="text-green1" />
                  <span className="font-poppins text-bodySmall">{`All analytics features`}</span>
                </div>
                <div className="flex items-center gap-3">
                  <LuCheck className="text-green1" />
                  <span className="font-poppins text-bodySmall">{`Up to 1,000,000 tracked visits`}</span>
                </div>
                <div className="flex items-center gap-3">
                  <LuCheck className="text-green1" />
                  <span className="font-poppins text-bodySmall">{`Premium support`}</span>
                </div>
                <div className="flex items-center gap-3">
                  <LuCheck className="text-green1" />
                  <span className="font-poppins text-bodySmall">{`Up to 10 team members`}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="shadow-custom-shadow rounded-3xl border py-6 px-8 pb-16">
          <div className="flex items-center gap-3">
            <img
              loading="lazy"
              src={platinumprice}
              alt="platinum price"
              className="w-[2.296rem] h-[2.296rem]"
            />
            <div>
              <span className="font-poppins text-textSmall text-navy">{`For big enterprise`}</span>
              <h2 className="font-nunito text-heading6 font-bold text-navy">{`Platinum`}</h2>
            </div>
          </div>
          <div className="flex gap-3 items-center my-5">
            <span className="font-nunito text-heading4 font-extrabold leading-[2.875rem]">{`NPR 399`}</span>
            <span>{`/month`}</span>
          </div>
          <div className="w-full text-center">
            <Link to="/customer/signup">
              <Button className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-12 rounded-xl tracking-wider">
                Get Started
              </Button>
            </Link>
          </div>
          <div className="mt-5">
            <span className="font-poppins text-textSmall">{`Includes`}</span>
            <div className="grid gap-2 mt-4">
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`All analytics features`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Up to 5,000,000 tracked visits`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Dedicated support`}</span>
              </div>
              <div className="flex items-center gap-3">
                <LuCheck className="text-green1" />
                <span className="font-poppins text-bodySmall">{`Up to 50 team members`}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingSection;
