"use client";

import { useF<PERSON>, Controller } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { useVerifyCustomerKycMutation } from "@/services/customer/mutation";
import { toast } from "@/components/ui/use-toast";
import { Loader } from "lucide-react";

export default function CustomerVerification() {
  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm();

  const { mutate, isPending } = useVerifyCustomerKycMutation();

  const onSubmit = (data) => {
    const formData = new FormData();
    formData.append("_method", "PUT");
    formData.append("name", `${data.firstName} ${data.lastName}`);
    formData.append("citizenship_front", data.citizenship_front);
    formData.append("citizenship_back", data.citizenship_back);
    formData.append("pan", data.pan);
    formData.append("phone", data.phone);

    mutate(formData, {
      onSuccess: () => {
        toast({
          title: "Success",
          description: "KYC verification submitted successfully.",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error.message || "Something went wrong.",
          variant: "destructive",
        });
      }
    });
  };

  return (
    <Card className="w-full max-w-5xl mx-auto mt-6 md:mt-12">
      <CardContent className="p-4 md:p-6">
        <h1 className="text-2xl md:text-3xl font-bold text-center font-inter mb-8">
          Customer Verification
        </h1>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-8">
            <div className="w-full md:w-1/2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                {...register("firstName", {
                  required: "First Name is required",
                })}
              />
              {errors.firstName && (
                <p className="text-red-500">{errors.firstName.message}</p>
              )}
            </div>
            <div className="w-full md:w-1/2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                {...register("lastName", {
                  required: "Last Name is required",
                })}
              />
              {errors.lastName && (
                <p className="text-red-500">{errors.lastName.message}</p>
              )}
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-8">
            <div className="w-full md:w-1/2">
              <Label htmlFor="citizenship_front">Citizenship (Front)</Label>
              <Controller
                name="citizenship_front"
                control={control}
                rules={{ required: "Citizenship (Front) is required" }}
                render={({ field }) => (
                  <Input
                    id="citizenship_front"
                    type="file"
                    onChange={(e) =>
                      field.onChange(e.target.files?.[0] || null)
                    }
                  />
                )}
              />
              {errors.citizenship_front && (
                <p className="text-red-500">
                  {errors.citizenship_front.message}
                </p>
              )}
            </div>

            <div className="w-full md:w-1/2">
              <Label htmlFor="citizenship_back">Citizenship (Back)</Label>
              <Controller
                name="citizenship_back"
                control={control}
                rules={{ required: "Citizenship (Back) is required" }}
                render={({ field }) => (
                  <Input
                    id="citizenship_back"
                    type="file"
                    onChange={(e) =>
                      field.onChange(e.target.files?.[0] || null)
                    }
                  />
                )}
              />
              {errors.citizenship_back && (
                <p className="text-red-500">
                  {errors.citizenship_back.message}
                </p>
              )}
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-8">
            <div className="w-full md:w-1/2">
              <Label htmlFor="pan">PAN Card</Label>
              <Controller
                name="pan"
                control={control}
                rules={{ required: "PAN Card is required" }}
                render={({ field }) => (
                  <Input
                    id="pan"
                    type="file"
                    onChange={(e) =>
                      field.onChange(e.target.files?.[0] || null)
                    }
                  />
                )}
              />
              {errors.pan && (
                <p className="text-red-500">{errors.pan.message}</p>
              )}
            </div>
            <div className="w-full md:w-1/2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                {...register("phone", {
                  required: "Phone Number is required",
                })}
              />
              {errors.phone && (
                <p className="text-red-500">{errors.phone.message}</p>
              )}
            </div>
          </div>
          <Button type="submit" disabled={isPending} className="w-full mt-4">
            {isPending && <Loader className="mr-2 h-4 w-4 animate-spin" />}
            Submit
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
