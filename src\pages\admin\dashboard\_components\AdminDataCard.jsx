import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { LuArrowDownRight, LuArrowUpRight } from "react-icons/lu";

// eslint-disable-next-line react/prop-types
export function AdminDataCard({ title, content, percentage, trend, icon: Icon, description }) {
  const isUp = trend === "Up";

  return (
    <Card className="p-4 font-nunito rounded-lg hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row p-0 w-full justify-between items-start">
        <div className="flex-1">
          <CardTitle className="text-sm text-muted-foreground font-medium">
            {title}
          </CardTitle>
          <CardDescription className="text-2xl mt-2 text-foreground font-bold">
            {content}
          </CardDescription>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        {Icon && (
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon className="w-5 h-5 text-primary" />
          </div>
        )}
      </CardHeader>
      <CardContent className="flex gap-2 items-center text-sm p-0 mt-3">
        {percentage && (
          <>
            {isUp ? (
              <LuArrowUpRight size={16} className="text-green-500" />
            ) : (
              <LuArrowDownRight size={16} className="text-red-500" />
            )}
            <span className={`font-medium ${isUp ? "text-green-500" : "text-red-500"}`}>
              {percentage}%
            </span>
            <span className="text-muted-foreground">from last month</span>
          </>
        )}
      </CardContent>
    </Card>
  );
}
