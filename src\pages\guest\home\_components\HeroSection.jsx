import highlighter from "@/assets/png/Union.png";
import threelineleft from "@/assets/png/threelineleft.png";
import threelineright from "@/assets/png/threelineright.png";
import avatar1 from "@/assets/webp/avatar1.webp";
import avatar2 from "@/assets/webp/avatar2.webp";
import avatar3 from "@/assets/webp/avatar3.webp";
import avatar4 from "@/assets/webp/avatar4.webp";
import whitedivider from "@/assets/png/whitedivider.png";
import { Link } from "react-router-dom";

const HeroSection = () => {
  return (
    <>
      <div className="bg-custom-gradient -mt-24 h-screen min-h-[50rem] max-h-[calc(100svh-20rem)] w-full flex items-center justify-center">
        <div>
          <div className="flex justify-center">
            <div className="flex gap-2">
              <div className="relative">
                <h1 className="font-poppins text-navy text-2xl sm:text-heading3 lg:text-heading2 font-semibold leading-5 relative z-10">{`Quote `}</h1>
                <img
                  loading="lazy"
                  src={highlighter}
                  alt="highlighter"
                  className="w-[5rem] sm:w-[11.239rem] sm:h-[2.183rem] lg:w-[20rem] lg:h-[2.183rem] absolute top-[0.6rem] sm:top-1.5 lg:top-3 left-0 z-0"
                />
              </div>
              <h1 className="font-poppins text-navy text-2xl sm:text-heading3 lg:text-heading2 font-semibold leading-5">{` in a Click`}</h1>
            </div>
          </div>
          <div className="flex flex-col justify-between items-center mt-8 lg:mt-12">
            <h2 className="font-nunito sm:text-xl text-center lg:text-3xl font-medium leading-5 text-blueGray">{`We Build your Quotation`}</h2>
            <p className="font-nunito font-medium text-bodySmall px-4 lg:px-0 max-w-[34rem] text-center lg:text-bodyLarge mt-2 lg:mt-5 lg:leading-6 text-darkTeal">{`"Streamline your quotes effortlessly with our powerful software. Get started today for better business outcomes."`}</p>
          </div>
          <div className="flex items-center gap-4 justify-center mt-14">
            <img
              loading="lazy"
              src={threelineleft}
              alt="highlighter"
              className="h-6 sm:h-8"
            />
            <Link
              to="/customer/signin"
              className="font-nunito text-darkNavy  text-bodyMedium font-extrabold bg-green2 hover:bg-green1 px-8 py-3 lg:px-12 lg:py-4 rounded-xl tracking-wider"
            >
              Get Started
            </Link>
            <img
              loading="lazy"
              src={threelineright}
              alt="highlighter"
              className="h-6 sm:h-8"
            />
          </div>
          <div className="flex flex-col items-center justify-center mt-14 gap-2">
            <div className="flex items-center">
              <img
                loading="lazy"
                src={avatar1}
                alt="highlighter"
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white bg-[#D9D9D9]"
              />
              <img
                loading="lazy"
                src={avatar2}
                alt="highlighter"
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white bg-[#D9D9D9] -ml-4"
              />
              <img
                loading="lazy"
                src={avatar3}
                alt="highlighter"
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white bg-[#D9D9D9] -ml-4"
              />
              <img
                loading="lazy"
                src={avatar4}
                alt="highlighter"
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white bg-[#D9D9D9] -ml-4"
              />
              <div className="bg-[#D9D9D9] w-8 h-8 sm:w-10 sm:h-10 -ml-4 border-2 border-white rounded-full flex items-center justify-center font-medium text-xs">
                <span>{`24+`}</span>
              </div>
            </div>
            <span className="font-nunito font-medium text-textSmall px-4 lg:px-0 max-w-[34rem] text-center lg:text-bodyLarge lg:leading-6 text-darkTeal">{`24+ vendor already joined. Get Started Today!`}</span>
          </div>
        </div>
      </div>
      <div className="w-full h-20 -mt-5">
        <img
          loading="lazy"
          src={whitedivider}
          alt="divider"
          className="w-full h-10"
        />
      </div>
    </>
  );
};

export default HeroSection;
