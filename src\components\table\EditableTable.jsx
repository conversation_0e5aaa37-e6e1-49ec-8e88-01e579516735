import React from "react";
import { fixedFields } from "@/utils/specProductFields";
import { MdDelete } from "react-icons/md";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Command,
  CommandInput,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
} from "@/components/ui/command";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";

import { units } from "@/utils/units";

const EditableTable = ({ data, setData }) => {
  const handleCellChange = (rowIndex, key, value, type) => {
    const processedValue = type === "number" ? Number(value) : value;
    const updatedData = data.map((row, index) =>
      index === rowIndex ? { ...row, [key]: processedValue } : row
    );
    setData(updatedData);
    console.log(updatedData);
  };

  const handleAddRow = () => {
    const emptyRow = {};
    fixedFields.forEach(({ key, type }) => {
      emptyRow[key] = type === "number" ? 0 : "";
    });
    setData([...data, emptyRow]);
  };

  const handleDeleteRow = (rowIndex) => {
    const updatedData = data.filter((_, index) => index !== rowIndex);
    setData(updatedData);
  };

  return (
    <div>
      <button
        onClick={handleAddRow}
        style={{ marginBottom: "10px" }}
        className="text-primary"
      >
        + Add New
      </button>

      <table border="1" cellPadding="5" cellSpacing="0" className="w-full">
        <thead>
          {data.length > 0 && (
            <tr>
              <th className="text-left">S.N.</th>
              {fixedFields.map(({ key, label, subtext }) => (
                <th key={key} className="text-left">
                  {label}
                  {subtext && (
                    <div className="text-xs text-gray-500 font-normal">({subtext})</div>
                  )}
                </th>
              ))}
              <th className="text-left">Actions</th>
            </tr>
          )}
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex}>
              <td>
                <span className="border px-3 py-2 rounded-lg">
                  {rowIndex + 1}
                </span>
              </td>
              {fixedFields.map(({ key, type }) => (
                <td key={key}>
                  {key === "unit" ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="w-full justify-between"
                        >
                          {row[key] || "Select unit"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search unit..." />
                          <CommandList>
                            <CommandEmpty>No unit found.</CommandEmpty>
                            <CommandGroup>
                              {units.map((unit) => (
                                <CommandItem
                                  key={unit}
                                  value={unit}
                                  onSelect={() =>
                                    handleCellChange(
                                      rowIndex,
                                      key,
                                      unit,
                                      "text"
                                    )
                                  }
                                >
                                  {unit}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <input
                      type={type}
                      value={row[key] ?? ""}
                      onChange={(e) =>
                        handleCellChange(rowIndex, key, e.target.value, type)
                      }
                      className="border rounded-lg px-3 py-2 w-full"
                    />
                  )}
                </td>
              ))}
              <td>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={() => handleDeleteRow(rowIndex)}
                      >
                        <MdDelete className="text-red-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete this item</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default EditableTable;
