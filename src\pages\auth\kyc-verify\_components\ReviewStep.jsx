"use client";

import { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

export default function ReviewStep({ formData, onSubmit, onEdit, isLoading = false }) {
  // Helper function to render file information
  const renderFileInfo = (file) => {
    if (file instanceof File) {
      return (
        <div>
          <p className="text-gray-800">{file.name}</p>
          <p className="text-sm text-gray-500">
            Size: {(file.size / 1024).toFixed(2)} KB
          </p>
        </div>
      );
    }
    return <p className="text-gray-800">{file}</p>;
  };

  // Helper function to format field names
  const formatFieldName = (key) => {
    return key
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  // Helper function to format field values
  const formatFieldValue = (key, value) => {
    // Handle business categories
    if (key === 'businessCategory' && Array.isArray(value)) {
      return value.join(', ');
    }
    
    // Handle business category names
    if (key === 'businessCategoryNames' && Array.isArray(value)) {
      return value.join(', ');
    }
    
    // Handle location IDs - show names instead
    if (key === 'province' && formData.companyDetails?.provinceName) {
      return formData.companyDetails.provinceName;
    }
    if (key === 'district' && formData.companyDetails?.districtName) {
      return formData.companyDetails.districtName;
    }
    if (key === 'municipality' && formData.companyDetails?.municipalityName) {
      return formData.companyDetails.municipalityName;
    }
    
    return value;
  };

  // Filter out internal fields that shouldn't be displayed
  const shouldDisplayField = (key) => {
    const hiddenFields = ['businessCategory', 'provinceName', 'districtName', 'municipalityName'];
    return !hiddenFields.includes(key);
  };

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold text-center">Review Your Details</h2>

      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(formData.personalInfo)
            .filter(([key]) => shouldDisplayField(key))
            .map(([key, value]) => (
              <div key={key} className="space-y-1">
                <Label className="text-sm font-medium">
                  {formatFieldName(key)}
                </Label>
                <p className="text-gray-800">{formatFieldValue(key, value)}</p>
              </div>
            ))}
        </CardContent>
      </Card>

      {formData.companyDetails &&
        Object.keys(formData.companyDetails).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Company Details</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(formData.companyDetails)
                .filter(([key]) => shouldDisplayField(key))
                .map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <Label className="text-sm font-medium">
                      {formatFieldName(key)}
                    </Label>
                    <p className="text-gray-800">{formatFieldValue(key, value)}</p>
                  </div>
                ))}
            </CardContent>
          </Card>
        )}

      <Card>
        <CardHeader>
          <CardTitle>Uploaded Documents</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(formData.documents).map(([key, value]) => (
            <div key={key} className="space-y-1">
              <Label className="text-sm font-medium">
                {formatFieldName(key)}
              </Label>
              {renderFileInfo(value)}
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Bank Details</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(formData.bankDetails).map(([key, value]) => (
            <div key={key} className="space-y-1">
              <Label className="text-sm font-medium">
                {formatFieldName(key)}
              </Label>
              <p className="text-gray-800">{value}</p>
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="flex justify-end gap-6 mt-8">
        <Button
          className="w-48"
          type="button"
          variant="outline"
          onClick={onEdit}
        >
          Previous
        </Button>
        <Button className="w-48" onClick={onSubmit} disabled={isLoading}>
          {isLoading ? "Submitting..." : "Submit"}
        </Button>
      </div>
    </div>
  );
}
