import avatar1 from "@/assets/webp/avatar1.webp";

const TestimonialCard = () => {
  return (
    <div className="shadow-custom-shadow rounded-3xl border py-4 px-5 min-w-[25rem]">
      <div className="flex items-center gap-2">
        <img
          src={avatar1}
          alt="highlighter"
          className="w-14 h-14 sm:w-16 sm:h-16 rounded-full border-2 border-white bg-[#D9D9D9]"
          loading="lazy"
        />
        <div>
          <h2 className="font-poppins font-medium text-bodyLarge leading-4">{`<PERSON>`}</h2>
          <span className="font-poppins text-textSmall text-darkTeal text-opacity-80">{`Vendor`}</span>
        </div>
      </div>
      <span>
        {`I can’t believe the impact zettabid has had on my business. It’s like having a personal sales expert right at my fingerprints. My sales have skyrocketed.`}
      </span>
    </div>
  );
};

export default TestimonialCard;
