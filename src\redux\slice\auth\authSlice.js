import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  user: {
    email: "",
    userId: "",
    address: "",
    profile: "",
    userType: "",
    name: "",
    isLogin: false,
  },
  token: {
    accessToken: "",
    refreshToken: "",
  },
  setup: {
    isSetup: false,
    step: 1,
    customerOrVendor: "customer",
    organizationOrIndividual: "individual",

    // basic details
    companyName: "",
    companyRepresentative: "",
    email: "",
    phoneNumber: "",

    // contact info
    province: "",
    district: "",
    municipality: "",
    streetAddress: "",

    // business info
    businessCategory: "",
    website: "",
    companyRegistration: "",
    vatOrPan: "",

    // additional info
    taxClearance: "",
    logo: "",
    additionalDocument: "",
    companyStamp: "",
    authorizedSignature: "",
  },
};

export const authSlice = createSlice({
  name: "authUser",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setToken: (state, action) => {
      state.token = action.payload;
    },
    setInitialUser: (state) => {
      state.user = initialState.user;
    },
    setInitialToken: (state) => {
      state.token = initialState.token;
    },
    setInitialAuth: (state) => {
      state.user = initialState.user;
      state.token = initialState.token;
    },
    setSetup: (state, action) => {
      state.setup = action.payload;
    },
    setInitialSetup: (state) => {
      state.setup = initialState.setup;
    },
  },
});

export const {
  setUser,
  setToken,
  setSetup,
  setInitialUser,
  setInitialToken,
  setInitialAuth,
  setInitialSetup,
} = authSlice.actions;
export default authSlice.reducer;
