import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Home, LogIn } from "lucide-react";
import PropTypes from "prop-types";

const UnauthorizedAccess = ({ 
  title = "Access Denied", 
  message = "You don't have permission to access this page.",
  redirectPath = "/",
  showLoginButton = true,
  userType = null 
}) => {
  const navigate = useNavigate();

  useEffect(() => {
    // Log the unauthorized access attempt for security monitoring
    console.warn("Unauthorized access attempt:", {
      timestamp: new Date().toISOString(),
      attemptedPath: window.location.pathname,
      userAgent: navigator.userAgent,
    });
  }, []);

  const handleGoHome = () => {
    navigate(redirectPath);
  };

  const handleLogin = () => {
    if (userType) {
      navigate(`/${userType}/signin`);
    } else {
      navigate("/");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">{title}</CardTitle>
          <CardDescription className="text-gray-600">
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-500 text-center">
            If you believe this is an error, please contact support or try logging in again.
          </div>
          <div className="flex flex-col space-y-2">
            <Button onClick={handleGoHome} variant="outline" className="w-full">
              <Home className="mr-2 h-4 w-4" />
              Go to Homepage
            </Button>
            {showLoginButton && (
              <Button onClick={handleLogin} className="w-full">
                <LogIn className="mr-2 h-4 w-4" />
                Login
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

UnauthorizedAccess.propTypes = {
  title: PropTypes.string,
  message: PropTypes.string,
  redirectPath: PropTypes.string,
  showLoginButton: PropTypes.bool,
  userType: PropTypes.oneOf(["customer", "vendor", "admin"]),
};

export default UnauthorizedAccess;
