import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { 
  getAuthenticationStatusForUserType, 
  clearAuthData, 
  getUserTypeFromRoute 
} from "@/utils/authUtils";
import { 
  getCookiesByUserType, 
  isAuthenticatedByUserType,
  getAccessTokenByUserType,
  getUserIdByUserType 
} from "@/utils/setCookies";

/**
 * Customer-specific authentication hook
 * Manages authentication state and operations for customer users
 */
export const useCustomerAuth = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);

  // Check authentication status on mount and when cookies change
  useEffect(() => {
    checkAuthStatus();
    
    // Set up interval to check auth status periodically
    const interval = setInterval(checkAuthStatus, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const checkAuthStatus = () => {
    try {
      const authStatus = getAuthenticationStatusForUserType("user"); // customer type is "user"
      setIsAuthenticated(authStatus.isAuthenticated);
      
      if (authStatus.isAuthenticated) {
        const cookies = getCookiesByUserType("user");
        setUser({
          userId: authStatus.userId,
          userType: authStatus.userType,
          email: cookies.email,
          name: cookies.name,
          isLogin: true,
          dashboardPath: authStatus.dashboardPath,
        });
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Error checking customer auth status:", error);
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const login = async (credentials, loginFunction) => {
    setLoading(true);
    try {
      const response = await loginFunction(credentials);
      
      if (!response.success) {
        toast({
          variant: "destructive",
          title: "Login Failed",
          description: "Invalid credentials. Please try again.",
          duration: 3000,
        });
        return response;
      }

      // Refresh auth status after successful login
      setTimeout(checkAuthStatus, 100);
      
      toast({
        title: "Login Successful",
        description: "Welcome back!",
        duration: 2000,
      });

      return response;
    } catch (error) {
      console.error("Customer login error:", error);
      toast({
        title: "Login Failed",
        description: error.message || "An error occurred during login",
        variant: "destructive",
        duration: 3000,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    try {
      // Clear only customer authentication data
      clearAuthData("user");
      
      // Update state
      setIsAuthenticated(false);
      setUser(null);
      
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
        duration: 2000,
      });

      // Redirect to customer login
      setTimeout(() => {
        window.location.href = "/customer/signin";
      }, 1000);
    } catch (error) {
      console.error("Customer logout error:", error);
      toast({
        title: "Logout Error",
        description: "An error occurred during logout",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const register = async (userData, registerFunction) => {
    setLoading(true);
    try {
      const response = await registerFunction(userData);
      
      if (!response.success) {
        toast({
          variant: "destructive",
          title: "Registration Failed",
          description: response?.errors?.email || "Registration failed. Please try again.",
          duration: 3000,
        });
        return response;
      }

      // Refresh auth status after successful registration
      setTimeout(checkAuthStatus, 100);
      
      toast({
        title: "Registration Successful",
        description: "Welcome! Your account has been created.",
        duration: 2000,
      });

      return response;
    } catch (error) {
      console.error("Customer registration error:", error);
      toast({
        title: "Registration Failed",
        description: error.message || "An error occurred during registration",
        variant: "destructive",
        duration: 3000,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Utility functions
  const getAccessToken = () => getAccessTokenByUserType("user");
  const getUserId = () => getUserIdByUserType("user");
  const isCurrentRoute = () => {
    const currentPath = window.location.pathname;
    return currentPath.startsWith("/customer/");
  };

  return {
    // State
    isAuthenticated,
    user,
    loading,
    
    // Actions
    login,
    logout,
    register,
    checkAuthStatus,
    
    // Utilities
    getAccessToken,
    getUserId,
    isCurrentRoute,
  };
};

export default useCustomerAuth;
