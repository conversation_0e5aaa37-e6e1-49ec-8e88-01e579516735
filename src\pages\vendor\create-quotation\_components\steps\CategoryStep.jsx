import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { categories } from '@/mock-data/spec-mock-data'
import { useGetBusinessCategory } from '@/services/customer/query';



export function CategoryStep({ currentSpec, onCategoryChange }) {

  const { data, isLoading } = useGetBusinessCategory();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Select Category</CardTitle>
        <CardDescription>Choose a category for your specification</CardDescription>
      </CardHeader>
      <CardContent>
        <Select
          value={currentSpec.category}
          onValueChange={onCategoryChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {data?.business_categories?.length > 0 && data.business_categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  )
}

