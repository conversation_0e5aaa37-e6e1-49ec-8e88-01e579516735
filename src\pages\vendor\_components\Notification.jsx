import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { FaRegBell } from "react-icons/fa";
import { GoDotFill } from "react-icons/go";
import useIsMobile from "@/hooks/useIsMobile";

const Notification = () => {
  const { isMobile } = useIsMobile();

  const handleMarkAllAsRead = () => {
    console.log("mark as read");
  };

  return (
    <Popover>
      <PopoverTrigger>
        <div className="relative">
          <FaRegBell className="w-5 h-5" />
          {/* <div className="absolute w-2 h-2 top-0 right-0.5 bg-schemes-dangerText rounded-full"></div> */}
          <GoDotFill className="absolute w-4 h-4 -top-1 -right-0.5 text-schemes-dangerText" />
        </div>
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align={isMobile ? "center" : "end"}
        className="pb-0 w-[20rem] sm:w-[30rem] border-0 shadow-custom rounded-xl"
      >
        <div>
          <div className="flex justify-between items-center">
            <h1>Notification</h1>
            <Button
              className="!py-0 px-0 bg-white hover:bg-white text-schemes-mutedText hover:text-black/80 !text-bodySmall"
              size="sm"
              onClick={handleMarkAllAsRead}
            >
              Mark all as read
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default Notification;
