import {
  <PERSON><PERSON><PERSON>rum<PERSON>,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useGetVendorProfileQuery } from "@/services/auth/query";
import { useGetSpecifications } from "@/services/vendor/query";
import { Dialog } from "@radix-ui/react-dialog";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import SpecificationFeedCard from "./_components/SpecificationFeedCard";

const Feeds = () => {
  const { data: vendorData } = useGetVendorProfileQuery();
  const { data: specificationsData, isLoading: specificationsLoading, error: specificationsError } = useGetSpecifications();
  const [showKycDialog, setShowKycDialog] = useState(false);
  console.log(vendorData?.vendor_details?.kyc_status);

  useEffect(() => {
    if (vendorData?.vendor_details?.kyc_status === "under_review") {
      setShowKycDialog(true);
    }
  }, [vendorData?.vendor_details?.kyc_status]);

  // Mock data for fallback when API is not available
  const mockSpecifications = [
    {
      id: 1,
      title: "Custom Steel Brackets Manufacturing",
      description: "Need 500 units of custom steel brackets with specific dimensions and coating requirements.",
      category: { name: "Manufacturing" }, // QuotationFormModal expects category.name
      subCategory: "Metal Fabrication",
      sub_category: "Metal Fabrication", // QuotationFormModal compatibility
      status: "open",
      createdAt: "2024-01-15T00:00:00Z",
      template_id: 1,
      items_count: 3,
      customer_name: "ABC Manufacturing Co.",
      customer_avatar: "https://github.com/shadcn.png",
      customer_email: "<EMAIL>",
      customer_phone: "+977-9812345678",
      customer_address: "Industrial Area, Kathmandu, Nepal",
      created_at: "2024-01-15T00:00:00Z", // Add this for QuotationFormModal compatibility
      specificationProductData: [
        {
          itemName: "Steel Bracket Type A",
          quantity: 200,
          unit: "pieces",
          attributes: "Galvanized coating, 10mm thickness",
          other: "High strength steel required",
        },
        {
          itemName: "Steel Bracket Type B",
          quantity: 300,
          unit: "pieces",
          attributes: "Powder coating, 8mm thickness",
          other: "Standard grade steel",
        },
      ],
    },
    {
      id: 2,
      title: "Electronic Component Assembly",
      description: "Looking for a vendor to assemble 1000 electronic control units according to provided specifications.",
      category: { name: "Electronics" }, // QuotationFormModal expects category.name
      subCategory: "PCB Assembly",
      sub_category: "PCB Assembly", // QuotationFormModal compatibility
      status: "pending",
      createdAt: "2024-01-14T00:00:00Z",
      template_id: 2,
      items_count: 5,
      customer_name: "TechCorp Industries",
      customer_avatar: "https://via.placeholder.com/150",
      customer_email: "<EMAIL>",
      customer_phone: "+977-9876543210",
      customer_address: "Tech Park, Lalitpur, Nepal",
      created_at: "2024-01-14T00:00:00Z", // Add this for QuotationFormModal compatibility
      specificationProductData: [
        {
          itemName: "Control Unit PCB",
          quantity: 1000,
          unit: "pieces",
          attributes: "SMD components, lead-free soldering",
          other: "RoHS compliant",
        },
      ],
    },
  ];

  // Transform specifications data for display
  const transformSpecificationsForFeed = (apiData) => {
    if (!apiData?.data || !Array.isArray(apiData.data)) {
      return mockSpecifications; // Return mock data if API data is not available
    }

    return apiData.data.map((spec) => ({
      id: spec.id,
      title: spec.title || `Specification #${spec.id}`,
      description: spec.description || "",
      // Keep both formats for compatibility
      category: typeof spec.category === "object" && spec.category?.name
        ? { name: spec.category.name } // QuotationFormModal expects category.name
        : { name: spec.category || "" },
      subCategory: typeof spec.sub_category === "object" && spec.sub_category?.name
        ? spec.sub_category.name
        : spec.sub_category || "",
      sub_category: typeof spec.sub_category === "object" && spec.sub_category?.name
        ? spec.sub_category.name
        : spec.sub_category || "",
      status: spec.status || "pending",
      createdAt: spec.created_at || "",
      created_at: spec.created_at || "", // QuotationFormModal expects created_at
      template_id: spec.template_id || 1,
      items_count: spec.items_count || 0,
      customer_name: spec.customer?.name || "Customer",
      customer_avatar: spec.customer?.avatar || "https://github.com/shadcn.png",
      customer_email: spec.customer?.email || "<EMAIL>",
      customer_phone: spec.customer?.phone || "+977-9812343435",
      customer_address: spec.customer?.address || "Kathmandu, Nepal",
      specificationProductData: Array.isArray(spec.items)
        ? spec.items.map((item) => ({
            itemName: item?.item_name || "",
            quantity: parseFloat(item?.quantity) || 0,
            unit: item?.unit || "",
            attributes: item?.specifications || "",
            other: item?.other || "",
          }))
        : [],
    }));
  };

  const specifications = transformSpecificationsForFeed(specificationsData);

  // Debug logging
  console.log("Vendor Feeds Debug:", {
    specificationsData,
    specificationsLoading,
    specificationsError,
    transformedSpecifications: specifications,
  });

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href={`#`}>Feeds</BreadcrumbLink>
              </BreadcrumbItem>
              {/* <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Data Fetching</BreadcrumbPage>
              </BreadcrumbItem> */}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="p-4 pt-0">
        <div className="mt-10 grid gap-4">
          {specificationsLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              Loading specifications...
            </div>
          ) : specificationsError ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-4">
                Error loading specifications: {specificationsError.message}
              </div>
              <div className="text-gray-600 text-sm">
                Showing mock data for demonstration
              </div>
              {specifications.map((spec) => (
                <SpecificationFeedCard
                  key={spec.id}
                  specification={spec}
                />
              ))}
            </div>
          ) : specifications.length > 0 ? (
            specifications.map((spec) => (
              <SpecificationFeedCard
                key={spec.id}
                specification={spec}
              />
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              No specifications available
            </div>
          )}
        </div>
      </div>
      <Dialog open={showKycDialog} onOpenChange={setShowKycDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>KYC Verification Required</DialogTitle>
            <DialogDescription>
              Your KYC verification is currently pending. Please complete your
              KYC profile to access all features.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowKycDialog(false)}>
              Skip for Now
            </Button>
            <Button asChild>
              <Link to="/kyc-verify">Update KYC</Link>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Feeds;
