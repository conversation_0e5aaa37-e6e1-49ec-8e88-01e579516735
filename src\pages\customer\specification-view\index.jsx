import React from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Printer, Download } from "lucide-react";
import SpecTemplate1Modified from "../create-specification/_components/templates/SpecTemplate1Modified";
import SpecTemplate2Modified from "../create-specification/_components/templates/SpecTemplate2Modified";
import SpecTemplate3Modified from "../create-specification/_components/templates/SpecTemplate3Modified";
import { useGetSpecificationByIdQuery } from "@/services/customer/query";

const templates = [
  { id: 1, name: "Simple", component: SpecTemplate1Modified },
  { id: 2, name: "Professional", component: SpecTemplate2Modified },
  { id: 3, name: "Modern", component: SpecTemplate3Modified },
];

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    // Handle category/subcategory objects with name property
    if (value.name) return String(value.name);
    // Handle other objects by converting to JSON
    return JSON.stringify(value);
  }
  return String(value);
};

// Transform API response to match template component expectations
const transformSpecificationData = (apiData) => {
  console.log("Raw API data:", apiData);

  if (!apiData) {
    console.log("No API data received");
    return null;
  }

  // Handle case where apiData might be an array (from the list endpoint)
  const specData = Array.isArray(apiData) ? apiData[0] : apiData;

  if (!specData) {
    console.log("No specification data found");
    return null;
  }

  console.log("Processing spec data:", specData);

  const transformed = {
    id: specData.id || null,
    title: specData.title || "",
    description: specData.description || "",
    type: specData.type || "",
    // Handle category - it might be an object or a string
    category:
      typeof specData.category === "object" && specData.category?.name
        ? specData.category.name
        : specData.category || "",
    // Handle sub_category - it might be an object or a string
    subCategory:
      typeof specData.sub_category === "object" && specData.sub_category?.name
        ? specData.sub_category.name
        : specData.sub_category || "",
    specificationType: specData.type || "",
    createdAt: specData.created_at || "",
    template_id: specData.template_id || 1,
    status: specData.status || "",
    // Transform items to match the expected specificationProductData format
    specificationProductData: Array.isArray(specData.items)
      ? specData.items.map((item) => ({
          itemName: item?.item_name || "",
          quantity: parseFloat(item?.quantity) || 0,
          unit: item?.unit || "",
          attributes: item?.specifications || "",
          other: item?.other || "",
        }))
      : [],
  };

  console.log("Transformed data:", transformed);
  return transformed;
};

export default function SpecificationView() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Use the API hook to fetch specification data
  const {
    data: apiResponse,
    isLoading: loading,
    error,
    isError,
  } = useGetSpecificationByIdQuery(id);

  console.log("Full API Response:", apiResponse);
  console.log("API Response Data:", apiResponse?.data);
  console.log("Loading:", loading);
  console.log("Error:", error);
  console.log("Is Error:", isError);

  // Transform the API response data
  const specification = transformSpecificationData(apiResponse?.data);

  const handlePrint = () => {
    window.print();
  };

  // Find the template component based on the template_id
  const selectedTemplate = templates.find(
    (template) => template.id === specification?.template_id
  );
  const TemplateComponent = selectedTemplate?.component;

  console.log("Current specification:", specification);
  console.log("Template ID:", specification?.template_id);
  console.log("Selected template:", selectedTemplate);
  console.log("Template component:", TemplateComponent);

  if (loading) {
    return <div className="p-6 text-center">Loading specification...</div>;
  }

  if (isError) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4 text-red-600">
          Error loading specification
        </h2>
        <p className="text-gray-600 mb-4">
          {error?.message || "Something went wrong"}
        </p>
        <Button onClick={() => navigate("/customer/1/view-all-specification")}>
          Back to Specifications
        </Button>
      </div>
    );
  }

  if (!specification) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4">Specification not found</h2>
        <Button onClick={() => navigate("/customer/1/view-all-specification")}>
          Back to Specifications
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/customer/1/view-all-specification")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-semibold">
              {safeRender(
                specification?.title,
                `Specification #${specification?.id || id}`
              )}
            </h1>
            {specification?.description && (
              <p className="text-gray-600 mt-1">
                {safeRender(specification.description)}
              </p>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6 shadow-md">
        <div className="mb-6">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="font-medium">{safeRender(specification?.type)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <p className="font-medium capitalize">
                {safeRender(specification?.status)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Category</p>
              <p className="font-medium">
                {safeRender(specification?.category)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Sub Category</p>
              <p className="font-medium">
                {safeRender(specification?.subCategory)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date Created</p>
              <p className="font-medium">
                {specification?.createdAt
                  ? (() => {
                      try {
                        return new Date(
                          specification.createdAt
                        ).toLocaleDateString();
                      } catch (e) {
                        return specification.createdAt;
                      }
                    })()
                  : "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Template</p>
              <p className="font-medium">
                {safeRender(
                  selectedTemplate?.name,
                  `Template ${specification?.template_id || 1}`
                )}
              </p>
            </div>
          </div>
        </div>

        <div className="print:mt-8">
          {TemplateComponent && specification ? (
            <>
              {console.log("Passing data to template:", specification)}
              <TemplateComponent data={specification} />
            </>
          ) : (
            <div className="text-center text-red-500">
              {!specification
                ? "Specification data not available"
                : `Template not found for ID: ${
                    specification?.template_id || "unknown"
                  }`}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
