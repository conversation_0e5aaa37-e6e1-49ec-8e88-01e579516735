import React from 'react';
import { useGetAllSpecificationsQuery, useGetSpecificationByIdQuery } from '@/services/customer/query';

export default function TestAPI() {
  const { data: allSpecs, isLoading: loadingAll, error: errorAll } = useGetAllSpecificationsQuery();
  const { data: singleSpec, isLoading: loadingSingle, error: errorSingle } = useGetSpecificationByIdQuery(3);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">API Test Page</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">All Specifications</h2>
        {loadingAll && <p>Loading all specifications...</p>}
        {errorAll && <p className="text-red-500">Error: {errorAll.message}</p>}
        {allSpecs && (
          <div>
            <p className="mb-2">Success: {allSpecs.success ? 'true' : 'false'}</p>
            <p className="mb-2">Status Code: {allSpecs.status_code}</p>
            <p className="mb-2">Message: {allSpecs.message}</p>
            <p className="mb-2">Data Length: {allSpecs.data?.length || 0}</p>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-64">
              {JSON.stringify(allSpecs, null, 2)}
            </pre>
          </div>
        )}
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Single Specification (ID: 3)</h2>
        {loadingSingle && <p>Loading single specification...</p>}
        {errorSingle && <p className="text-red-500">Error: {errorSingle.message}</p>}
        {singleSpec && (
          <div>
            <p className="mb-2">Success: {singleSpec.success ? 'true' : 'false'}</p>
            <p className="mb-2">Status Code: {singleSpec.status_code}</p>
            <p className="mb-2">Message: {singleSpec.message}</p>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-64">
              {JSON.stringify(singleSpec, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
