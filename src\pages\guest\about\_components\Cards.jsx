import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import React from "react";

const Cards = ({ data }) => {
  const { icon: Icon, title, desc } = data;
  return (
    <Card className="flex flex-col items-center justify-center p-4">
      <div className="h-16 w-16 bg-primary rounded-full flex justify-center items-center text-white">
        <Icon size={35} />
      </div>
      <CardHeader>
        <CardTitle className="font-nunito font-extrabold text-navy">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="font-nunito text-bodyLarge text-center text-darkTeal">
        {desc}
      </CardContent>
    </Card>
  );
};

export default Cards;
