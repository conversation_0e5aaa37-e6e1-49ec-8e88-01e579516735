import { useCallback } from 'react'
import * as XLSX from 'xlsx'

// Helper function to format date for CSV
const formatDateForCSV = (dateString) => {
    if (!dateString) return ''
    try {
        const date = new Date(dateString)
        // Use shorter format: MM/DD/YYYY
        return date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        })
    } catch (error) {
        return dateString
    }
}

// Simple CSV export function
export function useCSVExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        try {
            // Simple CSV headers
            const headers = ['Transaction ID', 'Specification', 'Amount', 'User Role', 'PO Number', 'Status', 'Date', 'Vendor', 'Delivery Time']

            // Convert data to CSV rows
            const rows = transactions.map(transaction => [
                transaction.id || '',
                transaction.specification || '',
                `Rs ${(transaction.amount || 0).toLocaleString()}`,
                transaction.userRole || '',
                transaction.poNumber || '',
                transaction.status || '',
                formatDateForCSV(transaction.date),
                transaction.details?.vendor || '',
                transaction.details?.deliveryTime || ''
            ])

            // Create CSV content with BOM for better Excel compatibility
            let csvContent = '\uFEFF' + headers.join(',') + '\n'
            rows.forEach(row => {
                const escapedRow = row.map(cell => {
                    const cellStr = String(cell)
                    if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                        return `"${cellStr.replace(/"/g, '""')}"`
                    }
                    return cellStr
                })
                csvContent += escapedRow.join(',') + '\n'
            })

            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
            const link = document.createElement('a')
            const url = URL.createObjectURL(blob)
            link.setAttribute('href', url)
            link.setAttribute('download', `${fileName}.csv`)
            link.style.visibility = 'hidden'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(url)

            return true
        } catch (error) {
            console.error('CSV Export Error:', error)
            return false
        }
    }, [])
}

// Simple Excel export function
export function useExcelExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        try {
            // Prepare data for Excel
            const data = transactions.map(transaction => ({
                'Transaction ID': transaction.id || '',
                'Specification': transaction.specification || '',
                'Amount': `Rs ${(transaction.amount || 0).toLocaleString()}`,
                'User Role': transaction.userRole || '',
                'PO Number': transaction.poNumber || '',
                'Status': transaction.status || '',
                'Date': formatDateForCSV(transaction.date),
                'Vendor': transaction.details?.vendor || '',
                'Delivery Time': transaction.details?.deliveryTime || ''
            }))

            // Create worksheet
            const worksheet = XLSX.utils.json_to_sheet(data)

            // Set column widths
            worksheet['!cols'] = [
                { wch: 15 }, // Transaction ID
                { wch: 30 }, // Specification
                { wch: 15 }, // Amount
                { wch: 12 }, // User Role
                { wch: 15 }, // PO Number
                { wch: 10 }, // Status
                { wch: 15 }, // Date
                { wch: 25 }, // Vendor
                { wch: 15 }, // Delivery Time
            ]

            // Create workbook and add worksheet
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Transactions')

            // Save file
            XLSX.writeFile(workbook, `${fileName}.xlsx`)

            return true
        } catch (error) {
            console.error('Excel Export Error:', error)
            return false
        }
    }, [])
}

// Simple PDF export function (basic implementation)
export function usePDFExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        try {
            // For now, let's create a simple HTML table and print it
            // This is a fallback approach that should work

            const headers = ['Transaction ID', 'Specification', 'Amount', 'User Role', 'PO Number', 'Status', 'Date', 'Vendor', 'Delivery Time']

            let htmlContent = `
                <html>
                <head>
                    <title>Transaction Report</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #333; }
                        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; font-weight: bold; }
                        tr:nth-child(even) { background-color: #f9f9f9; }
                    </style>
                </head>
                <body>
                    <h1>Transaction Report</h1>
                    <p>Generated on: ${new Date().toLocaleDateString()}</p>
                    <table>
                        <thead>
                            <tr>
                                ${headers.map(header => `<th>${header}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
            `

            transactions.forEach(transaction => {
                htmlContent += `
                    <tr>
                        <td>${transaction.id || ''}</td>
                        <td>${transaction.specification || ''}</td>
                        <td>Rs ${(transaction.amount || 0).toLocaleString()}</td>
                        <td>${transaction.userRole || ''}</td>
                        <td>${transaction.poNumber || ''}</td>
                        <td>${transaction.status || ''}</td>
                        <td>${formatDateForCSV(transaction.date)}</td>
                        <td>${transaction.details?.vendor || ''}</td>
                        <td>${transaction.details?.deliveryTime || ''}</td>
                    </tr>
                `
            })

            htmlContent += `
                        </tbody>
                    </table>
                </body>
                </html>
            `

            // Open in new window for printing/saving as PDF
            const printWindow = window.open('', '_blank')
            printWindow.document.write(htmlContent)
            printWindow.document.close()
            printWindow.focus()

            // Trigger print dialog
            setTimeout(() => {
                printWindow.print()
            }, 500)

            return true
        } catch (error) {
            console.error('PDF Export Error:', error)
            return false
        }
    }, [])
}
