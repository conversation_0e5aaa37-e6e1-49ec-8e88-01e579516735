import { apiRequest } from "./apiRequest";
import { getAuthenticationStatus, getUserTypeFromRoute } from "./authUtils";
import { getAccessTokenByUserType } from "./setCookies";

/**
 * Enhanced API request utility with additional security validations
 * Validates user permissions before making API calls
 */
export async function secureApiRequest({
  url,
  method = "GET",
  headers = {},
  body = null,
  params = {},
  options = {},
  isFormData = false,
  requiresAuth = true,
  allowedUserTypes = [], // Array of allowed user types: ['user', 'vendor', 'admin']
  validateUserId = false, // Whether to validate user ID in the request
  userIdField = "userId", // Field name in body/params that contains user ID
}) {
  try {
    // Skip security checks if authentication is not required
    if (!requiresAuth) {
      return await apiRequest({
        url,
        method,
        headers,
        body,
        params,
        options,
        isFormData,
      });
    }

    // Get authentication status
    const authStatus = getAuthenticationStatus();
    
    if (!authStatus.isAuthenticated) {
      throw new Error("Authentication required. Please log in.");
    }

    // Validate user type if specified
    if (allowedUserTypes.length > 0 && !allowedUserTypes.includes(authStatus.userType)) {
      throw new Error("Access denied. Insufficient permissions.");
    }

    // Validate user ID if required
    if (validateUserId) {
      const requestUserId = body?.[userIdField] || params?.[userIdField];
      if (requestUserId && requestUserId !== authStatus.userId) {
        throw new Error("Access denied. You can only access your own data.");
      }
    }

    // Add authentication headers
    const userType = getUserTypeFromRoute(url) || authStatus.userType;
    const accessToken = getAccessTokenByUserType(userType);
    const secureHeaders = {
      ...headers,
      Authorization: `Bearer ${accessToken}`,
    };

    // Make the secure API request
    return await apiRequest({
      url,
      method,
      headers: secureHeaders,
      body,
      params,
      options,
      isFormData,
    });

  } catch (error) {
    // Log security violations for monitoring
    if (error.message.includes("Access denied") || error.message.includes("Authentication required")) {
      console.warn("Security violation detected:", {
        timestamp: new Date().toISOString(),
        url,
        method,
        error: error.message,
        userAgent: navigator.userAgent,
      });
    }
    
    throw error;
  }
}

/**
 * Utility function to validate if current user can access a specific user's data
 * @param {string} targetUserId - The user ID being accessed
 * @param {string} requiredUserType - Required user type for access
 * @returns {boolean} Whether access is allowed
 */
export function validateUserAccess(targetUserId, requiredUserType = null) {
  const authStatus = getAuthenticationStatus();
  
  if (!authStatus.isAuthenticated) {
    return false;
  }

  // Check user type if specified
  if (requiredUserType && authStatus.userType !== requiredUserType) {
    return false;
  }

  // Check if user is accessing their own data
  if (targetUserId && authStatus.userId !== targetUserId) {
    return false;
  }

  return true;
}

/**
 * Utility function to validate route access based on URL parameters
 * @param {Object} params - URL parameters from useParams()
 * @param {string} routeType - Type of route (customer, vendor, admin)
 * @returns {Object} Validation result with isValid and error message
 */
export function validateRouteAccess(params, routeType) {
  const authStatus = getAuthenticationStatus();
  
  if (!authStatus.isAuthenticated) {
    return {
      isValid: false,
      error: "Authentication required",
      redirectTo: `/${routeType}/signin`,
    };
  }

  // Map route types to expected user types
  const routeUserTypeMap = {
    customer: "user",
    vendor: "vendor", 
    admin: "admin",
  };

  const expectedUserType = routeUserTypeMap[routeType];
  if (authStatus.userType !== expectedUserType) {
    return {
      isValid: false,
      error: "Access denied. Insufficient permissions.",
      redirectTo: authStatus.dashboardPath,
    };
  }

  // Validate user ID in URL
  const userIdParam = `${routeType}Id`;
  const urlUserId = params[userIdParam];
  
  if (urlUserId && authStatus.userId !== urlUserId) {
    return {
      isValid: false,
      error: "Access denied. You can only access your own account.",
      redirectTo: authStatus.dashboardPath,
    };
  }

  return {
    isValid: true,
    error: null,
    redirectTo: null,
  };
}

/**
 * Hook to validate API endpoints based on user permissions
 * @param {string} endpoint - API endpoint being accessed
 * @param {string} method - HTTP method
 * @returns {boolean} Whether the request is allowed
 */
export function validateApiEndpoint(endpoint, method = "GET") {
  const authStatus = getAuthenticationStatus();
  
  if (!authStatus.isAuthenticated) {
    return false;
  }

  // Define endpoint access rules
  const accessRules = {
    "/customer/": ["user"],
    "/vendor/": ["vendor"],
    "/admin/": ["admin"],
  };

  // Check if endpoint requires specific user type
  for (const [pattern, allowedTypes] of Object.entries(accessRules)) {
    if (endpoint.includes(pattern)) {
      return allowedTypes.includes(authStatus.userType);
    }
  }

  // Default: allow access if no specific rules apply
  return true;
}
