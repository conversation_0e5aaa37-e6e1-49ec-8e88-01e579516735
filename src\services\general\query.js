import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { getDistrictByProvince, getFaqs, getMunicipalityByDistrict, getProvince } from "./api";


export function useGetProvinceData() {
  return useSuspenseQuery({
    queryKey: ["getProvinceData"],
    queryFn: () => getProvince(),
  });
}

export function useDistrictQuery(provinceId) {
    return useQuery({
      queryKey: ['districts', provinceId],
      queryFn: () => getDistrictByProvince(provinceId),
      enabled: !!provinceId,
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    });
  }


  export function useMunicipalityQuery(districtId) {
    return useQuery({
      queryKey: ['municipalities', districtId],
      queryFn: () => getMunicipalityByDistrict(districtId),
      enabled: !!districtId,
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    });
  }

export function useGetFaqs() {
  return useQuery({
    queryKey: ["getFaqs"],
    queryFn: () => getFaqs(),
  });
}