"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import ProgressIndicator from "./_components/ProgressIndicator";
import CompanyDetailsForm from "./_components/CompanyDetailsForm";
import DocumentUploadForm from "./_components/DocumentUploadForm";
import BankDetailsForm from "./_components/BankDetailsForm";
import PersonalInfoForm from "./_components/PersonalInfoForm";
import ReviewStep from "./_components/ReviewStep"; // Import the new component
import { useKycVerificationRequestMutation } from "@/services/vendor/mutation";

export default function KYCForm() {
  const formatFormDataForBackend = (data) => {
    const { personalInfo, companyDetails, documents, bankDetails } = data;

    return {
      name: companyDetails.companyName,
      logo: documents.logo, // Company logo
      email: personalInfo.email,
      phone: personalInfo.phoneNumber,
      address: personalInfo.address,
      company_representative: `${personalInfo.firstName} ${personalInfo.lastName}`,
      province: companyDetails.province,
      district: companyDetails.district,
      municipality: companyDetails.municipality,
      ward_no: companyDetails.ward,
      tole: companyDetails.tole,
      street_address: companyDetails.streetAddress,
      business_type: personalInfo.companyType,
      business_category_ids: personalInfo.businessCategory, // Array of business category IDs
      company_registration: documents.idProof, // File
      audit_report: documents.audit, // File
      signature: documents.sign, // File
      bank_name: bankDetails.bankName,
      bank_account: bankDetails.accountNumber,
      account_holder_name: bankDetails.accountHolderName,
      bank_branch: bankDetails.bankBranch,
      vat_pan: documents.vat, // File
      tax_clearance: documents.tax, // File
      website: companyDetails.website || documents.website, // Optional string
    };
  };

  const [currentStep, setCurrentStep] = useState(0);
  const { mutate, isLoading, isError, isSuccess, error } =
    useKycVerificationRequestMutation();
  const [formData, setFormData] = useState({
    personalInfo: {},
    companyDetails: {},
    documents: {},
    bankDetails: {},
  });

  // Check if company type is selected
  const requiresCompanyDetails = [
    "private",
    "semi-government",
    "government",
  ].includes(formData.personalInfo.companyType);

  // Define steps dynamically
  const steps = [
    "Owner Information",
    "Company Details",
    "Document Upload",
    "Bank Details",
    "Review", // Add a new step for review
  ];

  const handleNext = (data) => {
    setFormData((prev) => ({
      ...prev,
      ...data,
    }));

    setCurrentStep((prev) => prev + 1);
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleSubmit = () => {
    console.log("Final form data submitted:", formData);
    const formattedData = formatFormDataForBackend(formData);
    const fd = new FormData();
    
    Object.entries(formattedData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          // Handle arrays (like business_category_ids)
          value.forEach((item, index) => {
            fd.append(`${key}[${index}]`, item);
          });
        } else if (value instanceof File) {
          // Handle file uploads
          fd.append(key, value);
        } else {
          // Handle regular values
          fd.append(key, value);
        }
      }
    });

    console.log("Formatted FormData values:");
    for (let [key, value] of fd.entries()) {
      console.log(`${key}:`, value);
    }

    mutate(fd, {
      onSuccess: (data) => {
        console.log("KYC submission successful:", data);
        alert("KYC verification submitted successfully! We will review your information and get back to you soon.");
        // You can redirect to dashboard or another page here
      },
      onError: (error) => {
        console.error("KYC submission failed:", error);
        alert("Failed to submit KYC verification. Please try again or contact support.");
      }
    });
  };

  const handleEdit = () => {
    setCurrentStep(0);
  };

  return (
    <div className="my-8">
      <ProgressIndicator steps={steps} currentStep={currentStep} />

      <Card className="w-full max-w-5xl mx-auto">
        <CardContent className="py-6 px-12">
          <h2 className="text-2xl font-semibold text-center text-primary mb-10">
            Vendor Verification
            <div className="h-1 w-32 bg-primary mx-auto mt-1" />
          </h2>

          {currentStep === 0 && (
            <PersonalInfoForm
              onNext={handleNext}
              initialData={formData.personalInfo}
            />
          )}

          {currentStep === 1 && requiresCompanyDetails && (
            <CompanyDetailsForm
              onNext={handleNext}
              onPrevious={handlePrevious}
              initialData={formData.companyDetails}
              companyType={formData.personalInfo.companyType}
            />
          )}

          {currentStep === (requiresCompanyDetails ? 2 : 1) && (
            <DocumentUploadForm
              onNext={handleNext}
              onPrevious={handlePrevious}
              initialData={formData.documents}
              companyType={formData.personalInfo.companyType}
            />
          )}

          {currentStep === (requiresCompanyDetails ? 3 : 2) && (
            <BankDetailsForm
              onSubmit={(data) => {
                setFormData((prev) => ({ ...prev, ...data })); // Update form data
                setCurrentStep((prev) => prev + 1); // Move to the review step
              }}
              onPrevious={handlePrevious}
              initialData={formData.bankDetails}
            />
          )}

          {currentStep === (requiresCompanyDetails ? 4 : 3) && (
            <ReviewStep
              formData={formData}
              onSubmit={handleSubmit}
              onEdit={handleEdit}
              isLoading={isLoading}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
