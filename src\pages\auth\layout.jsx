import { Outlet, useNavigate } from "react-router-dom";
import logo from "@/assets/png/logo.png";

const AuthLayout = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="pt-4 sm:pt-8 px-4 sm:px-8 xl:px-32">
        <img
          src={logo}
          alt="Zettabid"
          className="object-contain w-16 sm:w-32 cursor-pointer"
          onClick={() => navigate("/")}
        />
      </div>
      <Outlet />
    </>
  );
};

export default AuthLayout;
