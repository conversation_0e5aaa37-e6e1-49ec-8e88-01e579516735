import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { LuArrowLeft, LuArrowRight } from "react-icons/lu";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import successful from "@/assets/png/successful.png";
import { useState } from "react";
import InfoTable from "./InfoTable";
import { useNavigate } from "react-router-dom";

const SubmitVerification = () => {
  const navigate = useNavigate();
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  const handleBack = () => {
    dispatch(
      setSetup({
        ...setup,
        step: 4,
      })
    );
  };

  const onSubmit = async () => {
    setLoading(true);
    try {
      console.log(setup);
      console.log("Form Submitted");
      navigate("/vendor/1/dashboard");
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-[40rem] sm:min-w-[40rem] mx-auto p-6 space-y-6 bg-white">
      <h2 className="font-nunito text-bodyLarge sm:text-[1.8rem] text-center font-bold">
        Verification Summary
      </h2>
      {/* Basic Info Section */}
      <InfoTable
        title="Basic Info"
        data={[
          { label: "Company Name", value: setup?.companyName },
          { label: "Email", value: setup?.email },
          {
            label: "Company Representative",
            value: setup?.companyRepresentative,
          },
          { label: "Phone Number", value: setup?.phoneNumber },
        ]}
      />
      <hr className="border-t border-gray-300 my-4" />

      {/* Address Details Section */}
      <InfoTable
        title="Address Details"
        data={[
          { label: "Province", value: setup?.province },
          { label: "District", value: setup?.district },
          { label: "Municipality", value: setup?.municipality },
          { label: "Street Address", value: setup?.streetAddress },
        ]}
      />
      <hr className="border-t border-gray-300 my-4" />

      {/* Business Details Section */}
      <InfoTable
        title="Business Details"
        data={[
          { label: "Business Category", value: setup?.businessCategory },
          { label: "Website", value: setup?.website },
          { label: "Company Registration", value: setup?.companyRegistration },
          { label: "VAT/PAN", value: setup?.vatOrPan },
        ]}
      />
      <hr className="border-t border-gray-300 my-4" />

      {/* Additional Details Section */}
      <InfoTable
        title="Additional Details"
        data={[
          { label: "Tax Clearance Certificate", value: setup?.taxClearance },
          { label: "Logo", value: setup?.logo },
          { label: "Company Stamp", value: setup?.companyStamp },
          { label: "Additional Document", value: setup?.additionalDocument },
        ]}
      />
      <hr className="border-t border-gray-300 my-4" />

      {/* Buttons */}
      <div className="flex justify-end gap-4 mt-5">
        <Button
          variant="outline"
          className="flex items-center text-bodyMedium font-bold hover:bg-green1 text-darkNavy h-[3rem] px-10 sm:px-8 rounded-xl tracking-wider"
          onClick={handleBack}
        >
          <LuArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          className="font-bold text-bodyMedium bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider flex items-center"
          onClick={onSubmit}
          disabled={loading}
        >
          {loading ? (
            <AiOutlineLoading3Quarters className="h-4 w-4 animate-spin" />
          ) : (
            <>
              Submit
              <LuArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default SubmitVerification;
