import { useAppSelector } from "@/hooks/StoreHooks";
import { useEffect, useState } from "react";
import { TbAddressBook } from "react-icons/tb";
import { PiBuildingsBold } from "react-icons/pi";
import { HiLink } from "react-icons/hi";
import { LuFilePlus2 } from "react-icons/lu";

const Steps = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  const [step, setStep] = useState(1);

  useEffect(() => {
    if (setup) {
      setStep(setup.step);
    }
  }, [setup]);

  return (
    <div className="flex justify-between items-center border-2 max-w-[30rem] py-4 sm:py-5 px-2 sm:px-4 rounded-2xl">
      <div className="flex items-center gap-2 border-r-2 pr-2 sm:pr-4 h-6">
        <div
          className={`p-2 xs:p-2 sm:p-3 rounded-full
          ${step == 1 && "bg-green1"}
          ${step > 1 && "bg-[#b5fcd7]"}
          ${step < 1 && "bg-transparent"}
        `}
        >
          <PiBuildingsBold
            className={`text-lg xs:text-xl sm:text-2xl
            ${step == 1 && "text-white"}
            ${step > 1 && "text-green2"}
            ${step < 1 && "text-lightGray1"}
          `}
          />
        </div>
        <div className={`${step == 1 ? "block" : "hidden"}`}>
          <span className="text-xs font-nunito font-semibold text-nowrap">
            Step 1/4
          </span>
          <h2 className="text-textSmall sm:text-bodyMedium font-nunito font-bold text-nowrap">{`Basic Details`}</h2>
        </div>
      </div>
      <div className="flex items-center gap-2 border-r-2 px-2 sm:px-4 h-6">
        <div
          className={`p-2 xs:p-2 sm:p-3 rounded-full
          ${step == 2 && "bg-green1"}
          ${step > 2 && "bg-[#b5fcd7]"}
          ${step < 2 && "bg-transparent"}
        `}
        >
          <TbAddressBook
            className={`text-lg xs:text-xl sm:text-2xl
            ${step == 2 && "text-white"}
            ${step > 2 && "text-green2"}
            ${step < 2 && "text-lightGray1"}
          `}
          />
        </div>
        <div className={`${step == 2 ? "block" : "hidden"}`}>
          <span className="text-xs font-nunito font-semibold leading-3">
            Step 2/4
          </span>
          <h2 className="text-textSmall sm:text-bodyMedium font-nunito font-bold text-nowrap">{`Address Details`}</h2>
        </div>
      </div>
      <div className="flex items-center gap-2 border-r-2 px-2 sm:px-4 h-6">
        <div
          className={`p-2 xs:p-2 sm:p-3 rounded-full
          ${step == 3 && "bg-green1"}
          ${step > 3 && "bg-[#b5fcd7]"}
          ${step < 3 && "bg-transparent"}
        `}
        >
          <HiLink
            className={`text-lg xs:text-xl sm:text-2xl
            ${step == 3 && "text-white"}
            ${step > 3 && "text-green2"}
            ${step < 3 && "text-lightGray1"}
          `}
          />
        </div>
        <div className={`${step == 3 ? "block" : "hidden"}`}>
          <span className="text-xs font-nunito font-semibold leading-3">
            Step 3/4
          </span>
          <h2 className="text-textSmall sm:text-bodyMedium font-nunito font-bold text-nowrap">{`Business Details`}</h2>
        </div>
      </div>
      <div className="flex items-center gap-2 pl-2 sm:pl-4 h-6">
        <div
          className={`p-2 xs:p-2 sm:p-3 rounded-full
          ${step == 4 && "bg-green1"}
          ${step > 4 && "bg-[#b5fcd7]"}
          ${step < 4 && "bg-transparent"}
        `}
        >
          <LuFilePlus2
            className={`text-lg xs:text-xl sm:text-2xl
           ${step == 4 && "text-white"}
            ${step > 4 && "text-green2"}
            ${step < 4 && "text-lightGray1"}
          `}
          />
        </div>
        <div className={`${step == 4 ? "block" : "hidden"}`}>
          <span className="text-xs font-nunito font-semibold leading-3">
            Step 4/4
          </span>
          <h2 className="text-textSmall sm:text-bodyMedium font-nunito font-bold text-nowrap">{`Additional Details`}</h2>
        </div>
      </div>
    </div>
  );
};

export default Steps;
