"use client";

import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function PersonalInfoForm({ onNext, initialData }) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: initialData,
  });

  const onSubmit = (data) => {
    onNext({ personalInfo: data });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 font-poppins">
      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            {...register("firstName", { required: "First name is required" })}
          />
          {errors.firstName && (
            <p className="text-red-500">{errors.firstName.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            {...register("lastName", { required: "Last name is required" })}
          />
          {errors.lastName && (
            <p className="text-red-500">{errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            {...register("email", { required: "Email is required" })}
          />
          {errors.email && (
            <p className="text-red-500">{errors.email.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <Input
            id="phoneNumber"
            {...register("phoneNumber", {
              required: "Phone number is required",
            })}
          />
          {errors.phoneNumber && (
            <p className="text-red-500">{errors.phoneNumber.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            {...register("address", { required: "Address is required" })}
          />
          {errors.address && (
            <p className="text-red-500">{errors.address.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="companyType">Company Type</Label>
          <Controller
            name="companyType"
            control={control}
            rules={{ required: "Company type is required" }}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Select your company type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Company-Type</SelectLabel>
                    <SelectItem value="private">Private</SelectItem>
                    <SelectItem value="semi-government">
                      Semi-Government
                    </SelectItem>
                    <SelectItem value="government">Government</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.companyType && (
            <p className="text-red-500">{errors.companyType.message}</p>
          )}
        </div>
      </div>

      <Button
        type="submit"
        className="w-full text-black text-lg font-poppins mt-8 hover:border hover:text-primary"
      >
        Next
      </Button>
    </form>
  );
}
