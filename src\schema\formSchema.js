import { z } from "zod";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_FILE_TYPES = ["image/jpeg", "image/png", "application/pdf"];

export const kycFormSchema = z.object({
  company_representative: z.string().min(2, { message: 'Full name must be at least 2 characters.' }),
  logo: z.instanceof(File)
    .refine((file) => file.size <= MAX_FILE_SIZE, "File size must be less than 5MB")
    .refine(
      (file) => ACCEPTED_FILE_TYPES.includes(file.type),
      "File must be JPEG, PNG, or PDF"
    ),
  email: z.string().email({ message: 'Invalid email address.' }),
  province: z.string().min(1, { message: "Province is required" }),
  district: z.string().min(1, { message: "District is required" }),
  municipality: z.string().min(1, { message: "Municipality is required" }),
  phone: z.string().min(10, { message: 'Contact number must be at least 10 digits.' }),
  name: z.string().min(2, { message: 'Business name must be at least 2 characters.' }),
  address: z.string().min(5, { message: 'Business address must be at least 5 characters.' }),
  company_registration: z.instanceof(File)
    .refine((file) => file.size <= MAX_FILE_SIZE, "File size must be less than 5MB")
    .refine(
      (file) => ACCEPTED_FILE_TYPES.includes(file.type),
      "File must be JPEG, PNG, or PDF"
    ),
  vat_pan: z.instanceof(File)
    .refine((file) => file.size <= MAX_FILE_SIZE, "File size must be less than 5MB")
    .refine(
      (file) => ACCEPTED_FILE_TYPES.includes(file.type),
      "File must be JPEG, PNG, or PDF"
    ),
  tax_clearance: z.instanceof(File)
    .refine((file) => file.size <= MAX_FILE_SIZE, "File size must be less than 5MB")
    .refine(
      (file) => ACCEPTED_FILE_TYPES.includes(file.type),
      "File must be JPEG, PNG, or PDF"
    )
    .optional(),
  business_type: z.string({ required_error: 'Please select a business type.' }),

  consent: z.boolean().refine((value) => value === true, { message: 'You must agree to the declaration.' }),
});