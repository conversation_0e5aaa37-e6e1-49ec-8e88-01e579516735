import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useF<PERSON>, Controller } from "react-hook-form";
import { AiFillEye, AiFillEyeInvisible } from "react-icons/ai";
import { useLocation, useNavigate } from "react-router-dom";
import { useLoginAdminMutation } from "@/services/auth/mutation";
import { useToast } from "@/components/ui/use-toast";
import { useAppDispatch } from "@/hooks/StoreHooks";
import { setToken, setUser } from "@/redux/slice/auth/authSlice";

const AuthSignInPage = () => {
  const loginAdminMutation = useLoginAdminMutation();
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  const onSubmit = async (data) => {
    const response = await loginAdminMutation.mutateAsync(data);

    if (response.success) {
      toast({
        title: "Login Successful",
        description: "Welcome back!",
        variant: "success",
        duration: 1500,
        isclosable: true,
      });

      // Dispatch Redux actions for user and token management
      dispatch(
        setUser({
          email: response?.data?.email,
          userId: response?.data?.id,
          address: response?.data?.address,
          profile: response?.data?.profile,
          name: response?.data?.name,
          userType: response?.user_type,
          isLogin: true,
        })
      );

      dispatch(
        setToken({
          accessToken: response.token,
          refreshToken: "",
        })
      );

      reset();

      const from =
        location.state?.from || `/admin/${response.data.id}/dashboard`;

      if (from) {
        navigate(from, { replace: true });
      } else {
        navigate(`/admin/${response.data.id}/dashboard`);
      }
    } else {
      console.log("dklfj");
      toast({
        title: "Login Failed",
        description: response.message || "Please check your credentials.",
        variant: "error",
        duration: 3000,
        isclosable: true,
      });
    }
  };

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 min-h-[30rem] h-[calc(100svh-10rem)] items-center sm:mt-10">
      <div className="w-full py-5">
        <div>
          <h1 className="font-poppins font-semibold text-heading4 sm:text-heading3 leading-[50px] text-navy">{`Admin! Welcome to Zettabid`}</h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">{`Login to access your account`}</p>
        </div>
        <div className="lg:max-w-[30rem] mt-5">
          <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Email
              </label>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Email is required",
                  },
                }}
                render={({ field }) => (
                  <>
                    <input
                      {...field}
                      type="email"
                      placeholder="Email"
                      autoComplete="off"
                      className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                        errors.email
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    {errors.email && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.email.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Password Field */}
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Password
              </label>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Password is required",
                  },
                  minLength: {
                    value: 6,
                    message: "Password must be at least 6 characters",
                  },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      autoComplete="off"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${
                        errors.password
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-[1.7rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.password && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            {/* Submit Button */}
            <div>
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
                type="submit"
              >
                Signin
                {/* {false ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Continue"
                )} */}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AuthSignInPage;
