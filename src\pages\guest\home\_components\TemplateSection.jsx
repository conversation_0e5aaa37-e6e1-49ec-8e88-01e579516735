import template1 from "@/assets/png/template1.png";
import template2 from "@/assets/png/template2.png";
import template3 from "@/assets/png/template3.png";
import { Button } from "@/components/ui/button";

const TemplateSection = () => {
  return (
    <div className="mt-24 px-4 sm:px-8 xl:px-32">
      <span className="font-nunito font-semibold text-heading6 text-green1 w-full text-center flex justify-center">{`Templates`}</span>
      <div className="flex flex-col lg:flex-row gap-10 lg:justify-between items-center w-full">
        <div className="w-full mt-9 flex flex-col justify-center items-center">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy leading-[2.875rem] text-center">{`Choose Your Favorite Template`}</h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-darkTeal max-w-[50.375rem] text-center">{`Streamline Your Quotations with Ease. Efficiency meets Precision. Elevate Your Processes with our Intuitive Software. Empower Your Team. Unleash Growth Potential.`}</p>
        </div>
      </div>
      <div className="flex flex-wrap justify-center items-center mt-10 gap-5 xl:gap-[4.25rem]">
        <img
          loading="lazy"
          src={template1}
          alt="template 1"
          className="lg:max-w-[20.134rem]"
        />
        <img
          loading="lazy"
          src={template2}
          alt="template 2"
          className="lg:max-w-[20.134rem]"
        />
        <img
          loading="lazy"
          src={template3}
          alt="template 3"
          className="lg:max-w-[20.134rem]"
        />
      </div>
      <div className="w-full text-center mt-10">
        <Button className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-12 rounded-xl tracking-wider">
          More Template
        </Button>
      </div>
    </div>
  );
};

export default TemplateSection;
