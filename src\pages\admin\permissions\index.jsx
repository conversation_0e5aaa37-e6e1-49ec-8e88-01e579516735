import { useState } from "react";
import { Plus, Edit, Trash2, Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

// API imports
import { useGetPermissionsQuery } from "@/services/admin/queries";
import {
  useCreatePermissionMutation,
  useUpdatePermissionMutation,
  useDeletePermissionMutation,
} from "@/services/admin/mutation";

export default function PermissionsPage() {
  const { toast } = useToast();
  
  // API hooks
  const { data: permissionsData, isLoading } = useGetPermissionsQuery();
  const createPermissionMutation = useCreatePermissionMutation();
  const updatePermissionMutation = useUpdatePermissionMutation();
  const deletePermissionMutation = useDeletePermissionMutation();

  // Local state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPermission, setCurrentPermission] = useState(null);
  const [newPermission, setNewPermission] = useState({
    name: "",
    display_name: "",
    description: "",
    group: "",
  });
  const [isEditMode, setIsEditMode] = useState(false);

  const permissions = permissionsData?.data || [];

  // Check for errors
  const hasError = permissionsData?.error;

  // Handle opening the add permission dialog
  const handleAddPermission = () => {
    setIsEditMode(false);
    setNewPermission({
      name: "",
      display_name: "",
      description: "",
      group: "",
    });
    setIsAddDialogOpen(true);
  };

  // Handle opening the edit permission dialog
  const handleEditPermission = (permission) => {
    setIsEditMode(true);
    setNewPermission({
      name: permission.name,
      display_name: permission.display_name,
      description: permission.description,
      group: permission.group,
    });
    setCurrentPermission(permission);
    setIsAddDialogOpen(true);
  };

  // Handle saving a new or edited permission
  const handleSavePermission = async () => {
    try {
      if (isEditMode) {
        await updatePermissionMutation.mutateAsync({
          id: currentPermission.id,
          data: newPermission,
        });
        toast({
          title: "Success",
          description: "Permission updated successfully.",
          variant: "success",
        });
      } else {
        await createPermissionMutation.mutateAsync(newPermission);
        toast({
          title: "Success",
          description: "Permission created successfully.",
          variant: "success",
        });
      }
      setIsAddDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Something went wrong.",
        variant: "destructive",
      });
    }
  };

  // Handle opening the delete confirmation dialog
  const handleDeleteClick = (permission) => {
    setCurrentPermission(permission);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming permission deletion
  const handleDeleteConfirm = async () => {
    try {
      await deletePermissionMutation.mutateAsync(currentPermission.id);
      toast({
        title: "Success",
        description: "Permission deleted successfully.",
        variant: "success",
      });
      setIsDeleteDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete permission.",
        variant: "destructive",
      });
    }
  };

  // Early returns for loading and error states
  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (hasError) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-destructive mb-2">Failed to load permissions</p>
            <p className="text-sm text-muted-foreground">
              {hasError?.message || "Please try refreshing the page"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group permissions by group - with defensive programming
  const groupedPermissions = Array.isArray(permissions)
    ? permissions.reduce((acc, permission) => {
        const group = permission?.group || "Other";
        if (!acc[group]) {
          acc[group] = [];
        }
        acc[group].push(permission);
        return acc;
      }, {})
    : {};

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold">Permissions Management</CardTitle>
          <Button onClick={handleAddPermission}>
            <Plus className="h-4 w-4 mr-2" /> Add Permission
          </Button>
        </CardHeader>
        <CardContent>
          {Object.keys(groupedPermissions).length > 0 ? (
            <div className="space-y-6">
              {Object.entries(groupedPermissions).map(([group, groupPermissions]) => (
                <div key={group}>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    {group}
                    <Badge variant="secondary">{Array.isArray(groupPermissions) ? groupPermissions.length : 0}</Badge>
                  </h3>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Display Name</TableHead>
                          <TableHead className="hidden md:table-cell">Description</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Array.isArray(groupPermissions) && groupPermissions.map((permission) => (
                          <TableRow key={permission.id}>
                            <TableCell className="font-mono text-sm">
                              {permission.name}
                            </TableCell>
                            <TableCell className="font-medium">
                              {permission.display_name}
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              {permission.description}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditPermission(permission)}
                                >
                                  <Edit className="h-4 w-4" />
                                  <span className="sr-only">Edit</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteClick(permission)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                  <span className="sr-only">Delete</span>
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="flex flex-col items-center justify-center">
                <p className="text-muted-foreground mb-4">No permissions found</p>
                <Button onClick={handleAddPermission}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create your first permission
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Permission Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit Permission" : "Add New Permission"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the permission details below."
                : "Fill in the details to create a new permission."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Permission Name</Label>
              <Input
                id="name"
                value={newPermission.name}
                onChange={(e) =>
                  setNewPermission({ ...newPermission, name: e.target.value })
                }
                placeholder="e.g., manage_users"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="display_name">Display Name</Label>
              <Input
                id="display_name"
                value={newPermission.display_name}
                onChange={(e) =>
                  setNewPermission({ ...newPermission, display_name: e.target.value })
                }
                placeholder="e.g., Manage Users"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="group">Group</Label>
              <Input
                id="group"
                value={newPermission.group}
                onChange={(e) =>
                  setNewPermission({ ...newPermission, group: e.target.value })
                }
                placeholder="e.g., user_management"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newPermission.description}
                onChange={(e) =>
                  setNewPermission({ ...newPermission, description: e.target.value })
                }
                placeholder="Describe what this permission allows"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              onClick={handleSavePermission}
              disabled={
                !newPermission.name ||
                !newPermission.display_name ||
                createPermissionMutation.isPending ||
                updatePermissionMutation.isPending
              }
            >
              {createPermissionMutation.isPending || updatePermissionMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              {isEditMode ? "Update Permission" : "Add Permission"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the permission "{currentPermission?.display_name}".
              This action cannot be undone and may affect roles that use this permission.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground"
              disabled={deletePermissionMutation.isPending}
            >
              {deletePermissionMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
