import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useGetFaqs } from "@/services/general/query";
import { Skeleton } from "@/components/ui/skeleton";

const FAQSection = () => {
  const { data: faqs, isLoading, isError } = useGetFaqs();

  return (
    <div className="px-4 sm:px-8 xl:px-32 py-10">
      <span className="font-nunito font-semibold text-heading6 text-green1 w-full text-center flex justify-center">
        FAQs
      </span>

      <div className="w-full mt-9 flex flex-col justify-center items-center">
        <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy leading-[2.875rem] text-center">
          Frequently Asked Questions
        </h1>
      </div>

      <div className="flex justify-center w-full mt-10">
        {isLoading ? (
          <div className="space-y-6 w-full sm:min-w-[36.188rem] lg:min-w-[56.188rem] max-w-[56.188rem]">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="bg-white px-4 py-6 shadow-custom-shadow border rounded-xl space-y-3"
              >
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            ))}
          </div>
        ) : isError ? (
          <div className="text-red-500 font-nunito font-semibold">
            Failed to load FAQs. Please try again later.
          </div>
        ) : (
          <Accordion type="single" collapsible className="space-y-6">
            {faqs.map((faq) => (
              <AccordionItem
                key={faq.id}
                value={faq.id}
                className="bg-white px-4 shadow-custom-shadow border sm:min-w-[36.188rem] lg:min-w-[56.188rem] max-w-[56.188rem] rounded-xl"
              >
                <AccordionTrigger className="!font-nunito !font-bold !text-[1.25rem] !text-navy">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="!font-nunito !text-[1.125rem] !leading-7 text-darkTeal">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
};

export default FAQSection;
