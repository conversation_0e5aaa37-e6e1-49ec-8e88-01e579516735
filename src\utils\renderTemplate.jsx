import SpecTemplate1 from "@/pages/customer/create-specification/_components/templates/SpecTemplate1";
import SpecTemplate2 from "@/pages/customer/create-specification/_components/templates/SpecTemplate2";
import SpecTemplate3 from "@/pages/customer/create-specification/_components/templates/SpecTemplate3";

export function renderTemplate(templateId, currentSpec, onSpecChange) {
  switch (templateId) {
    case "basic":
      return (
        <>
          <SpecTemplate1
            handleChange={onSpecChange}
            currentSpec={currentSpec}
          />
        </>
      );
    case "detailed":
      return (
        <>
          <SpecTemplate2
            handleChange={onSpecChange}
            currentSpec={currentSpec}
          />
        </>
      );
    case "technical":
      return (
        <>
          <SpecTemplate3
            handleChange={onSpecChange}
            currentSpec={currentSpec}
          />
        </>
      );
    default:
      return null;
  }
}
