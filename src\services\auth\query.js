import { useSuspenseQuery } from "@tanstack/react-query";
import { getCustomerProfile, getVendorProfile } from "./api";

export function useGetCustomerProfileQuery() {
  return useSuspenseQuery({
    queryKey: ["getCustomerProfile"],
    queryFn: () => getCustomerProfile(),
  });
}
export function useGetVendorProfileQuery() {
  return useSuspenseQuery({
    queryKey: ["getVendorProfile"],
    queryFn: () => getVendorProfile(),
  });
}
