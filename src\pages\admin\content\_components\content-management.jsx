"use client";

import { useState } from "react";
import { <PERSON>, Eye, Clock, ArrowLeft, Save, Globe } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import { <PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import { RichTextEditor } from "./rich-text-editor";

// Mock data for pages
const initialPages = [
  {
    id: "about",
    title: "About Us",
    slug: "about",
    description: "Information about our company, mission, and team",
    lastUpdated: new Date(2023, 5, 15),
    status: "published",
    sections: [
      {
        id: "intro",
        title: "Introduction",
        content: `<h2>Our Story</h2>
        <p>Founded in 2010, our company has been at the forefront of innovation in the industry. We started with a simple mission: to provide high-quality solutions that make a difference.</p>
        <h2>Our Mission</h2>
        <p>We strive to deliver exceptional products and services that exceed our customers' expectations. Our commitment to quality and innovation drives everything we do.</p>`,
      },
      {
        id: "team",
        title: "Our Team",
        content: `<h2>Leadership</h2>
        <p>Our leadership team brings decades of experience from various industries, guiding our company with vision and expertise.</p>
        <ul>
          <li>John Doe - CEO</li>
          <li>Jane Smith - CTO</li>
          <li>Robert Johnson - CFO</li>
        </ul>
        <h2>Our Experts</h2>
        <p>Our team consists of dedicated professionals who are passionate about what they do. From engineers to designers, everyone plays a crucial role in our success.</p>`,
      },
      {
        id: "values",
        title: "Our Values",
        content: `<h2>Innovation</h2>
        <p>We constantly seek new ways to improve and innovate in everything we do.</p>
        <h2>Integrity</h2>
        <p>We conduct our business with the highest ethical standards and transparency.</p>
        <h2>Customer Focus</h2>
        <p>Our customers are at the center of our decisions and actions.</p>`,
      },
    ],
    meta: {
      title: "About Us | Company Name",
      description:
        "Learn about our company, our mission, values, and the team behind our success.",
    },
  },
  {
    id: "resources",
    title: "Resources",
    slug: "resources",
    description: "Helpful resources, guides, and documentation",
    lastUpdated: new Date(2023, 6, 10),
    status: "published",
    sections: [
      {
        id: "guides",
        title: "Guides",
        content: `<h2>Getting Started</h2>
        <p>Our comprehensive guides will help you get started with our products and services.</p>
        <ul>
          <li>Beginner's Guide to Our Platform</li>
          <li>Advanced Features Tutorial</li>
          <li>Integration Guide</li>
        </ul>`,
      },
      {
        id: "documentation",
        title: "Documentation",
        content: `<h2>API Documentation</h2>
        <p>Detailed documentation for developers looking to integrate with our platform.</p>
        <h2>User Manuals</h2>
        <p>Step-by-step instructions for using our products effectively.</p>`,
      },
      {
        id: "downloads",
        title: "Downloads",
        content: `<h2>Software Downloads</h2>
        <p>Download the latest versions of our software and tools.</p>
        <h2>Templates</h2>
        <p>Access templates to help you get started quickly.</p>`,
      },
    ],
    meta: {
      title: "Resources | Company Name",
      description:
        "Access guides, documentation, and downloads to help you make the most of our products.",
    },
  },
  {
    id: "templates",
    title: "Templates",
    slug: "templates",
    description: "Ready-to-use templates for various purposes",
    lastUpdated: new Date(2023, 7, 5),
    status: "published",
    sections: [
      {
        id: "business",
        title: "Business Templates",
        content: `<h2>Business Plans</h2>
        <p>Professional business plan templates to help you start or grow your business.</p>
        <h2>Financial Models</h2>
        <p>Excel templates for financial planning and analysis.</p>`,
      },
      {
        id: "marketing",
        title: "Marketing Templates",
        content: `<h2>Social Media</h2>
        <p>Templates for creating engaging social media content.</p>
        <h2>Email Campaigns</h2>
        <p>Ready-to-use email templates for your marketing campaigns.</p>`,
      },
      {
        id: "design",
        title: "Design Templates",
        content: `<h2>Presentation Templates</h2>
        <p>Professional PowerPoint and Keynote templates for impactful presentations.</p>
        <h2>Graphic Design</h2>
        <p>Photoshop and Illustrator templates for various design needs.</p>`,
      },
    ],
    meta: {
      title: "Templates | Company Name",
      description:
        "Browse and download our collection of professional templates for business, marketing, and design.",
    },
  },
  {
    id: "contact",
    title: "Contact Us",
    slug: "contact",
    description: "Contact information and inquiry form",
    lastUpdated: new Date(2023, 4, 20),
    status: "published",
    sections: [
      {
        id: "info",
        title: "Contact Information",
        content: `<h2>Get in Touch</h2>
        <p>We'd love to hear from you! Here's how you can reach us:</p>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Phone: (*************</li>
          <li>Address: 123 Main Street, City, Country</li>
        </ul>`,
      },
      {
        id: "hours",
        title: "Business Hours",
        content: `<h2>When We're Available</h2>
        <p>Our support team is available during the following hours:</p>
        <ul>
          <li>Monday - Friday: 9:00 AM - 6:00 PM</li>
          <li>Saturday: 10:00 AM - 2:00 PM</li>
          <li>Sunday: Closed</li>
        </ul>`,
      },
      {
        id: "form",
        title: "Contact Form",
        content: `<h2>Send Us a Message</h2>
        <p>Use the form below to send us a message, and we'll get back to you as soon as possible.</p>
        <form>
          <div class="mb-4">
            <label class="block mb-2">Name</label>
            <input type="text" placeholder="Your Name" class="w-full p-2 border rounded" />
          </div>
          <div class="mb-4">
            <label class="block mb-2">Email</label>
            <input type="email" placeholder="Your Email" class="w-full p-2 border rounded" />
          </div>
          <div class="mb-4">
            <label class="block mb-2">Message</label>
            <textarea placeholder="Your Message" class="w-full p-2 border rounded" rows="4"></textarea>
          </div>
          <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded">Send Message</button>
        </form>`,
      },
    ],
    meta: {
      title: "Contact Us | Company Name",
      description:
        "Get in touch with our team. Find our contact information and business hours.",
    },
  },
];

export default function ContentManagement() {
  const [pages, setPages] = useState(initialPages);
  const [currentPage, setCurrentPage] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedPage, setEditedPage] = useState(null);
  const [activeTab, setActiveTab] = useState("pages");
  const [saveStatus, setSaveStatus] = useState(null);

  const handleEditPage = (page) => {
    const copiedPage = JSON.parse(JSON.stringify(page)); // Deep copy to avoid direct mutation
    // Convert date strings to Date objects
    copiedPage.lastUpdated = new Date(copiedPage.lastUpdated);
    setEditedPage(copiedPage);
    setCurrentPage(page);
    setIsEditing(true);
  };

  // Handle saving page changes
  const handleSavePage = () => {
    setSaveStatus("saving");

    // Simulate API call with timeout
    setTimeout(() => {
      setPages(
        pages.map((page) => (page.id === editedPage.id ? editedPage : page))
      );
      setCurrentPage(editedPage);
      setSaveStatus("saved");

      // Reset save status after 3 seconds
      setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
    }, 1000);
  };

  // Handle updating section content
  const handleSectionContentChange = (sectionId, content) => {
    setEditedPage({
      ...editedPage,
      sections: editedPage.sections.map((section) =>
        section.id === sectionId ? { ...section, content } : section
      ),
      lastUpdated: new Date(),
    });
  };

  // Handle updating meta information
  const handleMetaChange = (field, value) => {
    setEditedPage({
      ...editedPage,
      meta: {
        ...editedPage.meta,
        [field]: value,
      },
      lastUpdated: new Date(),
    });
  };

  // Handle updating page title
  const handleTitleChange = (value) => {
    setEditedPage({
      ...editedPage,
      title: value,
      lastUpdated: new Date(),
    });
  };

  // Handle updating page description
  const handleDescriptionChange = (value) => {
    setEditedPage({
      ...editedPage,
      description: value,
      lastUpdated: new Date(),
    });
  };

  // Format date for display
  const formatDate = (date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <>
      {!isEditing ? (
        <Card>
          <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
            <CardTitle className="text-2xl font-bold">
              Content Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="pages"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="mb-4">
                <TabsTrigger value="pages">Pages</TabsTrigger>
              </TabsList>

              <TabsContent value="pages">
                <div className="grid gap-4 md:grid-cols-2">
                  {pages.map((page) => (
                    <Card key={page.id} className="overflow-hidden">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle>{page.title}</CardTitle>
                            <CardDescription className="mt-1">
                              {page.description}
                            </CardDescription>
                          </div>
                          <Badge
                            variant={
                              page.status === "published"
                                ? "default"
                                : "outline"
                            }
                          >
                            {page.status === "published"
                              ? "Published"
                              : "Draft"}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-3">
                        <div className="flex items-center text-sm text-muted-foreground mb-4">
                          <Clock className="h-4 w-4 mr-1" />
                          Last updated: {formatDate(page.lastUpdated)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPage(page)}
                          >
                            <Edit className="h-4 w-4 mr-2" /> Edit Content
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4 mr-2" /> Preview
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Globe className="h-4 w-4 mr-2" /> View Live
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Button variant="ghost" onClick={() => setIsEditing(false)}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Pages
            </Button>
            <div className="flex items-center gap-2">
              {saveStatus === "saved" && (
                <Alert className="w-auto py-2 px-4 h-10 flex items-center">
                  <AlertTitle className="text-sm">
                    Changes saved successfully!
                  </AlertTitle>
                </Alert>
              )}
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" /> Preview
              </Button>
              <Button
                onClick={handleSavePage}
                disabled={saveStatus === "saving"}
              >
                <Save className="h-4 w-4 mr-2" />
                {saveStatus === "saving" ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Edit {editedPage?.title}</CardTitle>
              <CardDescription>
                Last updated:{" "}
                {editedPage?.lastUpdated
                  ? formatDate(editedPage.lastUpdated)
                  : "Never"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="page-title">Page Title</Label>
                  <Input
                    id="page-title"
                    value={editedPage?.title || ""}
                    onChange={(e) => handleTitleChange(e.target.value)}
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="page-description">Page Description</Label>
                  <Textarea
                    id="page-description"
                    value={editedPage?.description || ""}
                    onChange={(e) => handleDescriptionChange(e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="grid gap-4">
                  <h3 className="text-lg font-medium">Page Sections</h3>
                  {editedPage?.sections.map((section) => (
                    <Card key={section.id}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">
                          {section.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <RichTextEditor
                          initialContent={section.content}
                          onChange={(content) =>
                            handleSectionContentChange(section.id, content)
                          }
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="grid gap-4">
                  <h3 className="text-lg font-medium">SEO Settings</h3>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="grid gap-4">
                        <div className="grid gap-2">
                          <Label htmlFor="meta-title">Meta Title</Label>
                          <Input
                            id="meta-title"
                            value={editedPage?.meta?.title || ""}
                            onChange={(e) =>
                              handleMetaChange("title", e.target.value)
                            }
                          />
                          <p className="text-sm text-muted-foreground">
                            Recommended length: 50-60 characters
                          </p>
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="meta-description">
                            Meta Description
                          </Label>
                          <Textarea
                            id="meta-description"
                            value={editedPage?.meta?.description || ""}
                            onChange={(e) =>
                              handleMetaChange("description", e.target.value)
                            }
                            rows={3}
                          />
                          <p className="text-sm text-muted-foreground">
                            Recommended length: 150-160 characters
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
