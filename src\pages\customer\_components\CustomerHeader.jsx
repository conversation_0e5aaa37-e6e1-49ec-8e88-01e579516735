import { Button } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { LuAlignLeft } from "react-icons/lu";
import Notification from "./Notification";
import ProfileSection from "./ProfileSection";
import logo from "@/assets/png/logo.png";
import Sidebar from "./Sidebar";

// eslint-disable-next-line react/prop-types
const CustomerHeader = ({ sidebarOpen, setSidebarOpen }) => {
  return (
    <header className=" w-full border sticky bg-darkLight top-0">
      <div className="flex justify-between px-4 py-2">
        <div className="flex items-center gap-6 pl-2">
          <div className="hidden lg:block">
            <Button
              className="px-0 bg-darkLight text-black"
              onClick={() => {
                setSidebarOpen(!sidebarOpen);
                console.log(sidebar<PERSON><PERSON>);
              }}
            >
              <LuAlignLeft className="w-4 h-4" />
            </Button>
          </div>
          <div className="lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button className="px-0 bg-darkLight text-black">
                  <LuAlignLeft className="w-4 h-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[80%] sm:w-[25rem]">
                <SheetHeader>
                  <SheetTitle className="mt-5"></SheetTitle>
                  <SheetDescription></SheetDescription>
                </SheetHeader>
                <Sidebar sidebarOpen="false" />
                <SheetFooter></SheetFooter>
              </SheetContent>
            </Sheet>
          </div>
          <div className="w-24 sm:w-32">
            <img
              src={logo}
              alt="Zettabid"
              className="object-contain w-full h-full"
            />
          </div>
        </div>
        <div className="flex items-center gap-6">
          <Notification />
          <ProfileSection />
        </div>
      </div>
    </header>
  );
};

export default CustomerHeader;
