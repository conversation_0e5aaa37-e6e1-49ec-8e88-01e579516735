"use client";

import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function CompanyDetailsForm({
  onNext,
  onPrevious,
  initialData,
  companyType,
}) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: initialData,
  });

  const onSubmit = (data) => {
    onNext({ companyDetails: data });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Common fields for all company types */}
      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            {...register("companyName", {
              required: "Company name is required",
            })}
          />
          {errors.companyName && (
            <p className="text-red-500">{errors.companyName.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="registrationNumber">Registration Number</Label>
          <Input
            id="registrationNumber"
            {...register("registrationNumber", {
              required: "Registration number is required",
            })}
          />
          {errors.registrationNumber && (
            <p className="text-red-500">{errors.registrationNumber.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="companyName">Province</Label>
          <Input
            id="province"
            {...register("province", {
              required: "Province is required",
            })}
          />
          {errors.companyName && (
            <p className="text-red-500">{errors.province.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="registrationNumber">District</Label>
          <Input
            id="district"
            {...register("district", {
              required: "District is required",
            })}
          />
          {errors.registrationNumber && (
            <p className="text-red-500">{errors.district.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="companyName">Municipality</Label>
          <Input
            id="municipality"
            {...register("municipality", {
              required: "Municipality is required",
            })}
          />
          {errors.companyName && (
            <p className="text-red-500">{errors.municipality.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="registrationNumber">Street Address</Label>
          <Input
            id="streetAddress"
            {...register("streetAddress", {
              required: "Street Address is required",
            })}
          />
          {errors.registrationNumber && (
            <p className="text-red-500">{errors.streetAddress.message}</p>
          )}
        </div>
      </div>

      {/* Conditional fields based on companyType */}
      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="shareholders">Ward</Label>
          <Input
            id="ward"
            {...register("ward", {
              required: "Ward information is required",
            })}
          />
          {errors.shareholders && (
            <p className="text-red-500">{errors.ward.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="boardOfDirectors">Address</Label>
          <Input
            id="address"
            {...register("address", {
              required: "Address is required",
            })}
          />
          {errors.boardOfDirectors && (
            <p className="text-red-500">{errors.address.message}</p>
          )}
        </div>
      </div>

      {/* {companyType === "semi-government" && (
        <div className="flex justify-between gap-8">
          <div className="w-1/2">
            <Label htmlFor="governmentAffiliation">
              Government Affiliation
            </Label>
            <Input
              id="governmentAffiliation"
              {...register("governmentAffiliation", {
                required: "Government affiliation is required",
              })}
            />
            {errors.governmentAffiliation && (
              <p className="text-red-500">
                {errors.governmentAffiliation.message}
              </p>
            )}
          </div>
          <div className="w-1/2">
            <Label htmlFor="publicPrivatePartnership">
              Public-Private Partnership
            </Label>
            <Input
              id="publicPrivatePartnership"
              {...register("publicPrivatePartnership", {
                required: "Public-Private Partnership information is required",
              })}
            />
            {errors.publicPrivatePartnership && (
              <p className="text-red-500">
                {errors.publicPrivatePartnership.message}
              </p>
            )}
          </div>
        </div>
      )}

      {companyType === "government" && (
        <div className="flex justify-between gap-8">
          <div className="w-1/2">
            <Label htmlFor="ministry">Ministry</Label>
            <Input
              id="ministry"
              {...register("ministry", {
                required: "Ministry information is required",
              })}
            />
            {errors.ministry && (
              <p className="text-red-500">{errors.ministry.message}</p>
            )}
          </div>
          <div className="w-1/2">
            <Label htmlFor="department">Department</Label>
            <Input
              id="department"
              {...register("department", {
                required: "Department information is required",
              })}
            />
            {errors.department && (
              <p className="text-red-500">{errors.department.message}</p>
            )}
          </div>
        </div>
      )} */}

      <div className="flex justify-between gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onPrevious}
          className=" w-1/2 text-black text-lg font-poppins hover:border hover:text-primary"
        >
          Previous
        </Button>
        <Button type="submit" className="w-1/2 text-lg">
          Next
        </Button>
      </div>
    </form>
  );
}
