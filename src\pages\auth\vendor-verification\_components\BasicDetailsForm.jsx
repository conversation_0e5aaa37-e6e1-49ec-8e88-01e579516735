import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { isEmail, isText } from "@/utils/validation";
import { useForm, Controller } from "react-hook-form";
import { LuArrowRight } from "react-icons/lu";

const BasicDetailsForm = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      companyName: setup?.companyName || "",
      companyRepresentative: setup?.companyRepresentative || "",
      email: setup?.email || "",
      phoneNo: setup?.phoneNo || "",
    },
  });

  const onSubmit = (data) => {
    console.log(data);
    dispatch(
      setSetup({
        ...setup,
        companyName: data?.companyName,
        companyRepresentative: data?.companyRepresentative,
        email: data?.email,
        phoneNumber: data?.phoneNo,
        step: 2,
      })
    );
  };

  return (
    <div className="max-w-[40rem] sm:min-w-[40rem]">
      <div className="flex flex-col">
        <h2 className="font-nunito text-bodyLarge sm:text-[1.8rem] text-center font-bold">{`Vendor Verification`}</h2>
        <span className="font-nunito text-center">{`Basic Details`}</span>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-10">
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="companyName"
            >
              Company Name *
            </label>
            <Controller
              name="companyName"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Company Name is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid Company Name";
                  }
                  if (value.length < 3) {
                    return "Company Name must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter company name"
                    autoComplete="off"
                    id="companyName"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.companyName && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.companyName?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="email"
            >
              Email *
            </label>
            <Controller
              name="email"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Email is required",
                },
                validate: {
                  isValidEmail: (value) =>
                    isEmail(value) || "Invalid Email Address",
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="email"
                    placeholder="Enter email"
                    autoComplete="off"
                    id="email"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.email && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.email?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="companyRepresentative"
            >
              Company Representative *
            </label>
            <Controller
              name="companyRepresentative"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Company Representative is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid Company Representative Name";
                  }
                  if (value.length < 3) {
                    return "Company Representative Name must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter company representative name"
                    autoComplete="off"
                    id="companyRepresentative"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.companyRepresentative && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.companyRepresentative?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="phoneNo"
            >
              Phone Number *
            </label>
            <Controller
              name="phoneNo"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Phone Number is required",
                },
                pattern: {
                  value: /^[0-9]+$/,
                  message: "Invalid Phone Number",
                },
                minLength: {
                  value: 10,
                  message: "Phone Number should be at least 10 characters",
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter phone number"
                    autoComplete="off"
                    id="phoneNo"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.phoneNo
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.phoneNo && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.phoneNo?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
        </div>
        <div className="float-right mt-5">
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider"
            type="submit"
          >
            Next Step
            <LuArrowRight className="ml-2 h-4 w-4" />
            {/* {false ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Continue"
                )} */}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BasicDetailsForm;
