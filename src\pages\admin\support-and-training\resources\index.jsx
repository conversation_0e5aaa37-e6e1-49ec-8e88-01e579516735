import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Label } from "@radix-ui/react-dropdown-menu";
import { Download, Eye, FileText, Upload, Video } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const SupportTrainingResourcesPage = () => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  const [resources, setResources] = useState([
    {
      id: 1,
      name: "How to Submit a Quote",
      type: "PDF",
      date: "2023-11-15",
      url: "/documents/how-to-submit-quote.pdf",
      tags: ["quotes", "vendors"],
    },
    {
      id: 2,
      name: "Project Pool Overview",
      type: "Video",
      date: "2023-11-10",
      url: "/videos/project-pool-overview.mp4",
      tags: ["overview", "getting-started"],
    },
    {
      id: 3,
      name: "Vendor Onboarding Guide",
      type: "PDF",
      date: "2023-11-05",
      url: "/documents/vendor-onboarding.pdf",
      tags: ["vendors", "onboarding"],
    },
    {
      id: 4,
      name: "Customer Dashboard Tutorial",
      type: "Video",
      date: "2023-10-28",
      url: "/videos/customer-dashboard.mp4",
      tags: ["customers", "dashboard"],
    },
  ]);

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);
      setUploadedFile(file);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);

        if (progress >= 100) {
          clearInterval(interval);
          setIsUploading(false);

          // Add the new resource
          const fileType = file.name.endsWith(".pdf")
            ? "PDF"
            : file.name.endsWith(".mp4") || file.name.endsWith(".mov")
            ? "Video"
            : "Document";

          const newResource = {
            id: resources.length + 1,
            name: file.name.split(".")[0],
            type: fileType,
            date: format(new Date(), "yyyy-MM-dd"),
            url: URL.createObjectURL(file),
            tags: [],
          };

          setResources([...resources, newResource]);

          toast({
            title: "Upload Complete",
            description: `${file.name} has been uploaded successfully.`,
          });
        }
      }, 300);
    }
  };
  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Resources</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="flex justify-between items-center mb-6">
          <div></div>
          <Label htmlFor="file-upload" className="cursor-pointer">
            <div className="flex items-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 rounded-md">
              <Upload className="h-4 w-4" />
              <span>Upload Resource</span>
              <Input
                id="file-upload"
                type="file"
                accept=".pdf,.mp4,.mov"
                className="hidden"
                onChange={handleFileUpload}
              />
            </div>
          </Label>
        </div>

        {isUploading && (
          <div className="mb-6">
            <div className="flex justify-between text-sm mb-1">
              <span>Uploading {uploadedFile?.name}...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="h-2" />
          </div>
        )}

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Date Uploaded</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {resources.map((resource) => (
                <TableRow key={resource.id}>
                  <TableCell className="font-medium">{resource.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {resource.type === "PDF" ? (
                        <FileText className="h-4 w-4 mr-2 text-red-500" />
                      ) : (
                        <Video className="h-4 w-4 mr-2 text-blue-500" />
                      )}
                      {resource.type}
                    </div>
                  </TableCell>
                  <TableCell>{resource.date}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {resource.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" asChild>
                        <a
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View {resource.name}</span>
                        </a>
                      </Button>
                      <Button variant="ghost" size="icon" asChild>
                        <a href={resource.url} download={resource.name}>
                          <Download className="h-4 w-4" />
                          <span className="sr-only">
                            Download {resource.name}
                          </span>
                        </a>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default SupportTrainingResourcesPage;
