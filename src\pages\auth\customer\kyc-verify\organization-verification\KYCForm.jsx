"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import ProgressIndicator from "./_components/ProgressIndicator";
import CompanyDetailsForm from "./_components/CompanyDetailsForm";
import DocumentUploadForm from "./_components/DocumentUploadForm";
import BankDetailsForm from "./_components/BankDetailsForm";
import PersonalInfoForm from "./_components/PersonalInfoForm";

export default function KYCForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    personalInfo: {},
    companyDetails: {},
    documents: {},
    bankDetails: {},
  });

  // Check if company type is selected
  const requiresCompanyDetails = [
    "private",
    "semi-government",
    "government",
  ].includes(formData.personalInfo.companyType);

  // Define steps dynamically
  const steps = [
    "Owner Information",
    "Company Details",
    "Document Upload",
    "Bank Details",
  ];

  const handleNext = (data) => {
    setFormData((prev) => ({
      ...prev,
      ...data,
    }));

    setCurrentStep((prev) => prev + 1);
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleSubmit = (data) => {
    setFormData((prev) => ({
      ...prev,
      ...data,
    }));
    console.log("Final form data:", { ...formData, ...data });
  };

  return (
    <>
      <ProgressIndicator steps={steps} currentStep={currentStep} />
      <Card className="w-full max-w-5xl mx-auto">
        <CardContent className="p-6">
          <h1 className="text-3xl font-bold text-center font-inter mb-8">
            Customer Verification
          </h1>

          {currentStep === 0 && (
            <PersonalInfoForm
              onNext={handleNext}
              initialData={formData.personalInfo}
            />
          )}
          {currentStep === 1 && requiresCompanyDetails && (
            <CompanyDetailsForm
              onNext={handleNext}
              onPrevious={handlePrevious}
              initialData={formData.companyDetails}
              companyType={formData.personalInfo.companyType}
            />
          )}
          {currentStep === (requiresCompanyDetails ? 2 : 1) && (
            <DocumentUploadForm
              onNext={handleNext}
              onPrevious={handlePrevious}
              initialData={formData.documents}
            />
          )}
          {currentStep === (requiresCompanyDetails ? 3 : 2) && (
            <BankDetailsForm
              onSubmit={handleSubmit}
              onPrevious={handlePrevious}
              initialData={formData.bankDetails}
            />
          )}
        </CardContent>
      </Card>
    </>
  );
}
