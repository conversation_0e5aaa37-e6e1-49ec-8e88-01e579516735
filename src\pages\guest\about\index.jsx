import office from "@/assets/jpg/office.jpg";
import laptop from "@/assets/jpg/laptop.jpg";
import { Link } from "react-router-dom";
import { Handshake, Eye, Shield, Award } from "lucide-react";
import Cards from "./_components/Cards";
import WorldMap from "@/components/ui/world-map";
import FooterSection from "../home/<USER>/FooterSection";

const cardData = [
  {
    title: "Collaboration",
    desc: "Collaboration is the process of two or more people or organizations working together to complete a task or achieve a goal",
    icon: Handshake,
  },
  {
    title: "Transparency",
    desc: "Transparency, as used in science is operating in such a way that it is easy for others to see what actions are performed,",
    icon: Eye,
  },
  {
    title: "Trust",
    desc: "Trust will help us foster a positive and productive environment that delivers value to our users and customers.",
    icon: Shield,
  },
  {
    title: "Integrity",
    desc: "Collaboration is the process of two or more people or organizations working together to complete a task or achieve a goal.",
    icon: Award,
  },
];

const AboutPage = () => {
  return (
    <>
      {" "}
      <div className="px-4 sm:px-8 xl:px-32 mt-16">
        <div className="flex flex-col justify-center items-center">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy text-center leading-[2.875rem]">
            Our Company
          </h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-center text-darkTeal max-w-[50.375rem]">
            Lorem ipsum, dolor sit amet consectetur adipisicing elit. Adipisci
            accusantium deserunt perspiciatis sequi, nostrum quaerat esse
            consequuntur eum quasi voluptas provident repellendus ducimus sunt
            dolore ipsum necessitatibus ad fugit libero?
          </p>
        </div>
        <img
          src={office}
          className="w-full h-[18rem] md:h-[28rem] lg:h-[32rem] mt-4 md:mt-8 rounded-md object-cover"
          alt=""
        />

        <div className="flex flex-col lg:flex-row gap-10 lg:justify-between w-full mt-16">
          <div className="w-full ">
            <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy lg:w-[30.375rem] leading-[2.875rem]">{`How we work with our clients`}</h1>
            <Link
              to="/customer/signin"
              className="font-nunito text-darkNavy text-bodyMedium font-extrabold bg-green2 hover:bg-green1 px-8 py-3 lg:px-12 lg:py-4 rounded-xl tracking-wider"
            >
              Contact Us
            </Link>
          </div>
          <div className="">
            <p className="mb-4 font-nunito font-medium text-bodySmall px-4 lg:px-0 max-w-[34rem] lg:text-bodyLarge mt-2 lg:mt-5 lg:leading-6 text-darkTeal">
              Lorem ipsum dolor, sit amet consectetur adipisicing elit.
              Doloribus, ipsam?
            </p>
            <p className="mb-4 font-nunito font-medium text-bodySmall px-4 lg:px-0 max-w-[34rem] lg:text-bodyLarge mt-2 lg:mt-5 lg:leading-6 text-darkTeal">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Totam
              temporibus laudantium voluptatibus dolores. Ab, sit!
            </p>
            <p className="mb-4 font-nunito font-medium text-bodySmall px-4 lg:px-0 max-w-[34rem] lg:text-bodyLarge mt-2 lg:mt-5 lg:leading-6 text-darkTeal">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Omnis
              alias labore quasi!
            </p>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-10 lg:justify-between w-full mt-16">
          <img src={laptop} alt="" className="w-full md:w-1/2 rounded-lg" />
          <div className="w-full">
            <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy lg:w-[30.375rem] leading-[2.875rem]">{`Dream Until your Dream comes true`}</h1>
            <p className="mb-4 font-nunito font-medium text-bodySmall px-4 lg:px-0 max-w-[34rem] lg:text-bodyLarge mt-2 lg:mt-5 lg:leading-6 text-darkTeal">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Omnis
              alias labore quasi!
            </p>
            <Link
              to="/customer/signin"
              className="font-nunito text-darkNavy text-bodyMedium font-extrabold bg-green2 hover:bg-green1 px-8 py-3 lg:px-12 lg:py-4 rounded-xl tracking-wider"
            >
              Read more
            </Link>
          </div>
        </div>

        <div className="flex flex-col justify-center items-center mt-16">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy text-center leading-[2.875rem]">
            Our Core Values
          </h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-center text-darkTeal max-w-[32rem]">
            Our values shape the culture of organization and define the
            character of our company
          </p>
          <div className="grid grid-cols-2 gap-12 mt-12">
            {cardData.map((items, indx) => (
              <Cards data={items} key={indx} />
            ))}
          </div>
        </div>
        <div className="flex flex-col justify-center items-center mt-16">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy text-center leading-[2.875rem]">
            Our Global Presence
          </h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-center mb-8 text-darkTeal max-w-[50.375rem]">
            When you need us for improve your business, then come with us to
            help your then come have reach it, you just sit and feel that goal
          </p>
        </div>
        <WorldMap />
      </div>
    </>
  );
};

export default AboutPage;
