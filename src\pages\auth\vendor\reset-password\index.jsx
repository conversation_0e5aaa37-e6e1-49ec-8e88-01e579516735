import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useForm, Controller } from "react-hook-form";
import { AiFillEye, AiFillEyeInvisible, AiOutlineLoading3Quarters } from "react-icons/ai";
import resetPasswordImg from "@/assets/svg/signin.svg";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useResetPasswordVendorMutation } from "@/services/auth/mutation";

const VendorResetPasswordPage = () => {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      password: "",
      password_confirmation: "",
    },
  });

  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isPasswordReset, setIsPasswordReset] = useState(false);

  const resetPasswordMutation = useResetPasswordVendorMutation();

  // Get token and email from URL parameters
  const token = searchParams.get('token');
  const email = searchParams.get('email');

  const password = watch("password");

  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prevState) => !prevState);
  };

  const onSubmit = async (data) => {
    try {
      const response = await resetPasswordMutation.mutateAsync({
        token,
        email,
        password: data.password,
        password_confirmation: data.password_confirmation,
      });
      
      if (response.success) {
        setIsPasswordReset(true);
        toast({
          title: "Password Reset Successfully",
          description: "Your password has been reset. You can now sign in with your new password.",
          variant: "success",
          duration: 5000,
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to reset password. Please try again.",
          variant: "error",
          duration: 3000,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "error",
        duration: 3000,
      });
    }
  };

  // Check if token and email are present
  if (!token || !email) {
    return (
      <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
        <div className="w-full">
          <div className="text-center">
            <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">
              Invalid Reset Link
            </h1>
            <p className="font-nunito text-bodyMedium leading-5 text-blueGray mt-4">
              This password reset link is invalid or has expired. Please request a new password reset link.
            </p>
            <div className="mt-8">
              <Link
                to="/vendor/forgot-password"
                className="text-green1 font-nunito text-bodyMedium hover:underline"
              >
                Request New Reset Link
              </Link>
            </div>
          </div>
        </div>
        <div className="hidden sm:block w-full">
          <img
            className="sm:block hidden h-full"
            src={resetPasswordImg}
            alt="Reset Password Image"
            loading="lazy"
          />
        </div>
      </div>
    );
  }

  if (isPasswordReset) {
    return (
      <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
        <div className="w-full">
          <div className="text-center">
            <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">
              Password Reset Complete
            </h1>
            <p className="font-nunito text-bodyMedium leading-5 text-blueGray mt-4">
              Your password has been successfully reset. You can now sign in with your new password.
            </p>
            <div className="mt-8">
              <Link
                to="/vendor/signin"
                className="inline-block bg-green2 hover:bg-green1 text-darkNavy font-nunito font-extrabold px-8 py-3 rounded-xl tracking-wider"
              >
                Sign In Now
              </Link>
            </div>
          </div>
        </div>
        <div className="hidden sm:block w-full">
          <img
            className="sm:block hidden h-full"
            src={resetPasswordImg}
            alt="Reset Password Image"
            loading="lazy"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
      <div className="w-full">
        <div>
          <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">
            Reset Your Password
          </h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">
            Enter your new password below to complete the reset process.
          </p>
          <p className="font-nunito text-bodySmall leading-5 text-blueGray mt-2">
            Resetting password for: <strong>{email}</strong>
          </p>
        </div>
        
        <div className="lg:max-w-[30rem] mt-8">
          <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                New Password
              </label>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Password is required",
                  },
                  minLength: {
                    value: 8,
                    message: "Password must be at least 8 characters",
                  },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter new password"
                      autoComplete="new-password"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${
                        errors.password
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-[1.7rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.password && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Confirm New Password
              </label>
              <Controller
                name="password_confirmation"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Please confirm your password",
                  },
                  validate: (value) =>
                    value === password || "Passwords do not match",
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm new password"
                      autoComplete="new-password"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${
                        errors.password_confirmation
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={toggleConfirmPasswordVisibility}
                      className="absolute right-3 top-[1.7rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showConfirmPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.password_confirmation && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.password_confirmation.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            <div>
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
                type="submit"
                disabled={resetPasswordMutation.isPending}
              >
                {resetPasswordMutation.isPending ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Reset Password"
                )}
              </Button>
            </div>
          </form>
          
          <div className="flex items-center justify-center gap-2 mt-5">
            <span className="font-nunito text-bodySmall">
              Remember your password?
            </span>
            <Link
              to="/vendor/signin"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Back to Sign In
            </Link>
          </div>
        </div>
      </div>
      <div className="hidden sm:block w-full">
        <img
          className="sm:block hidden h-full"
          src={resetPasswordImg}
          alt="Reset Password Image"
          loading="lazy"
        />
      </div>
    </div>
  );
};

export default VendorResetPasswordPage;
