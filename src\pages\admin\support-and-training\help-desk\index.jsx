import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Reply, Send } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const SupportTrainingHelpDeskPage = () => {
  const [showReplyDialog, setShowReplyDialog] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [replyTicket, setReplyTicket] = useState(null);
  // Helpdesk state
  const [tickets, setTickets] = useState([
    {
      id: "TKT-001",
      user: "John Smith",
      summary: "Cannot access vendor dashboard",
      status: "Open",
      date: "2023-11-18",
      isOld: true,
    },
    {
      id: "TKT-002",
      user: "Sarah Johnson",
      summary: "Error when uploading specifications",
      status: "Open",
      date: "2023-11-20",
      isOld: false,
    },
    {
      id: "TKT-003",
      user: "Michael Brown",
      summary: "Need help with quotation comparison",
      status: "Resolved",
      date: "2023-11-15",
      isOld: false,
    },
    {
      id: "TKT-004",
      user: "Emily Davis",
      summary: "Login issues after password reset",
      status: "Open",
      date: "2023-11-19",
      isOld: false,
    },
  ]);

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);
      setUploadedFile(file);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);

        if (progress >= 100) {
          clearInterval(interval);
          setIsUploading(false);

          // Add the new resource
          const fileType = file.name.endsWith(".pdf")
            ? "PDF"
            : file.name.endsWith(".mp4") || file.name.endsWith(".mov")
            ? "Video"
            : "Document";

          const newResource = {
            id: resources.length + 1,
            name: file.name.split(".")[0],
            type: fileType,
            date: format(new Date(), "yyyy-MM-dd"),
            url: URL.createObjectURL(file),
            tags: [],
          };

          setResources([...resources, newResource]);

          toast({
            title: "Upload Complete",
            description: `${file.name} has been uploaded successfully.`,
          });
        }
      }, 300);
    }
  };

  // Handle ticket reply
  const handleTicketReply = () => {
    if (!replyText.trim()) {
      toast({
        title: "Error",
        description: "Reply cannot be empty",
        variant: "destructive",
      });
      return;
    }

    // Update ticket status
    const updatedTickets = tickets.map((ticket) =>
      ticket.id === replyTicket.id ? { ...ticket, status: "Resolved" } : ticket
    );

    setTickets(updatedTickets);
    setReplyText("");
    setShowReplyDialog(false);

    toast({
      title: "Reply Sent",
      description: `Your reply to ticket ${replyTicket.id} has been sent.`,
    });
  };
  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Helpdesk</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ticket ID</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Issue Summary</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tickets.map((ticket) => (
                <TableRow
                  key={ticket.id}
                  className={
                    ticket.isOld && ticket.status === "Open"
                      ? "border-l-4 border-l-red-500"
                      : ""
                  }
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      {ticket.isOld && ticket.status === "Open" && (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                      {ticket.id}
                    </div>
                  </TableCell>
                  <TableCell>{ticket.user}</TableCell>
                  <TableCell>{ticket.summary}</TableCell>
                  <TableCell>
                    <Badge
                      variant={ticket.status === "Open" ? "outline" : "success"}
                    >
                      {ticket.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{ticket.date}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setReplyTicket(ticket);
                        setShowReplyDialog(true);
                      }}
                      disabled={ticket.status === "Resolved"}
                    >
                      <Reply className="h-4 w-4 mr-2" />
                      Reply
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reply to Ticket {replyTicket?.id}</DialogTitle>
              <DialogDescription>
                Responding to: {replyTicket?.summary}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Label htmlFor="reply">Your Response</Label>
              <Textarea
                id="reply"
                placeholder="Type your reply here..."
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                rows={5}
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowReplyDialog(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleTicketReply}>
                <Send className="h-4 w-4 mr-2" />
                Send Reply
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default SupportTrainingHelpDeskPage;
