"use client";
import { useWindowWidth } from "@wojtekmaj/react-hooks";
import { useEffect, useState } from "react";

export default function useIsLaptop() {
  const windowWidth = useWindowWidth();
  const [isLaptop, setIsLaptop] = useState(false);
  useEffect(() => {
    if (windowWidth >= 1024) {
      setIsLaptop(true);
    } else {
      setIsLaptop(false);
    }
  }, [windowWidth]);

  return { isLaptop };
}
