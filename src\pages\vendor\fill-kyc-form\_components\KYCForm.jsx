import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON>, Controller, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Combobox } from "@/components/ui/Combobox";
import { kycFormSchema } from "@/schema/formSchema";
import {
  useDistrictQuery,
  useMunicipalityQuery,
} from "@/services/general/query";
import { useGetVendorBusinessCategory } from "@/services/vendor/query";
import { useKycVerificationRequestMutation } from "@/services/vendor/mutation";

export function KYCForm({ provinceData }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: zodResolver(kycFormSchema),
    defaultValues: {
      logo: "",
      email: "",
      phone: "",
      website: "",
      province: "",
      district: "",
      municipality: "",
      name: "",
      address: "",
      company_registration: "",
      vat_pan: "",
      tax_clearance: "",
      company_representative: "",
      business_type: "",
      // additional_information: '',
      consent: false,
    },
  });

  const kycVerificationRequestMutation = useKycVerificationRequestMutation();

  const selectedProvince = useWatch({ control, name: "province" });
  const selectedDistrict = useWatch({ control, name: "district" });
  const { data: districtData, isLoading: districtLoading } =
    useDistrictQuery(selectedProvince);
  const { data: municipalityData, isLoading: municipalityLoading } =
    useMunicipalityQuery(selectedDistrict);

  const businessTypeData = [
    {
      id: "1",
      name: "Manufacturer",
    },
    {
      id: "2",
      name: "Wholesaler",
    },
    {
      id: "3",
      name: "Retailer",
    },
    {
      id: "4",
      name: "Service Provider",
    },
    {
      id: "5",
      name: "Other",
    },
  ];

  useEffect(() => {
    setValue("district", "");
  }, [selectedProvince, setValue]);

  useEffect(() => {
    setValue("municipality", "");
  }, [selectedDistrict, setValue]);

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Here you would typically send the data to your backend
      console.log("Form data:", data);
      const response = await kycVerificationRequestMutation.mutateAsync(data);
      console.log(response);
      alert("KYC form submitted successfully!");
    } catch (error) {
      console.error("KYC form submission error:", error);
      alert("Error submitting KYC form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>KYC Verification Form</CardTitle>
        <CardDescription>
          Please fill out all required fields for KYC verification.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Company Details</h2>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="name">Business/Company Name</Label>
                  <Input
                    id="name"
                    placeholder="Your Business Name"
                    {...field}
                  />
                  {errors.name && (
                    <p className="text-red-500">{errors.name.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="logo"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <div>
                  <Label htmlFor="logo">Business Logo</Label>
                  <Input
                    id="logo"
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => onChange(e.target.files[0])}
                    {...field}
                  />
                  {value && (
                    <p className="text-sm text-gray-500">
                      Selected file: {value.name}
                    </p>
                  )}
                  {errors.logo && (
                    <p className="text-red-500">{errors.logo.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                  />
                  {errors.email && (
                    <p className="text-red-500">{errors.email.message}</p>
                  )}
                </div>
              )}
            />
            <Controller
              name="phone"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="phone">Contact Number</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+1234567890"
                    {...field}
                  />
                  {errors.phone && (
                    <p className="text-red-500">{errors.phone.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="address"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="address">Business Address</Label>
                  <Input
                    id="address"
                    type="tel"
                    placeholder="Enter your business address"
                    {...field}
                  />
                  {errors.address && (
                    <p className="text-red-500">{errors.address.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="website"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="website">Business Website</Label>
                  <Input
                    id="website"
                    type="tel"
                    placeholder="yourcompany.com"
                    {...field}
                  />
                  {errors.website && (
                    <p className="text-red-500">{errors.website.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="company_representative"
              control={control}
              render={({ field }) => (
                <div>
                  <Label htmlFor="company_representative">
                    Company Representative
                  </Label>
                  <Input
                    id="company_representative"
                    placeholder="As per official document"
                    {...field}
                  />
                  {errors.company_representative && (
                    <p className="text-red-500">
                      {errors.company_representative.message}
                    </p>
                  )}
                </div>
              )}
            />

            <Controller
              name="province"
              control={control}
              render={({ field }) => (
                <div className="flex flex-col gap-1">
                  <Label htmlFor="province">Select Province</Label>
                  <Combobox
                    id="province"
                    defaultPlaceholder="Select Province"
                    data={provinceData}
                    value={field.value}
                    setValue={field.onChange}
                  />
                  {errors.province && (
                    <p className="text-red-500">{errors.province.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="district"
              control={control}
              render={({ field }) => (
                <div className="flex flex-col gap-1">
                  <Label htmlFor="district">Select District</Label>
                  <Combobox
                    id="district"
                    defaultPlaceholder={
                      districtLoading
                        ? "Loading districts..."
                        : "Select District"
                    }
                    data={districtData?.data || []}
                    value={field.value}
                    setValue={field.onChange}
                    disabled={districtLoading || !selectedProvince}
                  />
                  {errors.district && (
                    <p className="text-red-500">{errors.district.message}</p>
                  )}
                </div>
              )}
            />

            <Controller
              name="municipality"
              control={control}
              render={({ field }) => (
                <div className="flex flex-col gap-1">
                  <Label htmlFor="municipality">Select Municipality</Label>
                  <Combobox
                    id="municipality"
                    defaultPlaceholder={
                      municipalityLoading
                        ? "Loading municipality..."
                        : "Select Municipality"
                    }
                    data={municipalityData?.data || []}
                    value={field.value}
                    setValue={field.onChange}
                    disabled={municipalityLoading || !selectedDistrict}
                  />
                  {errors.municipality && (
                    <p className="text-red-500">
                      {errors.municipality.message}
                    </p>
                  )}
                </div>
              )}
            />
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Identification Documents</h2>

            <Controller
              name="company_registration"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <div>
                  <Label htmlFor="company_registration">
                    Business Registration Certificate
                  </Label>
                  <Input
                    id="company_registration"
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => onChange(e.target.files[0])}
                    {...field}
                  />
                  {errors.company_registration && (
                    <p className="text-red-500">
                      {errors.company_registration.message}
                    </p>
                  )}
                </div>
              )}
            />
            <Controller
              name="vat_pan"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <div>
                  <Label htmlFor="vat_pan">VAT/PAN</Label>
                  <Input
                    id="vat_pan"
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => onChange(e.target.files[0])}
                    {...field}
                  />
                  {errors.vat_pan && (
                    <p className="text-red-500">{errors.vat_pan.message}</p>
                  )}
                </div>
              )}
            />
            <Controller
              name="tax_clearance"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <div>
                  <Label htmlFor="tax_clearance">
                    Tax Clearance Document (Optional)
                  </Label>
                  <Input
                    id="tax_clearance"
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => onChange(e.target.files[0])}
                    {...field}
                  />
                  {errors.tax_clearance && (
                    <p className="text-red-500">
                      {errors.tax_clearance.message}
                    </p>
                  )}
                </div>
              )}
            />
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Additional Information</h2>
            <Controller
              name="business_type"
              control={control}
              render={({ field }) => (
                <div className="flex flex-col gap-1">
                  <Label htmlFor="business_type">Business Type</Label>
                  <Combobox
                    id="business_type"
                    defaultPlaceholder={"Select Business Type"}
                    data={businessTypeData || []}
                    value={field.value}
                    setValue={field.onChange}
                  />
                  {errors.business_type && (
                    <p className="text-red-500">
                      {errors.business_type.message}
                    </p>
                  )}
                </div>
              )}
            />

            {/* <Controller
              name="additional_information"
              control={control}
              render={({ field: { onChange,value, ...field } }) => (
                <div>
                  <Label htmlFor="additional_information">Tax Clearance Document (Optional)</Label>
                  <Input
                    id="additional_information"
                    multiple
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => onChange(e.target.files)}
                    {...field}
                  />
                  {errors.additional_information && <p className="text-red-500">{errors.additional_information.message}</p>}
                </div>
              )}
            /> */}
          </div>

          <Controller
            name="consent"
            control={control}
            render={({ field }) => (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="consent"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
                <Label htmlFor="consent">
                  I hereby declare that the information provided is true and
                  correct.
                </Label>
                {errors.consent && (
                  <p className="text-red-500">{errors.consent.message}</p>
                )}
              </div>
            )}
          />

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit KYC Form"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
