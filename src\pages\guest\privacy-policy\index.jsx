import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";

export default function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-4xl font-bold mb-8 text-navy">Privacy Policy</h1>

      <Card className="mb-8">
        <CardContent className="prose max-w-none">
          <h2 className="text-2xl font-semibold mb-4 text-navy">
            1. Information Collection
          </h2>
          <p className="mb-4 text-darkTeal">
            We collect information you provide directly to us when you use our
            services. This may include personal information such as your name,
            email address, and any other information you choose to provide.
          </p>

          <h2 className="text-2xl font-semibold mb-4 text-navy">
            2. Use of Information
          </h2>
          <p className="mb-4 text-darkTeal">
            We use the information we collect to provide, maintain, and improve
            our services, to develop new ones, and to protect our company and
            our users.
          </p>

          <h2 className="text-2xl font-semibold mb-4 text-navy">
            3. Information Sharing
          </h2>
          <p className="mb-4 text-darkTeal">
            We do not share personal information with companies, organizations,
            or individuals outside of our company except in the following cases:
            with your consent, for legal reasons, or to protect rights, property
            or safety.
          </p>

          <p className="mt-8 text-darkTeal">
            For our Terms of Service, please visit our{" "}
            <Link
              href="/terms-of-service"
              className="text-navy hover:underline"
            >
              Terms of Service page
            </Link>
            .
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
