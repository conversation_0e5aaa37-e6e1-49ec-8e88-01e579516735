{"name": "zetabid", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.3", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.61.5", "@tanstack/react-query-devtools": "^5.61.5", "@tanstack/react-table": "^8.20.5", "@wojtekmaj/react-hooks": "^1.21.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "framer-motion": "^11.11.9", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.441.0", "moment": "^2.30.1", "motion": "^11.15.0", "next-themes": "^0.4.6", "quill": "^2.0.2", "react": "^18.3.1", "react-day-picker": "^9.4.4", "react-dom": "^18.3.1", "react-form-stepper": "^2.0.3", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-spinners": "^0.14.1", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "rollup": "^4.37.0", "tailwindcss": "^3.4.12", "vite": "^5.4.1"}}