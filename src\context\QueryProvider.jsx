import React from "react";
import PropTypes from "prop-types";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

const QueryProvider = ({ children }) => {
  const [queryClient] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
            cacheTime: 30 * 60 * 1000, // Cache data for 30 minutes
            retry: 2, // Retry failed queries up to 2 times
            refetchOnWindowFocus: true, // Refetch on window focus
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
QueryProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default QueryProvider;
