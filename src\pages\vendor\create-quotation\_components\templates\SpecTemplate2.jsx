import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import React from "react";
import watch from "@/assets/jpg/watch.jpeg";

const SpecTemplate2 = ({ currentSpec, handleChange }) => {
  return (
    <div className="bg-white p-6 w-full max-w-3xl rounded-md shadow-lg mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 bg-zinc-200 px-2 py-1 rounded-md">
        <h1 className="text-xl font-bold">Product Specifications</h1>
        <div className="text-sm text-gray-500">4th Jan, 2025</div>
      </div>

      {/* Product Overview */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4 border-b">Product Overview</h2>

        <div className="space-y-4">
          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Product name</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="SenseVitae Smart Watch"
            />
          </div>

          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Product code</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="S2457WR"
            />
          </div>

          <div className="border-b">
            <div className="mb-2 font-bold">Product description</div>
            <Textarea
              className="p-0 border-0 text-base focus:outline-none min-h-[100px] resize-none"
              defaultValue="This device is designed to help users track their fitness goals and monitor their health goals. Using an advanced sensor technology, users can monitor their daily activity levels, heart rate, and sleep patterns—all in one device. Data collected from this device can be connected with the SenseVitae app for comprehensive health monitoring."
            />
          </div>

          <div className="border-b">
            <div className=" font-bold mb-2">Purpose</div>
            <Textarea
              className="text-base p-0 border-0 focus:outline-none min-h-[60px] resize-none"
              defaultValue="This device allows users to track their heart rate, calories, steps, sleep patterns, and other health metrics in real-time. They can also set fitness goals and monitor their progress at a glance."
            />
          </div>
        </div>
      </div>

      {/* Product Image(s) */}
      <div className="mb-8 border-b">
        <h2 className="text-lg font-bold mb-4">Product Image(s)</h2>
        <div className="flex gap-4">
          <div className="w-24 h-24 bg-gray-100 border rounded">
            <img
              src={watch}
              alt="Photo 1"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="w-24 h-24 bg-gray-100 border rounded">
            <img
              src={watch}
              alt="Photo 2"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        <div className="flex gap-4 mt-2 text-sm text-gray-500">
          <div className="w-24 text-center">Photo 1</div>
          <div className="w-24 text-center">Photo 2</div>
        </div>
      </div>

      {/* Design Specifications */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Design Specifications</h2>

        <div className="space-y-4">
          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Dimensions</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="40 x 35 x 10 mm"
            />
          </div>

          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Weight</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="35 g"
            />
          </div>

          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Materials</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="Polycarbonate casing with silica strap"
            />
          </div>
        </div>
      </div>

      {/* Packaging specifications */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Packaging specifications</h2>
        <div className="flex items-center gap-2 border-b">
          <div className="font-bold">Details</div>
          <Input
            className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
            defaultValue="N/A"
          />
        </div>
      </div>

      {/* Labeling specifications */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Labeling specifications</h2>
        <div className="flex items-center gap-2 border-b">
          <div className="font-bold">Details</div>
          <Input
            className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
            defaultValue="N/A"
          />
        </div>
      </div>

      {/* Additional information */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Additional information</h2>
        <div className="space-y-4">
          <div className="flex items-center gap-2 border-b">
            <div className="font-bold">Screen size</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="1.35 in"
            />
          </div>
          <div className="flex items-center gap-2 border-b">
            <div className=" font-bold">Screen resolution</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="240 x 432 px"
            />
          </div>
          <div className="flex items-center gap-2 border-b">
            <div className=" font-bold">Operating system</div>
            <Input
              className="flex-1 h-4 text-right text-base border-0 focus:outline-none"
              defaultValue="Android"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpecTemplate2;
