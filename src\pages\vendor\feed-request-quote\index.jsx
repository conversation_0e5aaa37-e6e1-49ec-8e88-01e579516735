import React from 'react'
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import QuotationRequestForm from './_components/QuotationRequestForm';

const FeedRequestQuote = () => {
  return (
    <div>
        <header className="flex h-12 shrink-0 items-center gap-2">
                <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <Breadcrumb className="font-nunito">
                        <BreadcrumbList>
                            <BreadcrumbItem className="hidden md:block">
                                <BreadcrumbLink href={`#`}>
                                    Request Quote
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
            </header>

            <div>
                <QuotationRequestForm />
            </div>
    </div>
  )
}

export default FeedRequestQuote