/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,jsx}",
    "./components/**/*.{js,jsx}",
    "./app/**/*.{js,jsx}",
    "./src/**/*.{js,jsx}",
  ],
  theme: {
    screens: {
      xs: "375px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
      "3xl": "1600px",
    },
    container: {
      center: "true",
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontSize: {
        heading1: "6rem",
        heading2: "3.75rem",
        heading3: "2.75rem",
        heading4: "2.25rem",
        heading5: "2rem",
        heading6: "1.25rem",
        bodyLarge: "1.125rem",
        bodyMedium: "1rem",
        bodySmall: "0.875rem",
        textSmall: "0.75rem",
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
        nunito: ["Nunito Sans", "sans-serif"],
        inter: ["Inter", "sans-serif"],
      },
      fontWeight: {
        light: "300",
        regular: "400",
        medium: "500",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        green1: "var(--green1)",
        green2: "var(--green2)",
        green3: "var(--green3)",
        green4: "var(--green4)",
        darkNavy: "var(--dark-navy)",
        navy: "var(--navy)",
        darkBrown: "var(--dark-brown)",
        blueGray: "var(--blue-gray)",
        darkTeal: "var(--dark-teal)",
        mintGreen: "var(--mint-green)",
        lightGray1: "var(--light-gray1)",
        lightBrown: "var(--light-brown)",
        lightGray2: "var(--light-gray2)",
        gold1: "var(--gold1)",
        yellow1: "var(--yellow1)",
        yellow2: "var(--yellow2)",
        darkLight: "var(--dark-light)",
        darkMedium: "var(--dark-medium)",
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      backgroundImage: {
        "custom-gradient":
          "linear-gradient(150deg, rgba(255,255,255,1) 0%, rgba(211,246,228,0.78) 41%, rgba(132,231,178,0.71) 100%)",
        "custom-gradient2":
          "linear-gradient(140deg, rgba(255,255,255,1) 0%, rgba(211,246,228,0.78) 21%, rgba(132,231,178,0.71) 100%)",
      },
      boxShadow: {
        "custom-shadow": "0 0 5.5px 0 rgba(20, 20, 43, 0.08)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [],
};
