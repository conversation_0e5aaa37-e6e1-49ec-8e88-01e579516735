"use client";

import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export default function DocumentUploadForm({
  onNext,
  onPrevious,
  initialData,
  companyType,
}) {
  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: initialData || { idProof: null, addressProof: null },
  });

  const onSubmit = (data) => {
    onNext({ documents: data });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="idProof">Company registration Doc.</Label>
          <Controller
            name="idProof"
            control={control}
            rules={{ required: "ID Proof is required" }}
            render={({ field }) => (
              <Input
                id="idProof"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.idProof && (
            <p className="text-red-500">{errors.idProof.message}</p>
          )}
        </div>

        <div className="w-1/2">
          <Label htmlFor="vat">VAT/PAN</Label>
          <Controller
            name="vat"
            control={control}
            rules={{ required: "VAT/PAN Proof is required" }}
            render={({ field }) => (
              <Input
                id="vat"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.vat && <p className="text-red-500">{errors.vat.message}</p>}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="tax">Tax Clearance</Label>
          <Controller
            name="tax"
            control={control}
            rules={{ required: "Tax Clearance is required" }}
            render={({ field }) => (
              <Input
                id="tax"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.tax && <p className="text-red-500">{errors.tax.message}</p>}
        </div>

        <div className="w-1/2">
          <Label htmlFor="audit">Audit Report</Label>
          <Controller
            name="audit"
            control={control}
            rules={{ required: "Audit Report is required" }}
            render={({ field }) => (
              <Input
                id="audit"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.audit && (
            <p className="text-red-500">{errors.audit.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="logo">Company Logo</Label>
          <Controller
            name="logo"
            control={control}
            rules={{ required: "Company Logo is required" }}
            render={({ field }) => (
              <Input
                id="logo"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.logo && <p className="text-red-500">{errors.logo.message}</p>}
        </div>

        <div className="w-1/2">
          <Label htmlFor="stamp">Company Stamp</Label>
          <Controller
            name="stamp"
            control={control}
            rules={{ required: "Company Stamp is required" }}
            render={({ field }) => (
              <Input
                id="stamp"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.stamp && (
            <p className="text-red-500">{errors.stamp.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-8">
        <div className="w-1/2">
          <Label htmlFor="sign">Authorized Signature</Label>
          <Controller
            name="sign"
            control={control}
            rules={{ required: "Authorized Signature is required" }}
            render={({ field }) => (
              <Input
                id="sign"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.sign && <p className="text-red-500">{errors.sign.message}</p>}
        </div>

        <div className="w-1/2">
          <Label htmlFor="firstName">Website</Label>
          <Input
            id="website"
            {...register("website", { required: "Website is required" })}
          />
          {errors.website && (
            <p className="text-red-500">{errors.website.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onPrevious}
          className="w-1/2"
        >
          Previous
        </Button>
        <Button type="submit" className="w-1/2">
          Next
        </Button>
      </div>
    </form>
  );
}
