import { useState, useRef } from "react";
import { Search, Plus, Edit, Trash2, X } from "lucide-react";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import {
  useCreateNotice,
  useDeleteNotice,
  useGetNotices,
  useUpdateNotice,
} from "@/services/admin/query";

export default function NoticeManagementPage() {
  const { data: noticesData, isLoading, isError, refetch } = useGetNotices();
  const createNoticeMutation = useCreateNotice();
  const updateNoticeMutation = useUpdateNotice();
  const deleteNoticeMutation = useDeleteNotice();
  const notices = Array.isArray(noticesData?.data) ? noticesData.data : [];

  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentNotice, setCurrentNotice] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const fileInputRef = useRef(null);

  const [newNotice, setNewNotice] = useState({
    id: "",
    title: "",
    description: "",
    image: null,
  });

  const filteredNotices = notices.filter((notice) =>
    notice.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddNotice = () => {
    setIsEditMode(false);
    setNewNotice({ id: "", title: "", description: "", image: null });
    setIsAddDialogOpen(true);
  };

  const handleEditNotice = (notice, e) => {
    e.stopPropagation();
    setIsEditMode(true);
    setNewNotice({
      id: notice.id,
      title: notice.title,
      description: notice.description,
      image: notice.image || null,
    });
    setIsAddDialogOpen(true);
  };

  const handleSaveNotice = () => {
    if (!newNotice.title.trim() || !newNotice.description.trim()) {
      toast({
        title: "Error",
        description: "Title and description are required.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("title", newNotice.title.trim());
    formData.append("description", newNotice.description.trim());
    if (newNotice.image instanceof File) {
      formData.append("attachment", newNotice.image);
    }

    if (isEditMode) {
      updateNoticeMutation.mutate(
        { id: newNotice.id, data: formData },
        {
          onSuccess: () => {
            toast({ title: "Success", description: "Notice updated." });
            setIsAddDialogOpen(false);
            refetch();
          },
          onError: (error) => {
            toast({
              title: "Error",
              description: error.response?.data?.message || "Failed to update.",
              variant: "destructive",
            });
          },
        }
      );
    } else {
      createNoticeMutation.mutate(formData, {
        onSuccess: () => {
          toast({ title: "Success", description: "Notice created." });
          setIsAddDialogOpen(false);
          refetch();
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: error.response?.data?.message || "Failed to create.",
            variant: "destructive",
          });
        },
      });
    }
  };

  const handleDeleteClick = (notice, e) => {
    e.stopPropagation();
    setCurrentNotice(notice);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    deleteNoticeMutation.mutate(currentNotice.id, {
      onSuccess: () => {
        toast({ title: "Deleted", description: "Notice removed." });
        setIsDeleteDialogOpen(false);
        refetch();
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to delete.",
          variant: "destructive",
        });
      },
    });
  };

  const handleViewNotice = (notice) => {
    setCurrentNotice(notice);
    setIsViewDialogOpen(true);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewNotice((prev) => ({ ...prev, image: file }));
    }
  };

  const handleImageClick = () => {
    fileInputRef.current.click();
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between pb-4">
          <CardTitle className="text-2xl font-bold">Notice Management</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search notices..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={handleAddNotice}>
              <Plus className="h-4 w-4 mr-2" /> Add Notice
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-10">Loading notices...</div>
          ) : isError ? (
            <div className="text-center py-10 text-red-500">
              Error loading notices.
            </div>
          ) : filteredNotices.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              No notices found. Add one to get started.
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredNotices.map((notice) => (
                <Card
                  key={notice.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleViewNotice(notice)}
                >
                  <div className="aspect-video relative overflow-hidden rounded-t-lg">
                    <img
                      src={
                        notice.attachment
                      }
                      alt={notice.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2 flex space-x-1">
                      <Button
                        variant="secondary"
                        size="icon"
                        className="h-8 w-8 bg-white/80 hover:bg-white"
                        onClick={(e) => handleEditNotice(notice, e)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        className="h-8 w-8 bg-red-500 hover:bg-red-600"
                        onClick={(e) => handleDeleteClick(notice, e)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardContent className="pt-4">
                    <h3 className="font-semibold text-lg line-clamp-1">
                      {notice.title}
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {format(
                        new Date(notice.created_at || notice.date),
                        "MMMM dd, yyyy"
                      )}
                    </p>
                    <p className="mt-2 text-sm line-clamp-2">
                      {notice.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add / Edit Notice Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit Notice" : "Add New Notice"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the notice details."
                : "Fill in the notice details."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={newNotice.title}
                onChange={(e) =>
                  setNewNotice((prev) => ({ ...prev, title: e.target.value }))
                }
                placeholder="Enter notice title"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newNotice.description}
                onChange={(e) =>
                  setNewNotice((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={4}
                placeholder="Enter notice description"
              />
            </div>
            <div className="grid gap-2">
              <Label>Image</Label>
              <div
                className="border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={handleImageClick}
              >
                {newNotice.image ? (
                  <div className="relative">
                    <img
                      src={
                        newNotice.image instanceof File
                          ? URL.createObjectURL(newNotice.image)
                          : newNotice.image
                      }
                      alt="Notice"
                      className="mx-auto max-h-[200px] object-contain"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        setNewNotice((prev) => ({ ...prev, image: null }));
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="py-8 text-muted-foreground">
                    Click to upload image
                  </div>
                )}
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveNotice}
              disabled={
                createNoticeMutation.isPending ||
                updateNoticeMutation.isPending
              }
            >
              {createNoticeMutation.isPending ||
              updateNoticeMutation.isPending
                ? "Saving..."
                : isEditMode
                ? "Update Notice"
                : "Add Notice"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Notice Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        {currentNotice && (
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>{currentNotice.title}</DialogTitle>
              <DialogDescription>
                Posted on{" "}
                {format(
                  new Date(currentNotice.created_at || currentNotice.date),
                  "MMMM dd, yyyy"
                )}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <img
                src={currentNotice.image || "/placeholder.svg"}
                className="w-full rounded-lg object-cover max-h-[300px]"
                alt={currentNotice.title}
              />
              <p className="text-sm">{currentNotice.description}</p>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsViewDialogOpen(false);
                  handleEditNotice(currentNotice, e);
                }}
              >
                Edit
              </Button>
              <Button variant="default" onClick={() => setIsViewDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this notice? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
