import Cookies from "js-cookie";
import CryptoJS from "crypto-js";

const secretKey = import.meta.env.VITE_SECRET_KEY;

function encryptData(data) {
  if (!data) {
    console.error("Invalid data to encrypt.");
    return null;
  }

  if (!secretKey) {
    console.error("Secret key is not defined.");
    return null;
  }

  try {
    const stringData = typeof data === "string" ? data : JSON.stringify(data);
    return CryptoJS.AES.encrypt(stringData, secretKey).toString();
  } catch (error) {
    console.error("Error encrypting data:", error);
    return null;
  }
}

function decryptData(encryptedData) {
  if (!encryptedData) {
    console.error("Invalid encrypted data.");
    return null;
  }

  if (!secretKey) {
    console.error("Secret key is not defined.");
    return null;
  }

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    return decrypted;
  } catch (error) {
    console.error("Error decrypting data:", error);
    return null;
  }
}

export const setCookies = (data) => {
  const { accessToken, userId, userType, name, email } = data;

  try {
    // Determine cookie prefix based on user type
    const prefix = getUserTypePrefix(userType);

    // Encrypt data
    const encryptedAccessToken = encryptData(accessToken);
    const encryptedUserId = encryptData(userId);

    // Store encrypted data in cookies with user type prefix
    if (encryptedAccessToken) {
      Cookies.set(`${prefix}accessToken`, encryptedAccessToken, {
        expires: 1, // Expires in 1 day
        secure: true, // Ensures cookies are transmitted over HTTPS
        sameSite: "Strict",
      });
    }

    if (encryptedUserId) {
      Cookies.set(`${prefix}userId`, encryptedUserId, {
        expires: 1,
        secure: true,
        sameSite: "Strict",
      });
    }

    Cookies.set(`${prefix}userType`, userType, {
      expires: 1,
      secure: true,
      sameSite: "Strict",
    });

    // Store additional user info if provided
    if (name) {
      Cookies.set(`${prefix}name`, name, {
        expires: 1,
        secure: true,
        sameSite: "Strict",
      });
    }

    if (email) {
      Cookies.set(`${prefix}email`, email, {
        expires: 1,
        secure: true,
        sameSite: "Strict",
      });
    }

    console.log(`Cookies set successfully for ${userType} with prefix: ${prefix}`);
  } catch (error) {
    console.error("Error setting cookies:", error);
  }
};

// Helper function to get cookie prefix based on user type
const getUserTypePrefix = (userType) => {
  switch (userType) {
    case "user": // customer type is "user" in the system
      return "customer_";
    case "vendor":
      return "vendor_";
    case "admin":
      return "admin_";
    default:
      console.warn(`Unknown user type: ${userType}, using default prefix`);
      return "default_";
  }
};

// Get cookies for a specific user type
export const getCookiesByUserType = (userType) => {
  const prefix = getUserTypePrefix(userType);

  return {
    accessToken: Cookies.get(`${prefix}accessToken`),
    userId: Cookies.get(`${prefix}userId`),
    userType: Cookies.get(`${prefix}userType`),
    name: Cookies.get(`${prefix}name`),
    email: Cookies.get(`${prefix}email`),
  };
};

// Get decrypted access token for a specific user type
export const getAccessTokenByUserType = (userType) => {
  const prefix = getUserTypePrefix(userType);
  const encryptedToken = Cookies.get(`${prefix}accessToken`);
  return encryptedToken ? decryptData(encryptedToken) : null;
};

// Get decrypted user ID for a specific user type
export const getUserIdByUserType = (userType) => {
  const prefix = getUserTypePrefix(userType);
  const encryptedUserId = Cookies.get(`${prefix}userId`);
  return encryptedUserId ? decryptData(encryptedUserId) : null;
};

// Clear cookies for a specific user type
export const clearCookiesByUserType = (userType) => {
  const prefix = getUserTypePrefix(userType);

  Cookies.remove(`${prefix}accessToken`);
  Cookies.remove(`${prefix}userId`);
  Cookies.remove(`${prefix}userType`);
  Cookies.remove(`${prefix}name`);
  Cookies.remove(`${prefix}email`);

  console.log(`Cookies cleared for ${userType} with prefix: ${prefix}`);
};

// Check if user is authenticated for a specific user type
export const isAuthenticatedByUserType = (userType) => {
  const cookies = getCookiesByUserType(userType);
  return !!(cookies.accessToken && cookies.userId && cookies.userType);
};

// Get all authenticated user types (for multi-session support)
export const getAuthenticatedUserTypes = () => {
  const userTypes = ["user", "vendor", "admin"]; // customer type is "user"
  return userTypes.filter(userType => isAuthenticatedByUserType(userType));
};

export { encryptData, decryptData };
