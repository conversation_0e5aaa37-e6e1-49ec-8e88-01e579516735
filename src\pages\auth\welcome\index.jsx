import { Link } from "react-router-dom";
import signin from "@/assets/webp/signinimage.webp";
import ChooseOrganizationOrIndividual from "@/pages/auth/welcome/_components/ChooseOrganizationOrIndividual";
import ChooseCustomerOrVendor from "@/pages/auth/welcome/_components/ChooseCustomerOrVendor";
import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";

const WelcomePage = () => {
  const { setup } = useAppSelector((state) => state.auth || {});

  const dispatch = useAppDispatch();

  const [step, setStep] = useState();

  useEffect(() => {
    setStep(setup.step);
  }, [setup.step]);

  const handleNext = () => {
    setStep(step + 1);
    dispatch(setSetup({ ...setup, customerOrVendor: "vendor", step: 2 }));
  };

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 min-h-[30rem] h-[calc(100svh-15rem)] items-center sm:mt-20">
      <div className="w-full py-5">
        <div>
          <h1 className="font-poppins font-semibold text-heading4 sm:text-heading3 leading-[50px] text-navy">{`Create an Account`}</h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">{`Register with Zettabid`}</p>
        </div>
        <div className="lg:max-w-[30rem] mt-5">
          <div>
            {step === 1 && <ChooseCustomerOrVendor onNext={handleNext} />}
            {step === 2 && <ChooseOrganizationOrIndividual />}
          </div>
          <div className="flex items-end gap-2 mt-5">
            <span className="font-nunito text-bodySmall">
              Already have an account?
            </span>
            <Link
              to="/signin"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Sign in
            </Link>
          </div>
        </div>
      </div>
      <div className="hidden sm:block w-full h-full">
        <img
          className="sm:block hidden h-full object-cover"
          src={signin}
          alt="Signin Image"
        />
      </div>
    </div>
  );
};

export default WelcomePage;
