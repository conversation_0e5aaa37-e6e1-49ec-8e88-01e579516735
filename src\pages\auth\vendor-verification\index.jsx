import { useAppSelector } from "@/hooks/StoreHooks";
import Steps from "@/pages/auth/vendor-verification/_components/Steps";
import BasicDetailsForm from "@/pages/auth/vendor-verification/_components/BasicDetailsForm";
import ContactInfoForm from "@/pages/auth/vendor-verification/_components/ContactInfoForm";
import BusinessInfoForm from "@/pages/auth/vendor-verification/_components/BusinessInfoForm";
import AdditionalInfoForm from "@/pages/auth/vendor-verification/_components/AdditionalInfoForm";
import SubmitVerification from "@/pages/auth/vendor-verification/_components/SubmitVerification";

const VendorVerificationPage = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  console.log(setup);
  return (
    <div className="px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 flex flex-col justify-center w-full">
      <div className="flex justify-center">
        <Steps />
      </div>
      <div className="mt-5 sm:mt-0 flex flex-col items-center">
        {setup?.step === 1 && <BasicDetailsForm />}
        {setup?.step === 2 && <ContactInfoForm />}
        {setup?.step === 3 && <BusinessInfoForm />}
        {setup?.step === 4 && <AdditionalInfoForm />}
        {setup?.step === 5 && <SubmitVerification />}
      </div>
    </div>
  );
};

export default VendorVerificationPage;
