import logo from "@/assets/png/logo.png";
import { <PERSON> } from "react-router-dom";
import { FiFacebook } from "react-icons/fi";
import { FaInstagram } from "react-icons/fa";
import { FaTiktok } from "react-icons/fa";
import { FiYoutube } from "react-icons/fi";

const FooterSection = () => {
  return (
    <div className="py-24 flex flex-col lg:flex-row lg:gap-40 xl:gap-64 px-4 sm:px-8 xl:px-32">
      <div className="space-y-5 lg:min-w-[15rem]">
        <div>
          <img
            src={logo}
            alt="Zettabid"
            className="object-contain w-[12rem] h-full"
            loading="lazy"
          />
        </div>
        <div>
          <span>{`Streamline your quotation with ease`}</span>
        </div>
        <div className="flex items-center gap-3">
          <Link to="/">
            <FiFacebook className="text-3xl text-darkTeal h-5" />
          </Link>
          <Link to="/">
            <FaInstagram className="text-3xl text-darkTeal h-5" />
          </Link>
          <Link to="/">
            <FaTiktok className="text-3xl text-darkTeal h-5" />
          </Link>
          <Link to="/">
            <FiYoutube className="text-3xl text-darkTeal h-5" />
          </Link>
        </div>
        <div>
          <span>{`<EMAIL>`}</span>
        </div>
      </div>
      <div className="flex flex-col sm:flex-row w-full mt-10 lg:mt-0 gap-10 lg:gap-0">
        <div className="w-full">
          <h2 className="font-poppins text-bodyLarge">{`Zettabid`}</h2>
          <div className="flex flex-col gap-3 mt-5">
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Start for free
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Login
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              About
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Resource
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Template
            </Link>
          </div>
        </div>
        <div className="w-full">
          <h2 className="font-poppins text-bodyLarge">{`Support`}</h2>
          <div className="flex flex-col gap-3 mt-5">
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Customer Server
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              {`+977 9812345678`}
            </Link>
            <Link
              to="/"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Report a Bug
            </Link>
            <Link
              to="/contact"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Contact
            </Link>
          </div>
        </div>
        <div className="w-full">
          <h2 className="font-poppins text-bodyLarge">{`Legal`}</h2>
          <div className="flex flex-col gap-3 mt-5">
            <Link
              to="/terms-of-service"
              className="text-darkTeal hover:text-green1 font-semibold text-nowrap"
            >
              Terms of Service
            </Link>
            <Link
              to="/privacy-policy"
              className="text-darkTeal hover:text-green1 font-semibold"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterSection;
