// Categories and subcategories mapping
export const categoriesData = {
  Manufacturing: [
    "Metal Fabrication",
    "Plastic Molding",
    "Precision Machining",
    "3D Printing",
    "CNC Machining",
    "Sheet Metal",
  ],
  Electronics: ["PCB Assembly", "Component Manufacturing", "Circuit Design", "Testing & QA", "Embedded Systems"],
  Software: ["Web Development", "Mobile Development", "API Development", "Cloud Services", "AI & Machine Learning"],
  Packaging: [
    "Sustainable Packaging",
    "Retail Packaging",
    "Industrial Packaging",
    "Food Packaging",
    "Custom Packaging",
  ],
  Textiles: [
    "Fabric Manufacturing",
    "Garment Production",
    "Technical Textiles",
    "Dyeing & Finishing",
    "Textile Printing",
  ],
}

// Get all unique categories
export const getCategories = () => {
  return Object.keys(categoriesData)
}

// Get subcategories for a specific category
export const getSubcategories = (category) => {
  return categoriesData[category] || []
}
