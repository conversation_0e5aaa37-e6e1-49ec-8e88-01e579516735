import { Button } from "@/components/ui/button";
import { Controller, useForm } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { useNavigate } from "react-router-dom";

const ChooseOrganizationOrIndividual = () => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      individualOrOrganization: "individual",
    },
  });
  const navigate = useNavigate();
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();

  const onSubmit = (data) => {
    console.log(data);
    reset();
    dispatch(
      setSetup({
        ...setup,
        organizationOrIndividual: data?.individualOrOrganization,
        step: 1,
      })
    );
    navigate("/verification");
  };

  return (
    <div>
      <form className="space-y-6 mt-10" onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name="individualOrOrganization"
          control={control}
          rules={{
            required: {
              value: true,
              message: "Choose one option",
            },
          }}
          render={({ field }) => (
            <>
              <RadioGroup onValueChange={field.onChange} value={field.value}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="individual" id="r1" />
                  <Label htmlFor="r1" className="text-md">
                    Individual
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="organization" id="r2" />
                  <Label htmlFor="r2" className="text-md">
                    Organization
                  </Label>
                </div>
              </RadioGroup>
              {errors?.individualOrOrganization && (
                <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                  {errors?.individualOrOrganization?.message}
                </p>
              )}
            </>
          )}
        />

        <div>
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
            type="submit"
          >
            Continue
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ChooseOrganizationOrIndividual;
