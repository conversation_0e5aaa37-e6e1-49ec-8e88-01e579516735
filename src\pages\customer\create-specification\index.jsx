import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { CategoryStep } from "./_components/steps/CategoryStep";
import { ProductStep } from "./_components/steps/ProductStep";
import { SpecificationList } from "./_components/SpecificationList";
import { But<PERSON> } from "@/components/ui/button";
import { useSpecification } from "@/context/SpecificationContext";
import { isStepComplete } from "@/utils/specCreateStepCompletion";
import { useNavigate } from "react-router-dom";
import { useExcelUpload } from "@/hooks/useExcelUpload";
import EditableTable from "@/components/table/EditableTable";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { fixedFields } from "@/utils/specProductFields";

const CreateSpecification = () => {
  const navigate = useNavigate();

  const {
    currentSpec,
    specifications,
    currentStep,
    selectedTemplate,
    handleCategoryChange,
    handleProductChange,
    handleSpecChange,
    handleAddSpecification,
    handleRemoveSpecification,
    handleFinalize,
    removeQueryParam,
    setCurrentStep,
    specificationType,
    setSpecificationType,
    specificationProductData,
    setSpecificationProductData,
  } = useSpecification();

  const { data, error, setData, handleFileUpload } = useExcelUpload();

  const handleSpecificationType = (type) => {
    setSpecificationType(type);
  };

  const handleNext = () => {
    // Validate required fields
    if (!specificationType) {
      alert("Please select a specification type first.");
      return;
    }

    if (!currentSpec.category) {
      alert("Please select a category first.");
      return;
    }

    if (!currentSpec.subcategory) {
      alert("Please select a subcategory first.");
      return;
    }

    if (!data || data.length === 0) {
      alert("Please add at least one product specification.");
      return;
    }

    // Create complete specification data including category/subcategory
    const completeSpecificationData = {
      category: currentSpec.category,
      categoryId: currentSpec.categoryId,
      subCategory: currentSpec.subcategory,
      subcategoryId: currentSpec.subcategoryId,
      specificationType: specificationType,
      specificationProductData: data,
      createdAt: new Date().toISOString(),
    };

    console.log("Complete specification data:", completeSpecificationData);
    setSpecificationProductData(completeSpecificationData);
    navigate(`/customer/1/create-specification/preview`);
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Create Specifications</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      {!specificationType ? (
        <div className="p-4 flex flex-col gap-4">
          <h4 className="text-lg font-medium">
            Do you want to create specification?
          </h4>
          <p>Please select one of the following specification type.</p>
          <div className="grid grid-cols-2 gap-6">
            <div className="flex flex-col justify-center gap-3 p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
              <h2 className="text-xl font-bold">Product</h2>
              <p>
                Outline product features, including design, materials,
                dimensions, and performance.
              </p>
              <button
                onClick={() => handleSpecificationType("products")}
                className="w-max bg-primary flex items-center font-extrabold tracking-wider px-6 py-3 rounded-xl"
              >
                Select
                <svg
                  className="rtl:rotate-180 w-3.5 h-3.5 ms-2"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 14 10"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M1 5h12m0 0L9 1m4 4L9 9"
                  />
                </svg>
              </button>
            </div>
            <div className="flex flex-col justify-center gap-3 p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
              <h2 className="text-xl font-bold">Service</h2>
              <p>
                Outline service details such as process, scope, deliverables,
                and performance expectations.
              </p>
              <button
                onClick={() => handleSpecificationType("services")}
                className="w-max bg-primary flex items-center font-extrabold tracking-wider px-6 py-3 rounded-xl"
              >
                Select
                <svg
                  className="rtl:rotate-180 w-3.5 h-3.5 ms-2"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 14 10"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M1 5h12m0 0L9 1m4 4L9 9"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="p-4 pt-0">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
              <CategoryStep
                currentSpec={currentSpec}
                onCategoryChange={handleCategoryChange}
                onSubCategoryChange={handleProductChange}
              />

              <div className="col-span-2 flex flex-col gap-2">
                {/* <h2 className="text-xl font-bold">
                  Upload Execl Data or add product details
                </h2>
                <p className="text-sm max-w-[500px]">
                  Please upload an Excel file with the following headers:{" "}
                  {fixedFields.map((item, index) => (
                    <strong className="mr-1">{`${item.label}${
                      index < fixedFields.length - 1 ? "," : "."
                    }`}</strong>
                  ))}
                  . You may also add rows manually.
                </p>
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="picture">Picture</Label>
                  <Input
                    id="picture"
                    type="file"
                    accept=".xlsx, .xls, .csv"
                    onChange={handleFileUpload}
                  />
                </div> */}
                {error && <div style={{ color: "red" }}>{error}</div>}
                <EditableTable data={data} setData={setData} />
              </div>
              <div className="col-span-2">
                <Button
                  disabled={data.length < 1}
                  onClick={handleNext}
                  className="w-max border-2 border-primary text-primary px-6 py-2 tracking-wider text-black font-bold"
                >
                  NEXT
                </Button>
              </div>
            </div>
            {/* <TemplateSelectionStep /> */}
            {specifications.length > 0 && (
              <SpecificationList
                specifications={specifications}
                onRemoveSpecification={handleRemoveSpecification}
                onFinalize={handleFinalize}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateSpecification;
