import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Building, Mail, Phone, User } from "lucide-react";

export default function VendorSpecTemplate2Modified({ data }) {
  const { 
    specificationProductData, 
    category, 
    subCategory, 
    specificationType,
    customer_name,
    customer_email,
    customer_phone,
    customer_address
  } = data || {};

  // Use customer data from the specification object instead of API call
  const name = customer_name || "Customer Name";
  const email = customer_email || "<EMAIL>";
  const phone = customer_phone || "+977-9812343435";
  const address = customer_address || "No address provided";

  // Get today's date formatted as MM/DD/YYYY
  const today = new Date().toLocaleDateString("en-US");

  return (
    <Card className="w-full max-w-4xl mx-auto p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-6 text-black">
            SPECIFICATION SHEET
          </h1>

          {/* Customer Details */}
          <div className="text-base text-black space-y-2 max-w-xs">
            <div className="flex items-center gap-2">
              <User size={18} strokeWidth={2.2} />{" "}
              <span>{name}</span>
            </div>
            <div className="flex items-start gap-2">
              <Building size={18} strokeWidth={2.2} className="mt-0.5" />
              <span className="whitespace-pre-line">
                {address}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Mail size={18} strokeWidth={2.2} />{" "}
              <span>{email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone size={18} strokeWidth={2.2} />{" "}
              <span>{phone}</span>
            </div>
          </div>
        </div>

        {/* Date on right */}
        <div className="text-black text-right text-lg font-semibold select-none">
          <div>DATE</div>
          <div className="mt-1 text-base">{today}</div>
        </div>
      </div>

      {/* Specification Details */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Specification Details</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span> {specificationType || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Category:</span> {typeof category === "object" ? category.name : category || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Sub-Category:</span> {subCategory || 'N/A'}
          </div>
        </div>
      </div>

      {/* Specification Table */}
      {specificationProductData?.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Items</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">S.N.</TableHead>
                <TableHead>Item Name</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead>Specifications</TableHead>
                <TableHead>Other</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {specificationProductData.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{index + 1}</TableCell>
                  <TableCell className="font-medium">{item.itemName}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{item.unit}</TableCell>
                  <TableCell>{item.attributes}</TableCell>
                  <TableCell>{item.other}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="flex justify-between items-center text-sm text-gray-600">
          <div>
            <p className="font-medium">Generated on: {today}</p>
          </div>
          <div className="text-right">
            <p className="font-medium">Specification Document</p>
          </div>
        </div>
      </div>
    </Card>
  );
}
