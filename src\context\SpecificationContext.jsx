import React, { createContext, useContext, useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

const SpecificationContext = createContext();

export const useSpecification = () => useContext(SpecificationContext);

export const SpecificationProvider = ({ children }) => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [currentStep, setCurrentStep] = useState(1);
  const [specifications, setSpecifications] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [specificationType, setSpecificationType] = useState("");
  const [currentSpec, setCurrentSpec] = useState({
    fullName: "Full Name",
    address: "Kathmandu, Nepal",
    email: "<EMAIL>",
    phone: "+977-********** / +977-**********",
    product_name: "",
    quantity: 1,
    product_features: "",
    brand_preference: "",
    certifications: "e.g., ISO 9001, CE certification",
    delivery_timeline: "",
    additional_notes: "",
    category: "",
    categoryId: "",
    subcategory: "",
    subcategoryId: "",
  });

  const [specificationProductData, setSpecificationProductData] = useState([]);

  // Update category
  const handleCategoryChange = (categoryData) => {
    setCurrentSpec({
      ...currentSpec,
      category: categoryData.name, // Store name instead of ID
      categoryId: categoryData.id,  // Store ID separately if needed
      subcategory: "",              // Reset subcategory
      subcategoryId: ""
    });
    setCurrentStep(2);
  };

  // Update subcategory
  const handleProductChange = (subcategoryData) => {
    setCurrentSpec({
      ...currentSpec,
      subcategory: subcategoryData.name, // Store name instead of ID
      subcategoryId: subcategoryData.id  // Store ID separately if needed
    });
    setCurrentStep(3);
  };

  // Update specific field
  const handleSpecChange = (field, value) => {
    setCurrentSpec((prevSpec) => ({ ...prevSpec, [field]: value }));
  };

  // Add specification
  const handleAddSpecification = () => {
    console.log("add currentSpec.product_name");
    if (currentSpec.category && currentSpec.subcategory) {
      setSpecifications([
        ...specifications,
        { ...currentSpec, id: Date.now().toString() },
      ]);
      setCurrentSpec({
        fullName: "Full Name",
        address: "Kathmandu, Nepal",
        email: "<EMAIL>",
        phone: "+977-********** / +977-**********",
        product_name: "",
        quantity: 1,
        product_features: "",
        brand_preference: "",
        certifications: "e.g., ISO 9001, CE certification",
        delivery_timeline: "",
        additional_notes: "",
        category: "",
        categoryId: "",
        subcategory: "",
        subcategoryId: "",
      });
      setCurrentStep(1);
      console.log("Specifications:", specifications);
      navigate(`/customer/1/specification-list`);
    }
  };

  // Remove specification
  const handleRemoveSpecification = (id) => {
    setSpecifications(specifications.filter((spec) => spec.id !== id));
  };

  const updateSpecification = (id, updatedSpec) => {
    setSpecifications((prevSpecs) =>
      prevSpecs.map((spec) =>
        spec.id === id ? { ...spec, ...updatedSpec } : spec
      )
    );
  };

  // Finalize specifications
  const handleFinalize = () => {
    console.log("Finalized specifications:", specifications);
    alert("Specifications finalized! Check the console for details.");
  };

  // Remove query param
  const removeQueryParam = (param) => {
    searchParams.delete(param);
    setSearchParams(searchParams);
  };

  // Handle template selection
  useEffect(() => {
    const template = searchParams.get("template");
    setSelectedTemplate(template || null);
  }, [searchParams]);

  return (
    <SpecificationContext.Provider
      value={{
        currentSpec,
        specifications,
        currentStep,
        selectedTemplate,
        setSelectedTemplate,
        handleCategoryChange,
        handleProductChange,
        handleSpecChange,
        handleAddSpecification,
        handleRemoveSpecification,
        updateSpecification,
        handleFinalize,
        removeQueryParam,
        setCurrentStep,
        specificationType,
        setSpecificationType,
        specificationProductData,
        setSpecificationProductData,
      }}
    >
      {children}
    </SpecificationContext.Provider>
  );
};
