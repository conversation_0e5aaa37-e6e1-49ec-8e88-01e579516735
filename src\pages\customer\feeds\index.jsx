import {
  <PERSON><PERSON><PERSON>rumb,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>read<PERSON>rumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { lazy, Suspense } from "react";

const CreatePostSection = lazy(() => import("./_components/CreatePostSection"));
const PostCard = lazy(() => import("./_components/PostCard"));
import DefaultPostImage from "@/assets/png/defaultpostimage.png";

const CustomerFeedsPage = () => {

  const posts = [
    {
      id: 1,
      author: "<PERSON><PERSON>",
      role: "Sopoholic",
      avatar: "https://github.com/shadcn.png",
      content: "Need for the Branded Keyboard and Mouse with multi-functionality",
      tags: ["Technology", "Office"],
      image: DefaultPostImage,
      likes: 120,
    },
    {
      id: 2,
      author: "<PERSON>",
      role: "Tech Enthusiast",
      avatar: "https://via.placeholder.com/150",
      content: "Exploring the latest advancements in AI technology.",
      tags: ["AI", "Innovation"],
      image: DefaultPostImage,
      likes: 230,
    },
    {
      id: 3,
      author: "John Doe",
      role: "Developer",
      avatar: "https://via.placeholder.com/150",
      content: "Looking for recommendations on cloud hosting services.",
      tags: ["Cloud", "Tech"],
      image: DefaultPostImage,
      likes: 85,
    },
  ];

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href={`/customer/1/feeds`}>
                  Feeds
                </BreadcrumbLink>
              </BreadcrumbItem>
              {/* <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Data Fetching</BreadcrumbPage>
              </BreadcrumbItem> */}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <Suspense fallback={<div>Loading...</div>}>
          <CreatePostSection />
        </Suspense>
        <div className="mt-10 grid gap-4">
          {posts.map((post) => (
            <PostCard
              key={post.id}
              author={post.author}
              role={post.role}
              avatar={post.avatar}
              content={post.content}
              tags={post.tags}
              image={post.image}
              likes={post.likes}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default CustomerFeedsPage;
