"use client";

import React from "react";
import { useAppDispatch } from "@/hooks/StoreHooks";
import { setInitialAuth } from "@/redux/slice/auth/authSlice";
import { clearAuthData, getUserTypeFromRoute } from "@/utils/authUtils";

const useAuthHooks = () => {
  const [isLogout, setIsLogout] = React.useState(false);
  const dispatch = useAppDispatch();

  const handleLogout = (userType = null) => {
    // Determine user type from current route if not provided
    const currentPath = window.location.pathname;
    const targetUserType = userType || getUserTypeFromRoute(currentPath);

    // Clear cookies and localStorage for specific user type
    clearAuthData(targetUserType);

    // Reset Redux state
    dispatch(setInitialAuth());

    setTimeout(() => {
      setIsLogout(true);
    }, 500);
  };

  const handleLogoutAll = () => {
    // Clear all authentication data
    clearAuthData();

    // Reset Redux state
    dispatch(setInitialAuth());

    setTimeout(() => {
      setIsLogout(true);
    }, 500);
  };

  return {
    handleLogout,
    handleLogoutAll,
    isLogout,
  };
};

export default useAuthHooks;
