import React, { useState } from "react";
import { Mail, MapPin, Phone } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

export function SpecificationReview({ specification, onUpdate, onFinalize }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedSpec, setEditedSpec] = useState({ ...specification });

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    onUpdate(editedSpec);
    setIsEditing(false);
  };

  const handleChange = (field, value) => {
    setEditedSpec((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="bg-white p-6 w-full max-w-3xl rounded-md shadow-lg mx-auto">
      <div className="flex justify-between">
        {/* Full Name Section */}
        <div className="mb-6">
          {isEditing ? (
            <Input
              value={editedSpec.fullName}
              id="fullname"
              onChange={(e) => handleChange("fullName", e.target.value)}
              className="text-2xl font-bold mb-2 font-inter p-0 border-0 focus:outline-none focus:ring-0"
            />
          ) : (
            <p className="text-2xl font-bold mb-2 font-inter">
              {editedSpec.fullName}
            </p>
          )}
          <div className="space-y-1">
            <div className="flex gap-2">
              <MapPin />
              {isEditing ? (
                <Input
                  id="address"
                  value={editedSpec.address}
                  onChange={(e) => handleChange("address", e.target.value)}
                  className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.address}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Mail />
              {isEditing ? (
                <Input
                  id="email"
                  value={editedSpec.email}
                  onChange={(e) => handleChange("email", e.target.value)}
                  className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.email}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Phone />
              {isEditing ? (
                <Input
                  id="phone"
                  value={editedSpec.phone}
                  onChange={(e) => handleChange("phone", e.target.value)}
                  className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.phone}</p>
              )}
            </div>
          </div>
        </div>

        {/* Product Specification Header */}
        <div className="mb-6">
          <h1 className="text-xl font-bold">Product Specification</h1>
          <div className="text-sm space-y-1">
            <div>
              Specification ID:
              {isEditing ? (
                <Input
                  id="specificationId"
                  value={editedSpec.specificationId}
                  onChange={(e) =>
                    handleChange("specificationId", e.target.value)
                  }
                  className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.specificationId}</p>
              )}
            </div>
            <div>
              Created Date:
              {isEditing ? (
                <Input
                  id="createdDate"
                  type="date"
                  value={editedSpec.createdDate}
                  onChange={(e) => handleChange("createdDate", e.target.value)}
                  className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.createdDate}</p>
              )}
            </div>
            <div>
              Due Date:
              {isEditing ? (
                <Input
                  id="dueDate"
                  type="date"
                  value={editedSpec.dueDate}
                  onChange={(e) => handleChange("dueDate", e.target.value)}
                  className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
                />
              ) : (
                <p className="text-base">{editedSpec.dueDate}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      <form className="space-y-4">
        {/* Category */}
        <div className="flex items-center">
          <Label htmlFor="category" className="font-bold">
            Category
          </Label>
          {isEditing ? (
            <Input
              id="category"
              value={editedSpec.category}
              onChange={(e) => handleChange("category", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.category}</p>
          )}
        </div>

        {/* Product Name */}
        <div className="flex items-center border-b">
          <Label htmlFor="productName" className="font-bold">
            Product Name
          </Label>
          {isEditing ? (
            <Input
              id="productName"
              value={editedSpec.productName}
              onChange={(e) => handleChange("productName", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.productName}</p>
          )}
        </div>

        {/* Product Code */}
        <div className="flex items-center border-b">
          <Label htmlFor="productCode" className="font-bold">
            Product Code
          </Label>
          {isEditing ? (
            <Input
              id="productCode"
              value={editedSpec.productCode}
              onChange={(e) => handleChange("productCode", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.productCode}</p>
          )}
        </div>

        {/* Description */}
        <div className="border-b">
          <Label htmlFor="description" className="font-bold">
            Description
          </Label>
          {isEditing ? (
            <Textarea
              id="description"
              value={editedSpec.description}
              onChange={(e) => handleChange("description", e.target.value)}
              className="flex-1 min-h-[100px] resize-none p-0 border-0 focus:outline-none focus:ring-0"
            />
          ) : (
            <p className="text-base">{editedSpec.description}</p>
          )}
        </div>

        {/* Brand */}
        <div className="flex items-center border-b">
          <Label htmlFor="brand" className="font-bold">
            Brand
          </Label>
          {isEditing ? (
            <Input
              id="brand"
              value={editedSpec.brand}
              onChange={(e) => handleChange("brand", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.brand}</p>
          )}
        </div>

        {/* Type/Model */}
        <div className="flex items-center border-b">
          <Label htmlFor="typeModel" className="font-bold">
            Type/Model
          </Label>
          {isEditing ? (
            <Input
              id="typeModel"
              value={editedSpec.typeModel}
              onChange={(e) => handleChange("typeModel", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.typeModel}</p>
          )}
        </div>

        {/* Manufacturer */}
        <div className="flex items-center border-b">
          <Label htmlFor="manufacturer" className="font-bold">
            Manufacturer
          </Label>
          {isEditing ? (
            <Input
              id="manufacturer"
              value={editedSpec.manufacturer}
              onChange={(e) => handleChange("manufacturer", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.manufacturer}</p>
          )}
        </div>

        {/* Country of Origin */}
        <div className="flex items-center border-b">
          <Label htmlFor="countryOrigin" className="font-bold">
            Country of Origin
          </Label>
          {isEditing ? (
            <Input
              id="countryOrigin"
              value={editedSpec.countryOrigin}
              onChange={(e) => handleChange("countryOrigin", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.countryOrigin}</p>
          )}
        </div>

        {/* Design Specification Header */}
        <div className="font-bold text-lg pt-4">Design Specification</div>

        {/* Dimensions */}
        <div className="flex items-center border-b">
          <Label htmlFor="dimensions" className="font-bold">
            Dimensions
          </Label>
          {isEditing ? (
            <Input
              id="dimensions"
              value={editedSpec.dimensions}
              onChange={(e) => handleChange("dimensions", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.dimensions}</p>
          )}
        </div>

        {/* Weight */}
        <div className="flex items-center border-b">
          <Label htmlFor="weight" className="font-bold">
            Weight
          </Label>
          {isEditing ? (
            <Input
              id="weight"
              value={editedSpec.weight}
              onChange={(e) => handleChange("weight", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.weight}</p>
          )}
        </div>

        {/* Variant */}
        <div className="flex items-center border-b">
          <Label htmlFor="variant" className="font-bold">
            Variant
          </Label>
          {isEditing ? (
            <Input
              id="variant"
              value={editedSpec.variant}
              onChange={(e) => handleChange("variant", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.variant}</p>
          )}
        </div>

        {/* Delivery Header */}
        <div className="font-bold text-lg pt-4">Delivery</div>

        {/* Delivery Date */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryDate" className="font-bold">
            Delivery Date
          </Label>
          {isEditing ? (
            <Input
              id="deliveryDate"
              value={editedSpec.deliveryDate}
              onChange={(e) => handleChange("deliveryDate", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.deliveryDate}</p>
          )}
        </div>

        {/* Delivery Location */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryLocation" className="font-bold">
            Delivery Location
          </Label>
          {isEditing ? (
            <Input
              id="deliveryLocation"
              value={editedSpec.deliveryLocation}
              onChange={(e) => handleChange("deliveryLocation", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">
              {editedSpec.deliveryLocation}
            </p>
          )}
        </div>

        {/* Delivery Instructions */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryInstructions" className="font-bold">
            Delivery Instructions
          </Label>
          {isEditing ? (
            <Input
              id="deliveryInstructions"
              value={editedSpec.deliveryInstructions}
              onChange={(e) =>
                handleChange("deliveryInstructions", e.target.value)
              }
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">
              {editedSpec.deliveryInstructions}
            </p>
          )}
        </div>

        {/* Budget/Price Range */}
        <div className="flex items-center border-b">
          <Label htmlFor="budgetPriceRange" className="font-bold">
            Budget/Price Range
          </Label>
          {isEditing ? (
            <Input
              id="budgetPriceRange"
              value={editedSpec.budgetPriceRange}
              onChange={(e) => handleChange("budgetPriceRange", e.target.value)}
              className="flex-1 h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">
              {editedSpec.budgetPriceRange}
            </p>
          )}
        </div>

        {/* Pricing Terms */}
        <div className="flex items-center border-b">
          <Label htmlFor="pricingTerms" className="font-bold">
            Pricing Terms
          </Label>
          {isEditing ? (
            <Input
              id="pricingTerms"
              value={editedSpec.pricingTerms}
              onChange={(e) => handleChange("pricingTerms", e.target.value)}
              className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            />
          ) : (
            <p className="text-base text-right">{editedSpec.pricingTerms}</p>
          )}
        </div>

        {/* Additional Instructions */}
        <div className="">
          <Label htmlFor="additionalInstructions" className="font-bold">
            Additional Instructions
          </Label>
          {isEditing ? (
            <Textarea
              id="additionalInstructions"
              value={editedSpec.additionalInstructions}
              onChange={(e) =>
                handleChange("additionalInstructions", e.target.value)
              }
              className="p-0 min-h-[100px] resize-none p-0 border-0 focus:outline-none focus:ring-0"
            />
          ) : (
            <p className="text-base">{editedSpec.additionalInstructions}</p>
          )}
        </div>
      </form>

      <div className="flex justify-between mt-6">
        {isEditing ? (
          <Button
            className="bg-white text-primary border border-primary w-36 hover:text-white hover:bg-primary"
            onClick={handleSave}
          >
            Save Changes
          </Button>
        ) : (
          <Button
            className="bg-white text-primary border border-primary w-36 hover:text-white hover:bg-primary"
            onClick={handleEdit}
          >
            Edit
          </Button>
        )}
        <Button
          className="bg-white text-primary border border-primary w-36 hover:text-white hover:bg-primary"
          onClick={() => onFinalize(specification.id)}
          disabled={isEditing}
        >
          Finalize
        </Button>
      </div>
    </div>
  );
}
