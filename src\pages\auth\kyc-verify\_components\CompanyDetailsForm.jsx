"use client";

import { useForm, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEffect } from "react";
import {
  useDistrictQuery,
  useGetProvinceData,
  useMunicipalityQuery,
} from "@/services/general/query";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function CompanyDetailsForm({
  onNext,
  onPrevious,
  initialData,
  companyType,
}) {
  const {
    register,
    handleSubmit,
    control,
    watch,
    resetField,
    formState: { errors },
  } = useForm({
    defaultValues: initialData,
  });

  const { data: provinces } = useGetProvinceData();
  console.log(provinces);
  const selectedProvinceId = watch("province");
  const selectedDistrictId = watch("district");

  const { data: districts, isLoading: isDistrictsLoading } =
    useDistrictQuery(selectedProvinceId);
  const { data: municipalities, isLoading: isMunicipalitiesLoading } =
    useMunicipalityQuery(selectedDistrictId);

  useEffect(() => {
    if (selectedProvinceId) {
      resetField("district");
      resetField("municipality");
    }
  }, [selectedProvinceId, resetField]);

  useEffect(() => {
    if (selectedDistrictId) {
      resetField("municipality");
    }
  }, [selectedDistrictId, resetField]);

  const onSubmit = (data) => {
    // Get the actual names for display in review
    const selectedProvince = provinces?.data?.find(p => p.id.toString() === data.province);
    const selectedDistrict = districts?.data?.find(d => d.id.toString() === data.district);
    const selectedMunicipality = municipalities?.data?.find(m => m.id.toString() === data.municipality);
    
    const enhancedData = {
      ...data,
      provinceName: selectedProvince?.name || '',
      districtName: selectedDistrict?.name || '',
      municipalityName: selectedMunicipality?.name || '',
    };
    
    onNext({ companyDetails: enhancedData });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Common fields for all company types */}
      <div className="flex justify-between gap-12">
        <div className="w-1/2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            className="mt-1"
            id="companyName"
            {...register("companyName", {
              required: "Company name is required",
            })}
          />
          {errors.companyName && (
            <p className="text-red-500">{errors.companyName.message}</p>
          )}
        </div>
        <div className="w-1/2">
          <Label htmlFor="province">Province</Label>
          <Controller
            name="province"
            control={control}
            rules={{ required: "Province is required" }}
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!provinces}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select Province" />
                </SelectTrigger>
                <SelectContent>
                  {provinces?.data?.map((province) => (
                    <SelectItem
                      key={province.id}
                      value={province.id.toString()}
                    >
                      {province.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.province && (
            <p className="text-red-500">{errors.province.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-12">
        <div className="w-1/2">
          <Label htmlFor="district">District</Label>
          <Controller
            name="district"
            control={control}
            rules={{ required: "District is required" }}
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!districts}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select District" />
                </SelectTrigger>
                <SelectContent>
                  {districts?.data?.map((district) => (
                    <SelectItem
                      key={district.id}
                      value={district.id.toString()}
                    >
                      {district.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.district && (
            <p className="text-red-500">{errors.district.message}</p>
          )}
        </div>

        <div className="w-1/2">
          <Label htmlFor="municipality">Municipality</Label>
          <Controller
            name="municipality"
            control={control}
            rules={{ required: "Municipality is required" }}
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!municipalities}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select Municipality" />
                </SelectTrigger>
                <SelectContent>
                  {municipalities?.data?.map((municipality) => (
                    <SelectItem
                      key={municipality.id}
                      value={municipality.id.toString()}
                    >
                      {municipality.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.municipality && (
            <p className="text-red-500">{errors.municipality.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between gap-12">
        <div className="w-1/2">
          <Label htmlFor="ward">Ward</Label>
          <Input
            className="mt-1"
            id="ward"
            {...register("ward", {
              required: "Ward is required",
            })}
          />
          {errors.ward && <p className="text-red-500">{errors.ward.message}</p>}
        </div>

        <div className="w-1/2">
          <Label htmlFor="streetAddress">Street Address</Label>
          <Input
            className="mt-1"
            id="streetAddress"
            {...register("streetAddress", {
              required: "Street Address is required",
            })}
          />
          {errors.streetAddress && (
            <p className="text-red-500">{errors.streetAddress.message}</p>
          )}
        </div>
      </div>

      {/* Conditional fields based on companyType */}
      <div className="flex justify-between gap-12">
        <div className="w-1/2">
          <Label htmlFor="tole">Tole</Label>
          <Input
            className="mt-1"
            id="tole"
            {...register("tole", {
              required: "Tole is required",
            })}
          />
          {errors.tole && <p className="text-red-500">{errors.tole.message}</p>}
        </div>
      </div>

      <div className="flex justify-end gap-6 mt-8">
        <Button
          className="w-48"
          type="button"
          variant="outline"
          onClick={onPrevious}
        >
          Previous
        </Button>
        <Button className="w-48" type="submit">
          Next
        </Button>
      </div>
    </form>
  );
}
{
  /* <div className="w-1/2">
<Label htmlFor="registrationNumber">Registration Number</Label>
<Input
  id="registrationNumber"
  {...register("registrationNumber", {
    required: "Registration number is required",
  })}
/>
{errors.registrationNumber && (
  <p className="text-red-500">{errors.registrationNumber.message}</p>
)}
</div> */
}
