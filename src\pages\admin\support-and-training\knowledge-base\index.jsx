import React from "react";
import { SelectItem } from "@/components/ui/select";

import { SelectContent } from "@/components/ui/select";

import { SelectValue } from "@/components/ui/select";

import { SelectTrigger } from "@/components/ui/select";

import { Select } from "@/components/ui/select";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit, Plus, Search, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const SupportTrainingKnowledgeBasePage = () => {
  const [faqs, setFaqs] = useState([
    {
      id: 1,
      question: "How do I reset my password?",
      answer:
        "Click on the 'Forgot Password' link on the login page and follow the instructions sent to your email.",
      category: "Account",
    },
    {
      id: 2,
      question: "What is a PO Number?",
      answer:
        "A Purchase Order (PO) Number is a unique identifier assigned to a purchase order. It helps track the order throughout the procurement process.",
      category: "Orders",
    },
    {
      id: 3,
      question: "How do I submit a quotation?",
      answer:
        "Navigate to the 'Quotations' section, click on 'New Quotation', fill in the required details, and click 'Submit'.",
      category: "Quotations",
    },
    {
      id: 4,
      question: "What is KYC verification?",
      answer:
        "Know Your Customer (KYC) verification is a process where we verify the identity of our clients. This helps prevent fraud and ensures compliance with regulations.",
      category: "Account",
    },
  ]);
  const [searchQuery, setSearchQuery] = useState("");
  const [newFaq, setNewFaq] = useState({
    question: "",
    answer: "",
    category: "General",
  });
  const [editingFaq, setEditingFaq] = useState(null);

  const handleScheduleTraining = (e) => {
    e.preventDefault();

    if (!newTraining.title || !newTraining.datetime || !newTraining.link) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    const newSession = {
      id: trainingSessions.length + 1,
      ...newTraining,
      status: "Upcoming",
    };

    setTrainingSessions([...trainingSessions, newSession]);
    setNewTraining({ title: "", datetime: "", link: "" });

    toast({
      title: "Training Scheduled",
      description: `${newTraining.title} has been scheduled successfully.`,
    });
  };

  // Filter FAQs based on search query
  const filteredFaqs = faqs.filter(
    (faq) =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle adding new FAQ
  const handleAddFaq = () => {
    if (!newFaq.question || !newFaq.answer) {
      toast({
        title: "Error",
        description: "Question and answer cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const newFaqItem = {
      id: faqs.length + 1,
      ...newFaq,
    };

    setFaqs([...faqs, newFaqItem]);
    setNewFaq({ question: "", answer: "", category: "General" });

    toast({
      title: "FAQ Added",
      description: "New FAQ has been added successfully.",
    });
  };

  // Handle editing FAQ
  const handleEditFaq = () => {
    if (!editingFaq.question || !editingFaq.answer) {
      toast({
        title: "Error",
        description: "Question and answer cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const updatedFaqs = faqs.map((faq) =>
      faq.id === editingFaq.id ? editingFaq : faq
    );

    setFaqs(updatedFaqs);
    setEditingFaq(null);

    toast({
      title: "FAQ Updated",
      description: "FAQ has been updated successfully.",
    });
  };

  // Handle deleting FAQ
  const handleDeleteFaq = (id) => {
    const updatedFaqs = faqs.filter((faq) => faq.id !== id);
    setFaqs(updatedFaqs);

    toast({
      title: "FAQ Deleted",
      description: "FAQ has been deleted successfully.",
    });
  };
  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Knowledge Base</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search FAQs..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Browse through common questions and answers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <Accordion type="single" collapsible className="w-full">
                  {filteredFaqs.map((faq) => (
                    <AccordionItem key={faq.id} value={`faq-${faq.id}`}>
                      <AccordionTrigger>
                        <div className="flex items-start text-left">
                          <span className="mr-2">Q:</span>
                          <span>{faq.question}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="flex items-start">
                          <span className="mr-2">A:</span>
                          <span>{faq.answer}</span>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <Badge variant="outline" className="text-xs">
                            {faq.category}
                          </Badge>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setEditingFaq({ ...faq })}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-destructive"
                              onClick={() => handleDeleteFaq(faq.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </ScrollArea>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{editingFaq ? "Edit FAQ" : "Add New FAQ"}</CardTitle>
              <CardDescription>
                {editingFaq
                  ? "Update an existing FAQ"
                  : "Create a new frequently asked question"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="question">Question</Label>
                  <Input
                    id="question"
                    placeholder="e.g., How do I reset my password?"
                    value={editingFaq ? editingFaq.question : newFaq.question}
                    onChange={(e) =>
                      editingFaq
                        ? setEditingFaq({
                            ...editingFaq,
                            question: e.target.value,
                          })
                        : setNewFaq({ ...newFaq, question: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="answer">Answer</Label>
                  <Textarea
                    id="answer"
                    placeholder="Provide a clear and concise answer..."
                    rows={4}
                    value={editingFaq ? editingFaq.answer : newFaq.answer}
                    onChange={(e) =>
                      editingFaq
                        ? setEditingFaq({
                            ...editingFaq,
                            answer: e.target.value,
                          })
                        : setNewFaq({ ...newFaq, answer: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={editingFaq ? editingFaq.category : newFaq.category}
                    onValueChange={(value) =>
                      editingFaq
                        ? setEditingFaq({ ...editingFaq, category: value })
                        : setNewFaq({ ...newFaq, category: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="General">General</SelectItem>
                      <SelectItem value="Account">Account</SelectItem>
                      <SelectItem value="Orders">Orders</SelectItem>
                      <SelectItem value="Quotations">Quotations</SelectItem>
                      <SelectItem value="Technical">Technical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              {editingFaq ? (
                <>
                  <Button variant="outline" onClick={() => setEditingFaq(null)}>
                    Cancel
                  </Button>
                  <Button onClick={handleEditFaq}>Update FAQ</Button>
                </>
              ) : (
                <Button className="w-full" onClick={handleAddFaq}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add FAQ
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SupportTrainingKnowledgeBasePage;
