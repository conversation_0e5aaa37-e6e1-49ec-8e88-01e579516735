import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Building, Mail, Phone, User } from "lucide-react";
import { useGetCustomerProfileQuery } from "@/services/auth/query";

export default function SpecTemplate2Modified({ data }) {
  const { specificationProductData, category, subCategory, specificationType } = data || {};
  const customerData = useGetCustomerProfileQuery();
  const { name, phone, email, address } =
    customerData?.data?.data || {};

  // Get today's date formatted as MM/DD/YYYY
  const today = new Date().toLocaleDateString("en-US");

  return (
    <Card className="w-full max-w-4xl mx-auto p-8">
      {/* Header with logo + customer details left, date right */}
      <div className="flex justify-between items-start mb-10">
        <div className="flex flex-col gap-6">
          {/* Logo placeholder */}
          <div className="text-3xl font-extrabold text-black select-none">
            {/* Replace below with actual logo img or component */}
            LOGO
          </div>

          {/* Customer Details */}
          <div className="text-base text-black space-y-2 max-w-xs">
            <div className="flex items-center gap-2">
              <User size={18} strokeWidth={2.2} />{" "}
              <span>{name || "Customer Name"}</span>
            </div>
            <div className="flex items-start gap-2">
              <Building size={18} strokeWidth={2.2} className="mt-0.5" />
              <span className="whitespace-pre-line">
                {address || "No address provided"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Mail size={18} strokeWidth={2.2} />{" "}
              <span>{email || "<EMAIL>"}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone size={18} strokeWidth={2.2} />{" "}
              <span>{phone || "+977-9812343435"}</span>
            </div>
          </div>
        </div>

        {/* Date on right */}
        <div className="text-black text-right text-lg font-semibold select-none">
          <div>DATE</div>
          <div className="mt-1 text-base">{today}</div>
        </div>
      </div>

      {/* Specification Details */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Specification Details</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span> {specificationType || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Category:</span> {category || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Sub-Category:</span> {subCategory || 'N/A'}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">S.N.</TableHead>
              <TableHead>Item Name</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Unit</TableHead>
              <TableHead>Attributes</TableHead>
              <TableHead>Other</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {specificationProductData?.map((item, index) => (
              <TableRow key={index}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>{item.itemName}</TableCell>
                <TableCell>{item.quantity}</TableCell>
                <TableCell>{item.unit}</TableCell>
                <TableCell>{item.attributes}</TableCell>
                <TableCell>{item.other}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Footer */}
      <div className="mt-10 flex justify-between text-black">
        <div className="space-y-4 text-sm max-w-md">
          <div>
            <h3 className="font-semibold mb-1">Remarks:</h3>
            <p>
              Specification details are tentative and can be customized upon
              discussion.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-1">Terms:</h3>
            <p>
              Ensure all specifications meet your requirements before
              confirmation.
            </p>
          </div>
        </div>
        <div className="text-sm text-black">
          <div className="pt-8 border-t">
            <p className="font-medium mb-2">Authorized Signature</p>
            <div className="h-12" />
          </div>
        </div>
      </div>
    </Card>
  );
}
