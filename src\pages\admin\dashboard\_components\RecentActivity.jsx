import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";

const recentActivities = [
  {
    id: 1,
    type: "user_registration",
    user: "John Doe",
    action: "registered as a new vendor",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    status: "success",
    avatar: null,
  },
  {
    id: 2,
    type: "quotation_submitted",
    user: "Tech Solutions Inc.",
    action: "submitted a quotation for Office Equipment",
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    status: "pending",
    avatar: null,
  },
  {
    id: 3,
    type: "document_verified",
    user: "Admin",
    action: "verified documents for ABC Corp",
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    status: "success",
    avatar: null,
  },
  {
    id: 4,
    type: "specification_created",
    user: "Acme Corporation",
    action: "created a new specification for IT Infrastructure",
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    status: "info",
    avatar: null,
  },
  {
    id: 5,
    type: "training_scheduled",
    user: "Admin",
    action: "scheduled training session for new vendors",
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    status: "info",
    avatar: null,
  },
];

const getStatusColor = (status) => {
  switch (status) {
    case "success":
      return "bg-green-100 text-green-800";
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "error":
      return "bg-red-100 text-red-800";
    case "info":
    default:
      return "bg-blue-100 text-blue-800";
  }
};

const getActivityIcon = (type) => {
  switch (type) {
    case "user_registration":
      return "👤";
    case "quotation_submitted":
      return "📄";
    case "document_verified":
      return "✅";
    case "specification_created":
      return "📋";
    case "training_scheduled":
      return "🎓";
    default:
      return "📌";
  }
};

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest actions and updates in the system</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={activity.avatar} />
                <AvatarFallback className="text-xs">
                  {getActivityIcon(activity.type)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-foreground">
                    {activity.user}
                  </p>
                  <Badge variant="outline" className={`text-xs ${getStatusColor(activity.status)}`}>
                    {activity.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{activity.action}</p>
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
