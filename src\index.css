@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0, 0%, 100%;
    --foreground: 0, 0%, 3.9%;

    --card: 0, 0%, 100%;
    --card-foreground: 0, 0%, 3.9%;

    --popover: 0, 0%, 100%;
    --popover-foreground: 0, 0%, 3.9%;

    --primary: 148, 93%, 42%;
    --primary-foreground: 0, 100%, 50%;

    --secondary: 0, 0%, 96.1%;
    --secondary-foreground: 0, 0%, 9%;

    --muted: 0, 0%, 96.1%;
    --muted-foreground: 0, 0%, 45.1%;

    --accent: 0, 0%, 96.1%;
    --accent-foreground: 0, 0%, 9%;

    --destructive: 0, 84.2%, 60.2%;
    --destructive-foreground: 0, 0%, 98%;

    --border: 0, 0%, 89.8%;
    --input: 0, 0%, 89.8%;
    --ring: 0, 0%, 3.9%;

    --radius: 0.5rem;

    --chart-1: 12, 76%, 61%;
    --chart-2: 173, 58%, 39%;
    --chart-3: 197, 37%, 24%;
    --chart-4: 43, 74%, 66%;
    --chart-5: 27, 87%, 67%;

    /* custom colors */
    --green1: #06a651; /* #06A651 */
    --green2: #08cf65; /* #08CF65 */
    --green3: #4cdc90;
    --green4: #ecfeef;
    --dark-navy: #0c101c;
    --navy: #181f38;
    --dark-brown: #211c19;
    --blue-gray: #2b3857;
    --dark-teal: #464c60;
    --mint-green: #84e7b2;
    --light-gray1: #a4a4a4;
    --light-brown: #ba1a1a;
    --light-gray2: #c8c8c8;
    --gold1: #cca634;
    --yellow1: #ffd041;
    --yellow2: #ffe8a0;
    --dark-light: #fbfbfb;
    --dark-medium: #e6e6e6;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0, 0%, 3.9%;
    --foreground: 0, 0%, 98%;

    --card: 0, 0%, 3.9%;
    --card-foreground: 0, 0%, 98%;

    --popover: 0, 0%, 3.9%;
    --popover-foreground: 0, 0%, 98%;

    --primary: 120, 100%, 40%;
    --primary-foreground: 0, 0%, 9%;

    --secondary: 0, 0%, 14.9%;
    --secondary-foreground: 0, 0%, 98%;

    --muted: 0, 0%, 14.9%;
    --muted-foreground: 0, 0%, 63.9%;

    --accent: 0, 0%, 14.9%;
    --accent-foreground: 0, 0%, 98%;

    --destructive: 0, 62.8%, 30.6%;
    --destructive-foreground: 0, 0%, 98%;

    --border: 0, 0%, 14.9%;
    --input: 0, 0%, 14.9%;
    --ring: 0, 0%, 83.1%;

    --chart-1: 220, 70%, 50%;
    --chart-2: 160, 60%, 45%;
    --chart-3: 30, 80%, 55%;
    --chart-4: 280, 65%, 60%;
    --chart-5: 340, 75%, 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

/* custom css */
.frameChild {
  flex: 1;
  position: relative;
  border-top: 1px solid #cfcfcf;
  box-sizing: border-box;
  height: 1px;
}
.or {
  position: relative;
}
.lineParent {
  width: 100%;
  position: relative;
  padding-top: 1rem !important;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 18px;
  text-align: left;
  font-size: 14px;
  color: rgba(164, 164, 164, 0.67);
  padding: 0.5rem 0rem;
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
