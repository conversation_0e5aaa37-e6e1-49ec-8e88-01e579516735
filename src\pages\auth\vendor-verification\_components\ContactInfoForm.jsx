import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { isText } from "@/utils/validation";
import { useForm, Controller } from "react-hook-form";
import { LuArrowLeft, LuArrowRight } from "react-icons/lu";

const ContactInfoForm = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      province: setup?.province || "",
      district: setup?.district || "",
      municipality: setup?.municipality || "",
      streetAddress: setup?.streetAddress || "",
    },
  });

  const handleBack = () => {
    dispatch(
      setSetup({
        ...setup,
        step: 1,
      })
    );
  };

  const onSubmit = (data) => {
    console.log(data);
    dispatch(
      setSetup({
        ...setup,
        province: data?.province,
        district: data?.district,
        municipality: data?.municipality,
        streetAddress: data?.streetAddress,
        step: 3,
      })
    );
  };

  return (
    <div className="max-w-[40rem] sm:min-w-[40rem]">
      <div className="flex flex-col">
        <h2 className="font-nunito text-bodyLarge sm:text-[1.8rem] text-center font-bold">{`Vendor Verification`}</h2>
        <span className="font-nunito text-center">{`Address Details`}</span>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-10">
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="province"
            >
              Province *
            </label>
            <Controller
              name="province"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Province is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid Province";
                  }
                  if (value.length < 3) {
                    return "Province must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter province"
                    autoComplete="off"
                    id="province"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.province && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.province?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="district"
            >
              District *
            </label>
            <Controller
              name="district"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "District is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid District";
                  }
                  if (value.length < 3) {
                    return "District must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter district"
                    autoComplete="off"
                    id="district"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.district && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.district?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="municipality"
            >
              Municipality *
            </label>
            <Controller
              name="municipality"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Municipality is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid Municipality";
                  }
                  if (value.length < 3) {
                    return "Municipality must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter municipality"
                    autoComplete="off"
                    id="municipality"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.municipality && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.municipality?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="streetAddress"
            >
              Street Address *
            </label>
            <Controller
              name="streetAddress"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Street Address is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid Street Address";
                  }
                  if (value.length < 3) {
                    return "Street Address must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter street address"
                    autoComplete="off"
                    id="streetAddress"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.email
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.streetAddress && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.streetAddress?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
        </div>
        <div className="float-right mt-5 flex flex-col xs:flex-row items-center gap-4">
          <Button
            variant="outline"
            className="font-nunito flex items-center text-bodyMedium font-extrabold hover:bg-green1 text-darkNavy h-[3rem] px-10 sm:px-8 rounded-xl tracking-wider"
            onClick={handleBack}
          >
            <LuArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider"
            type="submit"
          >
            Next Step
            <LuArrowRight className="ml-2 h-4 w-4" />
            {/* {false ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Continue"
                )} */}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ContactInfoForm;
