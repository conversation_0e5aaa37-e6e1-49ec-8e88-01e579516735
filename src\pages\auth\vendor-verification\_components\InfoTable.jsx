const InfoTable = ({ title, data }) => {
  return (
    <div className="w-full">
      <table className="w-full text-sm text-left text-gray-500 mb-5 table-fixed">
        <thead className="w-full">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 font-bold text-black uppercase font-nunito w-full"
              colSpan="2"
            >
              {title}
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={index} className="bg-white">
              <th
                scope="row"
                className="px-6 py-2 font-medium whitespace-nowrap w-1/3 font-nunito"
              >
                {item.label}
              </th>
              <td className="px-6 py-2 w-2/3 font-semibold text-black font-nunito">
                {item.value || "N/A"}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InfoTable;
