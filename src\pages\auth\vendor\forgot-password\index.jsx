import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useForm, Controller } from "react-hook-form";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import forgotPasswordImg from "@/assets/svg/signin.svg";
import { Link, useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useForgotPasswordVendorMutation } from "@/services/auth/mutation";

const VendorForgotPasswordPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: "",
    },
  });

  const { toast } = useToast();
  const navigate = useNavigate();
  const [isEmailSent, setIsEmailSent] = useState(false);

  const forgotPasswordMutation = useForgotPasswordVendorMutation();

  const onSubmit = async (data) => {
    try {
      const response = await forgotPasswordMutation.mutateAsync(data);
      
      if (response.success) {
        setIsEmailSent(true);
        toast({
          title: "Email Sent Successfully",
          description: "Please check your email for password reset instructions.",
          variant: "success",
          duration: 5000,
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to send reset email. Please try again.",
          variant: "error",
          duration: 3000,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "error",
        duration: 3000,
      });
    }
  };

  if (isEmailSent) {
    return (
      <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
        <div className="w-full">
          <div className="text-center">
            <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">
              Check Your Email
            </h1>
            <p className="font-nunito text-bodyMedium leading-5 text-blueGray mt-4">
              We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
            </p>
            <div className="mt-8">
              <Link
                to="/vendor/signin"
                className="text-green1 font-nunito text-bodyMedium hover:underline"
              >
                Back to Sign In
              </Link>
            </div>
          </div>
        </div>
        <div className="hidden sm:block w-full">
          <img
            className="sm:block hidden h-full"
            src={forgotPasswordImg}
            alt="Forgot Password Image"
            loading="lazy"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
      <div className="w-full">
        <div>
          <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">
            Forgot Your Password?
          </h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">
            Enter your email address and we'll send you a link to reset your password.
          </p>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray mt-2">
            {`Need customer account? `}
            <Link
              to="/customer/forgot-password"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Reset Customer Password
            </Link>
          </p>
        </div>
        
        <div className="lg:max-w-[30rem] mt-8">
          <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Email Address
              </label>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Email is required",
                  },
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Please enter a valid email address",
                  },
                }}
                render={({ field }) => (
                  <>
                    <input
                      {...field}
                      type="email"
                      placeholder="Enter your email address"
                      autoComplete="email"
                      className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                        errors.email
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    {errors.email && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.email.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            <div>
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
                type="submit"
                disabled={forgotPasswordMutation.isPending}
              >
                {forgotPasswordMutation.isPending ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Send Reset Link"
                )}
              </Button>
            </div>
          </form>
          
          <div className="flex items-center justify-center gap-2 mt-5">
            <span className="font-nunito text-bodySmall">
              Remember your password?
            </span>
            <Link
              to="/vendor/signin"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Back to Sign In
            </Link>
          </div>
        </div>
      </div>
      <div className="hidden sm:block w-full">
        <img
          className="sm:block hidden h-full"
          src={forgotPasswordImg}
          alt="Forgot Password Image"
          loading="lazy"
        />
      </div>
    </div>
  );
};

export default VendorForgotPasswordPage;
