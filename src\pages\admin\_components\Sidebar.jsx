import {
  BadgeCheck,
  Bell,
  ChartNoAxesGantt,
  ChevronRight,
  ChevronsUpDown,
  DatabaseZap,
  FileCheck,
  Headset,
  Layers,
  LayoutList,
  LockKeyhole,
  LogOut,
  Sparkles,
  SquareTerminal,
  TableOfContents,
  Target,
  User,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import logo from "@/assets/png/logo.png";
import useAuthHooks from "@/hooks/authUser/useAuthUser";
import { useEffect } from "react";

import getShortName from "@/utils/getShortName";
import { Link} from "react-router-dom";

const DashboardSidebar = () => {
  const { handleLogout, isLogout } = useAuthHooks();


  useEffect(() => {
    if (isLogout) {
      window.location.href = "/";
    }
  }, [isLogout]);

  const data = {
    navMain: [
      {
        title: "Dashboard",
        url: "/admin/1/dashboard",
        icon: SquareTerminal,
        isActive: true,
      },
      {
        title: "User Management",
        url: "/admin/1/user-management",
        icon: User,
      },
      {
        title: "Document Verification",
        url: "/admin/1/document-verification",
        icon: FileCheck,
      },
      {
        title: "Category",
        url: "/admin/1/category",
        icon: Layers,
      },
      {
        title: "Subcategory",
        url: "/admin/1/sub-category",
        icon: ChartNoAxesGantt,
      },
      {
        title: "Quotation Management",
        url: "/admin/1/quotation-management",
        icon: DatabaseZap,
      },
      {
        title: "Specification Management",
        url: "/admin/1/specification-management",
        icon: LayoutList,
      },
      {
        title: "Project Pool",
        url: "#",
        icon: Target,
        items: [
          {
            title: "All Transactions",
            url: "/admin/1/project-pool/all-transactions",
          },
          {
            title: "Compare Quotations",
            url: "/admin/1/project-pool/compare-quotations",
          },
          {
            title: "Import Quotations",
            url: "/admin/1/project-pool/import-quotations",
          },
        ],
      },
      {
        title: "Roles and Permissions",
        url: "/admin/1/roles-and-permissions",
        icon: LockKeyhole,
      },
      {
        title: "Support & Training",
        url: "#",
        icon: Headset,
        items: [
          {
            title: "Resources",
            url: "/admin/1/support-and-training/resources",
          },
          {
            title: "Helpdesk",
            url: "/admin/1/support-and-training/helpdesk",
          },
          {
            title: "Training",
            url: "/admin/1/support-and-training/training",
          },
          {
            title: "Knowledge Base",
            url: "/admin/1/support-and-training/knowledge-base",
          },
          {
            title: "Feedback",
            url: "/admin/1/support-and-training/feedback",
          },
        ],
      },
      {
        title: "Notice",
        url: "/admin/1/notice",
        icon: Bell,
      },
      {
        title: "Content",
        url: "/admin/1/content-management",
        icon: TableOfContents,
      },
    ],
  };

  return (
    <Sidebar variant="inset" className="font-nunito">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <img
                src={logo}
                alt="Zettabid"
                loading="lazy"
                className="object-contain w-[10rem] h-full"
              />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent style={{ scrollbarWidth: "thin" }}>
        <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {data?.navMain.map((item) => {
              const hasSubItems = item.items && item.items.length > 0;

              return (
                <Collapsible
                  key={item?.title}
                  asChild
                  defaultOpen={item?.isActive}
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    {hasSubItems ? (
                      <>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton tooltip={item?.title}>
                            {item?.icon && <item.icon />}
                            <span>{item?.title}</span>
                            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.items?.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton asChild>
                                  <Link to={subItem.url}>
                                    <span>{subItem.title}</span>
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </>
                    ) : (
                      <SidebarMenuButton asChild tooltip={item?.title}>
                        <Link to={item.url}>
                          {item?.icon && <item.icon />}
                          <span>{item?.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                </Collapsible>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={data?.user?.avatar}
                      alt={data?.user?.name}
                    />
                    <AvatarFallback className="rounded-lg uppercase">
                      {getShortName(data?.user?.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold capitalize">
                      {data?.user?.name}
                    </span>
                    <span className="truncate text-xs">
                      {data?.user?.email}
                    </span>
                  </div>
                  <ChevronsUpDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg font-nunito"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage
                        src={data?.user?.avatar}
                        alt={data?.user?.name}
                      />
                      <AvatarFallback className="rounded-lg uppercase">
                        {getShortName(data?.user?.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold capitalize">
                        {data?.user?.name}
                      </span>
                      <span className="truncate text-xs">
                        {data?.user?.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Upgrade to Pro
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <BadgeCheck className="w-4 h-4 mr-2" />
                    Account
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default DashboardSidebar;
