import { apiRequest } from "@/utils/apiRequest";
import { getAccessTokenByUserType } from "@/utils/setCookies";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const getVendorBusinessCategory = async () => {
  const accessToken = getAccessTokenByUserType("vendor");
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/business-category`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const kycVerificationRequest = async (data) => {
  const accessToken = getAccessTokenByUserType("vendor");
  console.log("Token:", accessToken);

  // const formData = objectToFormData(data);
  // for (let [key, value] of formData.entries()) {
  //   console.log(key, value);
  // }

  const response = await fetch(`${BASE_URL}/vendor/company-profile`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  });
  console.log(response);

  return response;
};

export const getSpecifications = async () => {
  const accessToken = getAccessTokenByUserType("vendor");
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/specifications`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const submitQuotation = async (formData) => {
  const accessToken = getAccessTokenByUserType("vendor");

  // Create FormData for file uploads
  const submitData = new FormData();

  // Add form fields
  // submitData.append('customer_specification_id', formData.specificationId);
  submitData.append('product_details', formData.productDetails);
  submitData.append('price', formData.price);
  submitData.append('quantity', formData.quantity);
  submitData.append('delivery_date', formData.deliveryTimeline);
  submitData.append('terms_conditions', formData.termsAndConditions);

  // Add image files
  formData.productImages.forEach((file, index) => {
    submitData.append(`product_images[${index}]`, file);
  });

  formData.certifications.forEach((file, index) => {
    submitData.append(`product_certifications[${index}]`, file);
  });

  const response = await apiRequest({
    url: `${BASE_URL}/vendor/quotations`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: submitData,
    isFormData: true,
  });

  return response;
};
