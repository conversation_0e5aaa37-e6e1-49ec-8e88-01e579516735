import { useState } from "react";
import { Search, Download } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { useCSVExport, useExcelExport, usePDFExport } from "@/hooks/admin/useExportSimple";

export default function CompareQuotationsPage() {
  // Export hooks
  const exportToCSV = useCSVExport();
  const exportToExcel = useExcelExport();
  const exportToPDF = usePDFExport();

  // State for selected transactions to compare
  const [selectedForComparison, setSelectedForComparison] = useState([]);

  // Sample transaction data (same as in All Transactions)
  const transactions = [
    {
      id: "TRX-001",
      specification: "Office Furniture Set",
      amount: 5000,
      userRole: "Customer",
      poNumber: "PO-2023-001",
      status: "Success",
      date: "2023-10-15",
      details: {
        specificationUrl: "/documents/spec-001.pdf",
        items: [
          { name: "Executive Desk", price: 2000 },
          { name: "Office Chair", price: 1500 },
          { name: "Bookshelf", price: 1500 },
        ],
        vendor: "Office Solutions Inc.",
        deliveryTime: "2 weeks",
      },
    },
    {
      id: "TRX-002",
      specification: "IT Equipment",
      amount: 12000,
      userRole: "Vendor",
      poNumber: "PO-2023-002",
      status: "Failed",
      date: "2023-10-20",
      details: {
        specificationUrl: "/documents/spec-002.pdf",
        items: [
          { name: "Laptops (5x)", price: 7500 },
          { name: "Monitors (5x)", price: 2500 },
          { name: "Peripherals", price: 2000 },
        ],
        vendor: "Tech Supplies Co.",
        deliveryTime: "1 week",
      },
    },
    {
      id: "TRX-003",
      specification: "Conference Room Setup",
      amount: 8500,
      userRole: "Customer",
      poNumber: "PO-2023-003",
      status: "Success",
      date: "2023-11-05",
      details: {
        specificationUrl: "/documents/spec-003.pdf",
        items: [
          { name: "Conference Table", price: 3000 },
          { name: "Chairs (10x)", price: 3500 },
          { name: "Projector System", price: 2000 },
        ],
        vendor: "Office Solutions Inc.",
        deliveryTime: "3 weeks",
      },
    },
    {
      id: "TRX-004",
      specification: "Software Licenses",
      amount: 4500,
      userRole: "Vendor",
      poNumber: "PO-2023-004",
      status: "Success",
      date: "2023-11-10",
      details: {
        specificationUrl: "/documents/spec-004.pdf",
        items: [
          { name: "Design Software (3x)", price: 2700 },
          { name: "Project Management Tool", price: 1800 },
        ],
        vendor: "Software Solutions Ltd.",
        deliveryTime: "Immediate",
      },
    },
    {
      id: "TRX-005",
      specification: "Office Renovation",
      amount: 25000,
      userRole: "Customer",
      poNumber: "PO-2023-005",
      status: "Failed",
      date: "2023-11-15",
      details: {
        specificationUrl: "/documents/spec-005.pdf",
        items: [
          { name: "Flooring", price: 10000 },
          { name: "Painting", price: 5000 },
          { name: "Electrical Work", price: 7000 },
          { name: "Miscellaneous", price: 3000 },
        ],
        vendor: "Construction Experts Inc.",
        deliveryTime: "1 month",
      },
    },
  ];

  // Toggle transaction selection for comparison
  const toggleTransactionSelection = (transaction) => {
    if (selectedForComparison.some((t) => t.id === transaction.id)) {
      setSelectedForComparison(
        selectedForComparison.filter((t) => t.id !== transaction.id)
      );
    } else {
      if (selectedForComparison.length < 3) {
        setSelectedForComparison([...selectedForComparison, transaction]);
      } else {
        toast({
          title: "Selection Limit",
          description: "You can compare up to 3 quotations at a time.",
          variant: "destructive",
        });
      }
    }
  };

  // Reset comparison selection
  const resetComparison = () => {
    setSelectedForComparison([]);
  };

  // Handle export of selected comparisons
  const handleExportComparison = () => {
    if (selectedForComparison.length === 0) {
      toast({
        title: "No Data to Export",
        description: "Please select quotations to compare before exporting.",
        variant: "destructive",
      });
      return;
    }

    const fileName = `quotation_comparison_${new Date().toISOString().split('T')[0]}`;
    const success = exportToExcel(selectedForComparison, fileName);

    if (success) {
      toast({
        title: "Export Complete",
        description: `Comparison data exported successfully as Excel file.`,
      });
    } else {
      toast({
        title: "Export Failed",
        description: "There was an error exporting your comparison data.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Compare Quotations</h1>
        <div className="flex items-center gap-2">
          <span className="mr-2 text-sm text-muted-foreground">
            {selectedForComparison.length} of 3 selected
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportComparison}
            disabled={selectedForComparison.length === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={resetComparison}>
            Reset
          </Button>
        </div>
      </div>

      {selectedForComparison.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {selectedForComparison.map((transaction) => (
            <Card key={transaction.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h4 className="font-semibold">
                      {transaction.specification}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {transaction.id}
                    </p>
                  </div>
                  <Badge
                    variant={
                      transaction.status === "Success"
                        ? "success"
                        : "destructive"
                    }
                  >
                    {transaction.status}
                  </Badge>
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Vendor</p>
                    <p>{transaction.details.vendor}</p>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">
                      Total Amount
                    </p>
                    <p className="text-xl font-bold">
                      ${transaction.amount.toLocaleString()}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">
                      Delivery Time
                    </p>
                    <p>{transaction.details.deliveryTime}</p>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">Items</p>
                    <ul className="list-disc list-inside">
                      {transaction.details.items.map((item, index) => (
                        <li key={index} className="text-sm">
                          {item.name} - ${item.price.toLocaleString()}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center p-8 border rounded-md mb-6">
          <p className="text-muted-foreground mb-4">
            Select transactions to compare from the table below
          </p>
          <Search className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            You can select up to 3 quotations to compare
          </p>
        </div>
      )}

      {/* Transactions Table for Selection */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Specification</TableHead>
              <TableHead>Quotation Amount</TableHead>
              <TableHead>User Role</TableHead>
              <TableHead>PO Number</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Select</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.length > 0 ? (
              transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">
                    {transaction.id}
                  </TableCell>
                  <TableCell>{transaction.specification}</TableCell>
                  <TableCell>${transaction.amount.toLocaleString()}</TableCell>
                  <TableCell>{transaction.userRole}</TableCell>
                  <TableCell>{transaction.poNumber}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        transaction.status === "Success"
                          ? "success"
                          : "destructive"
                      }
                    >
                      {transaction.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedForComparison.some(
                        (t) => t.id === transaction.id
                      )}
                      onChange={() => toggleTransactionSelection(transaction)}
                      className="h-4 w-4"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={8}
                  className="h-24 text-center"
                >
                  No transactions found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
