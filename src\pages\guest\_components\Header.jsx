import logo from "@/assets/png/logo.png";
import { Link, useLocation } from "react-router-dom";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { RxHamburgerMenu } from "react-icons/rx";

const Header = () => {
  const location = useLocation();

  return (
    <>
      <div className="flex items-center justify-between px-4 py-4 sm:px-8 xl:px-32">
        <div className="flex justify-between w-full lg:py-2">
          <div className="w-[9rem] sm:w-[12rem]">
            <img
              src={logo}
              alt="Zettabid"
              className="object-contain w-full h-full"
            />
          </div>
          <div className="justify-center items-center font-semibold gap-4 sm:gap-6 md:gap-[40px] hidden lg:flex">
            <Link
              className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${location.pathname === "/" ? "text-primary" : "text-darkTeal"}
              `}
              to="/"
            >
              Home
            </Link>
            <Link
              className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/about"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
              to="/about"
            >
              About
            </Link>
            <Link
              className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/resources"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
              to="/"
            >
              Resources
            </Link>
            <Link
              className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/template"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
              to="/"
            >
              Template
            </Link>
            <Link
              className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/contact"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
              to="/contact"
            >
              Contact
            </Link>
          </div>
          <div className="hidden lg:block">
            <Link
              to="/customer/signin"
              className="font-nunito text-navy text-bodyMedium font-extrabold bg-primary px-8 py-3 rounded-xl tracking-wider"
            >
              Get Started
            </Link>
          </div>
        </div>

        <div className="lg:hidden">
          <Sheet>
            <SheetTrigger>
              <RxHamburgerMenu className="w-4 h-4 sm:w-8 sm:h-8 text-primary" />
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle></SheetTitle>
                <SheetDescription asChild>
                  <div className="flex flex-col items-center gap-8 text-center">
                    <div className="grid gap-7 font-semibold">
                      <SheetClose asChild>
                        <Link
                          className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${location.pathname === "/" ? "text-primary" : "text-darkTeal"}
              `}
                          to="/"
                        >
                          Home
                        </Link>
                      </SheetClose>
                      <SheetClose asChild>
                        <Link
                          className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/about"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
                          to="/about"
                        >
                          About
                        </Link>
                      </SheetClose>
                      <SheetClose asChild>
                        <Link
                          className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/resources"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
                          to="/"
                        >
                          Resources
                        </Link>
                      </SheetClose>
                      <SheetClose asChild>
                        <Link
                          className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/template"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
                          to="/"
                        >
                          Template
                        </Link>
                      </SheetClose>
                      <SheetClose asChild>
                        <Link
                          className={`list-none hover:cursor-pointer hover:text-primary font-poppins leading-3 text-bodyMedium font-medium
              ${
                location.pathname === "/contact"
                  ? "text-primary"
                  : "text-darkTeal"
              }
              `}
                          to="/"
                        >
                          Contact
                        </Link>
                      </SheetClose>
                    </div>
                    <SheetClose asChild>
                      <Link
                        to="/customer/signin"
                        className="w-full font-nunito text-navy text-bodyMedium font-extrabold bg-primary px-8 py-3 rounded-xl tracking-wider"
                      >
                        Get Started
                      </Link>
                    </SheetClose>
                  </div>
                </SheetDescription>
              </SheetHeader>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </>
  );
};

export default Header;
