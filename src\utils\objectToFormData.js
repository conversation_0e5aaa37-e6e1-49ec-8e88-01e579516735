export const objectToFormData =(obj, formData = new FormData(), parentKey = '')=> {
    if (obj && typeof obj === 'object' && !(obj instanceof File)) {
      Object.entries(obj).forEach(([key, value]) => {
        const formKey = parentKey ? `${parentKey}[${key}]` : key;
  
        if (value instanceof File || value instanceof Blob) {
          formData.append(formKey, value);
        } else if (Array.isArray(value)) {
          value.forEach((item, index) => {
            objectToFormData(item, formData, `${formKey}[${index}]`);
          });
        } else if (typeof value === 'object' && value !== null) {
          objectToFormData(value, formData, formKey);
        } else {
          formData.append(formKey, value);
        }
      });
    } else {
      formData.append(parentKey, obj);
    }
    return formData;
  }