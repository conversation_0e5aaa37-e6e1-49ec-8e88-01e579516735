import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useState, useEffect } from "react";
import { useGetBusinessCategory } from "@/services/customer/query";

export function CategoryStep({ currentSpec, onCategoryChange, onSubCategoryChange }) {
  const { data } = useGetBusinessCategory();

  const categories = data?.data || [];
  // console.log(data?.business_categories);

  const [selectedCategoryId, setSelectedCategoryId] = useState(
    currentSpec?.category || ""
  );
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(
    currentSpec?.subcategory || ""
  );
  const [subCategories, setSubCategories] = useState([]);

  // Update sub-categories when category changes
  useEffect(() => {
    const category = categories.find(
      (cat) => cat.id === Number(selectedCategoryId)
    );
    if (category && category.sub_categories?.length > 0) {
      setSubCategories(category.sub_categories);
    } else {
      setSubCategories([]);
    }
    // Only reset subcategory if category actually changed
    if (selectedCategoryId !== currentSpec?.category) {
      setSelectedSubCategoryId("");
    }
  }, [selectedCategoryId, categories]);

  const handleCategoryChange = (value) => {
    setSelectedCategoryId(value);
    // Find category name and pass both ID and name
    const category = categories.find(cat => cat.id === Number(value));
    onCategoryChange({
      id: value,
      name: category?.name || ""
    });
  };

  const handleSubCategoryChange = (value) => {
    setSelectedSubCategoryId(value);
    // Find subcategory name and pass both ID and name
    const subCategory = subCategories.find(sub => sub.id === Number(value));
    onSubCategoryChange({
      id: value,
      name: subCategory?.name || ""
    });
  };

  return (
    <div className="flex gap-4">
      {/* Category Selection */}
      <Card className="w-1/2">
        <CardHeader>
          <CardTitle>Select Category</CardTitle>
          <CardDescription>
            Choose a category for your specification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedCategoryId}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={String(category.id)}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Sub-category Selection */}
      <Card className="w-1/2">
        <CardHeader>
          <CardTitle>Select Sub-category</CardTitle>
          <CardDescription>
            Choose a sub-category related to the selected category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedSubCategoryId}
            onValueChange={handleSubCategoryChange}
            disabled={subCategories.length === 0}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a sub-category" />
            </SelectTrigger>
            <SelectContent>
              {subCategories.map((sub) => (
                <SelectItem key={sub.id} value={String(sub.id)}>
                  {sub.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>
    </div>
  );
}
