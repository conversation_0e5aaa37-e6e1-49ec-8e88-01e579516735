import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  // Notice mutations
  createNotice,
  updateNotice,
  deleteNotice,
  // Training mutations
  createTrainingSession,
  updateTrainingSession,
  deleteTrainingSession,
  // Permission mutations
  createPermission,
  updatePermission,
  deletePermission,
  // Role mutations
  createRole,
  updateRole,
  deleteRole,
} from "./api";

// Notice Mutations
export function useCreateNoticeMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => createNotice(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notices"] });
    },
    onError: (error) => {
      console.error("Create notice failed:", error);
    },
  });
}

export function useUpdateNoticeMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => updateNotice(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notices"] });
    },
    onError: (error) => {
      console.error("Update notice failed:", error);
    },
  });
}

export function useDeleteNoticeMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => deleteNotice(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notices"] });
    },
    onError: (error) => {
      console.error("Delete notice failed:", error);
    },
  });
}

// Training Mutations
export function useCreateTrainingMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => createTrainingSession(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["training-sessions"] });
    },
    onError: (error) => {
      console.error("Create training session failed:", error);
    },
  });
}

export function useUpdateTrainingMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => updateTrainingSession(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["training-sessions"] });
    },
    onError: (error) => {
      console.error("Update training session failed:", error);
    },
  });
}

export function useDeleteTrainingMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => deleteTrainingSession(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["training-sessions"] });
    },
    onError: (error) => {
      console.error("Delete training session failed:", error);
    },
  });
}

// Permission Mutations
export function useCreatePermissionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => createPermission(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["roles"] }); // Also invalidate roles since they depend on permissions
    },
    onError: (error) => {
      console.error("Create permission failed:", error);
      // The error will be handled by the component
    },
  });
}

export function useUpdatePermissionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updatePermission(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["roles"] }); // Also invalidate roles since they depend on permissions
    },
    onError: (error) => {
      console.error("Update permission failed:", error);
    },
  });
}

export function useDeletePermissionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deletePermission(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["roles"] }); // Also invalidate roles since they depend on permissions
    },
    onError: (error) => {
      console.error("Delete permission failed:", error);
    },
  });
}

// Role Mutations
export function useCreateRoleMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
    },
    onError: (error) => {
      console.error("Create role failed:", error);
    },
  });
}

export function useUpdateRoleMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
    },
    onError: (error) => {
      console.error("Update role failed:", error);
    },
  });
}

export function useDeleteRoleMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id) => deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
    },
    onError: (error) => {
      console.error("Delete role failed:", error);
    },
  });
}
