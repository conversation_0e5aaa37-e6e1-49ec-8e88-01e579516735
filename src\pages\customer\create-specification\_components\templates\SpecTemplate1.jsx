import React from "react";
import { Mail, MapPin, Phone } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

const SpecTemplate1 = ({ currentSpec, handleChange }) => {
  return (
    <div className="bg-white p-6 w-full max-w-3xl rounded-md shadow-lg mx-auto">
      <div className="flex justify-between">
        {/* Full Name Section */}
        <div className="mb-6">
          <Input
            value={currentSpec.fullName}
            id="fullname"
            onChange={(e) => handleChange("fullName", e.target.value)}
            className="text-2xl font-bold mb-2 font-inter p-0 border-0 focus:outline-none focus:ring-0"
          />
          <div className="space-y-1">
            <div className="flex gap-2">
              <MapPin />
              <Input
                id="address"
                value={currentSpec.address}
                onChange={(e) => handleChange("address", e.target.value)}
                defaultValue={currentSpec.address}
                className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
            <div className="flex gap-2">
              <Mail />
              <Input
                id="email"
                value={currentSpec.email}
                onChange={(e) => handleChange("email", e.target.value)}
                className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
            <div className="flex gap-2">
              <Phone />
              <Input
                id="phone"
                value={currentSpec.phone}
                onChange={(e) => handleChange("phone", e.target.value)}
                className="w-full text-base h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
          </div>
        </div>

        {/* Product Specification Header */}
        <div className="mb-6">
          <h1 className="text-xl font-bold">Product Specification</h1>
          <div className="text-sm space-y-1">
            <div>
              Specification ID:
              <Input
                id="specificationId"
                defaultValue="#000123"
                className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              Created Date:
              <Input
                id="createdDate"
                type="date"
                defaultValue="2022-12-22"
                className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              Due Date:
              <Input
                id="dueDate"
                type="date"
                defaultValue="2022-12-22"
                className="w-full h-4 p-0 border-0 focus:outline-none focus:ring-0"
              />
            </div>
          </div>
        </div>
      </div>

      <form className="space-y-4">
        {/* Category */}
        <div className="flex items-center">
          <Label htmlFor="category" className="font-bold">
            Category
          </Label>
          <Input
            id="category"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Product Name */}
        <div className="flex items-center border-b">
          <Label htmlFor="productName" className="font-bold">
            Product Name
          </Label>
          <Input
            id="productName"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="Laptop i3 10 Gen Iris Graphics"
          />
        </div>

        {/* Product Code */}
        <div className="flex items-center border-b">
          <Label htmlFor="productCode" className="font-bold">
            Product Code
          </Label>
          <Input
            id="productCode"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Description */}
        <div className="border-b">
          <Label htmlFor="description" className="font-bold">
            Description
          </Label>
          <Textarea
            id="description"
            className="flex-1 min-h-[100px] resize-none p-0 border-0 focus:outline-none focus:ring-0"
            defaultValue="Lorem ipsum dolor sit amet consectetur. Amet lacus amet sed lacus. Blandit ut nulla faucibus amet enim. In massa tortor id egestas. Non quis accumsan in id diam malesuada tellus. Viverra viverra amet lectus aliquam urna bibendum. Fermentum condimentum non pellentesque."
          />
        </div>

        {/* Brand */}
        <div className="flex items-center border-b">
          <Label htmlFor="brand" className="font-bold">
            Brand
          </Label>
          <Input
            id="brand"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Type/Model */}
        <div className="flex items-center border-b">
          <Label htmlFor="typeModel" className="font-bold">
            Type/Model
          </Label>
          <Input
            id="typeModel"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Manufacturer */}
        <div className="flex items-center border-b">
          <Label htmlFor="manufacturer" className="font-bold">
            Manufacturer
          </Label>
          <Input
            id="manufacturer"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Country of Origin */}
        <div className="flex items-center border-b">
          <Label htmlFor="countryOrigin" className="font-bold">
            Country of Origin
          </Label>
          <Input
            id="countryOrigin"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Design Specification Header */}
        <div className="font-bold text-lg pt-4">Design Specification</div>

        {/* Dimensions */}
        <div className="flex items-center border-b">
          <Label htmlFor="dimensions" className="font-bold">
            Dimensions
          </Label>
          <Input
            id="dimensions"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Weight */}
        <div className="flex items-center border-b">
          <Label htmlFor="weight" className="font-bold">
            Weight
          </Label>
          <Input
            id="weight"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Variant */}
        <div className="flex items-center border-b">
          <Label htmlFor="variant" className="font-bold">
            Variant
          </Label>
          <Input
            id="variant"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Delivery Header */}
        <div className="font-bold text-lg pt-4">Delivery</div>

        {/* Delivery Date */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryDate" className="font-bold">
            Delivery Date
          </Label>
          <Input
            id="deliveryDate"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Delivery Location */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryLocation" className="font-bold">
            Delivery Location
          </Label>
          <Input
            id="deliveryLocation"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Delivery Instructions */}
        <div className="flex items-center border-b">
          <Label htmlFor="deliveryInstructions" className="font-bold">
            Delivery Instructions
          </Label>
          <Input
            id="deliveryInstructions"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="#7654385284526757531"
          />
        </div>

        {/* Budget/Price Range */}
        <div className="flex items-center border-b">
          <Label htmlFor="budgetPriceRange" className="font-bold">
            Budget/Price Range
          </Label>
          <Input
            id="budgetPriceRange"
            className="flex-1 h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="Estimated budget or price range"
          />
        </div>

        {/* Pricing Terms */}
        <div className="flex items-center border-b">
          <Label htmlFor="pricingTerms" className="font-bold">
            Pricing Terms
          </Label>
          <Input
            id="pricingTerms"
            className="h-4 p-0 border-0 focus:outline-none focus:ring-0 text-right"
            defaultValue="Estimated budget or price range"
          />
        </div>

        {/* Additional Instructions */}
        <div className="">
          <Label htmlFor="additionalInstructions" className="font-bold">
            Additional Instructions
          </Label>
          <Textarea
            id="additionalInstructions"
            className="p-0 min-h-[100px] resize-non ep-0 border-0 focus:outline-none focus:ring-0"
            defaultValue="Lorem ipsum dolor sit amet consectetur. Consequat lacus facillis dictum enim. Blandit diam sed consequat amet. Amet viverra id porttitor non. Consequat sed quis egestas. Nunc quis mattis. Pilt pellentesque sem massa porttitor. Nunc. Consequat purus facilisis lorem vel nunc ut sapien. Adipiscing justo ligit bibikum condimentum."
          />
        </div>
      </form>
    </div>
  );
};

export default SpecTemplate1;
