import { Button } from "@/components/ui/button";
import PropTypes from "prop-types";
import { Controller, useForm } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";

const ChooseCustomerOrVendor = ({ onNext }) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      customerOrVendor: "customer", // Initialize empty string
    },
  });

  const navigate = useNavigate();

  const onSubmit = (data) => {
    console.log("Submitted");
    console.log(data);
    reset();
    if (data.customerOrVendor === "customer") {
      navigate("/customer/1/feeds");
    }
    if (data.customerOrVendor === "vendor") {
      onNext();
    }
  };
  return (
    <div>
      <form className="space-y-6 mt-10" onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name="customerOrVendor"
          control={control}
          rules={{
            required: {
              value: true,
              message: "Choose one option",
            },
          }}
          render={({ field }) => (
            <>
              <RadioGroup onValueChange={field.onChange} value={field.value}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="customer" id="r1" />
                  <Label htmlFor="r1" className="text-md">
                    Customer
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="vendor" id="r2" />
                  <Label htmlFor="r2" className="text-md">
                    Vendor
                  </Label>
                </div>
              </RadioGroup>
              {errors?.customerOrVendor && (
                <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                  {errors?.customerOrVendor?.message}
                </p>
              )}
            </>
          )}
        />

        <div>
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
            type="submit"
          >
            Continue
          </Button>
        </div>
      </form>
    </div>
  );
};

ChooseCustomerOrVendor.propTypes = {
  onNext: PropTypes.func.isRequired,
};

export default ChooseCustomerOrVendor;
