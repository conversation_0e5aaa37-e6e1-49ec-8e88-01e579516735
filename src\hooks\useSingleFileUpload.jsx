import { useState, useRef } from "react";

const useSingleFileUpload = (acceptedTypes = [".pdf"]) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const fileInputRef = useRef(null);

  const openFileDialog = (e) => {
    e.preventDefault();
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
    fileInputRef.current.value = null;
  };

  const handleFilesSelected = (e) => {
    e.preventDefault();
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setUploadedFile(files[0]); // Only keep the first file
    }
    fileInputRef.current.value = null;
  };

  return {
    fileInputRef,
    uploadedFile, // Only a single file is stored
    openFileDialog,
    handleFilesSelected,
    acceptedTypes: acceptedTypes.join(","), // Accepted file types
  };
};

export default useSingleFileUpload;
