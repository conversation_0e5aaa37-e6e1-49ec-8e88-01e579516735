"use client";

import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function BankDetailsForm({ onSubmit, onPrevious }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onFormSubmit = (data) => {
    onSubmit({ bankDetails: data }); // Pass the form data to the parent component
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
      {/* Account Number */}
      <div className="space-y-2">
        <Label htmlFor="accountNumber">Account Number</Label>
        <Input
          id="accountNumber"
          {...register("accountNumber", {
            required: "Account number is required",
          })}
        />
        {errors.accountNumber && (
          <p className="text-sm text-red-500">{errors.accountNumber.message}</p>
        )}
      </div>

      {/* Account Holder Name */}
      <div className="space-y-2">
        <Label htmlFor="accountHolderName">Account Holder Name</Label>
        <Input
          id="accountHolderName"
          {...register("accountHolderName", {
            required: "Account holder name is required",
          })}
        />
        {errors.accountHolderName && (
          <p className="text-sm text-red-500">
            {errors.accountHolderName.message}
          </p>
        )}
      </div>

      {/* Bank Name */}
      <div className="space-y-2">
        <Label htmlFor="bankName">Bank Name</Label>
        <Input
          id="bankName"
          {...register("bankName", { required: "Bank name is required" })}
        />
        {errors.bankName && (
          <p className="text-sm text-red-500">{errors.bankName.message}</p>
        )}
      </div>

      {/* Bank Branch */}
      <div className="space-y-2">
        <Label htmlFor="bankBranch">Bank Branch</Label>
        <Input
          id="bankBranch"
          {...register("bankBranch", { required: "Bank branch is required" })}
        />
        {errors.bankBranch && (
          <p className="text-sm text-red-500">{errors.bankBranch.message}</p>
        )}
      </div>

      <div className="flex justify-end gap-6 mt-8">
        <Button
          className="w-48"
          type="button"
          variant="outline"
          onClick={onPrevious}
        >
          Previous
        </Button>
        <Button className="w-48" type="submit">
          Next
        </Button>
      </div>
    </form>
  );
}
