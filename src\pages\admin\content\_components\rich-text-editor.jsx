import { useState, useEffect } from "react";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Link,
  ImageIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export function RichTextEditor({ initialContent = "", onChange }) {
  const [content, setContent] = useState(initialContent);
  const [activeTab, setActiveTab] = useState("visual");
  const [editorRef, setEditorRef] = useState(null);

  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    onChange(newContent);
  };

  const handleHTMLChange = (e) => {
    handleContentChange(e.target.value);
  };

  const insertFormat = (format) => {
    if (!editorRef) return;

    const selection = window.getSelection();
    const range = selection.getRangeAt(0);
    const selectedText = range.toString();

    let formattedText = "";
    switch (format) {
      case "bold":
        formattedText = `<strong>${selectedText}</strong>`;
        break;
      case "italic":
        formattedText = `<em>${selectedText}</em>`;
        break;
      case "ul":
        formattedText = `<ul>\n  <li>${selectedText}</li>\n</ul>`;
        break;
      case "ol":
        formattedText = `<ol>\n  <li>${selectedText}</li>\n</ol>`;
        break;
      case "align-left":
        formattedText = `<div style="text-align: left">${selectedText}</div>`;
        break;
      case "align-center":
        formattedText = `<div style="text-align: center">${selectedText}</div>`;
        break;
      case "align-right":
        formattedText = `<div style="text-align: right">${selectedText}</div>`;
        break;
      default:
        formattedText = selectedText;
    }

    // Create a new document fragment with the formatted HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = formattedText;
    const fragment = document.createDocumentFragment();
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }

    // Replace the selected text with the formatted HTML
    range.deleteContents();
    range.insertNode(fragment);

    // Update the content state with the new HTML
    handleContentChange(editorRef.innerHTML);
  };

  const insertLink = (url, text) => {
    if (!editorRef) return;

    const linkHTML = `<a href="${url}" target="_blank">${text}</a>`;
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);

    // Create a new document fragment with the link HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = linkHTML;
    const fragment = document.createDocumentFragment();
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }

    // Replace the selected text with the link HTML
    range.deleteContents();
    range.insertNode(fragment);

    // Update the content state with the new HTML
    handleContentChange(editorRef.innerHTML);
  };

  const insertImage = (url, alt) => {
    if (!editorRef) return;

    const imgHTML = `<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto;" />`;
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);

    // Create a new document fragment with the image HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = imgHTML;
    const fragment = document.createDocumentFragment();
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }

    // Replace the selected text with the image HTML
    range.deleteContents();
    range.insertNode(fragment);

    // Update the content state with the new HTML
    handleContentChange(editorRef.innerHTML);
  };

  return (
    <div className="border rounded-md">
      <Tabs
        defaultValue="visual"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <div className="flex items-center justify-between border-b px-3">
          <TabsList className="h-10">
            <TabsTrigger value="visual" className="text-xs">
              Visual
            </TabsTrigger>
            <TabsTrigger value="html" className="text-xs">
              HTML
            </TabsTrigger>
          </TabsList>

          {activeTab === "visual" && (
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => insertFormat("bold")}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => insertFormat("italic")}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => insertFormat("ul")}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => insertFormat("ol")}
              >
                <ListOrdered className="h-4 w-4" />
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Link className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none">Insert Link</h4>
                      <p className="text-sm text-muted-foreground">
                        Add a hyperlink to the selected text.
                      </p>
                    </div>
                    <div className="grid gap-2">
                      <div className="grid grid-cols-3 items-center gap-4">
                        <Label htmlFor="link-text">Text</Label>
                        <Input
                          id="link-text"
                          defaultValue="Link text"
                          className="col-span-2"
                        />
                      </div>
                      <div className="grid grid-cols-3 items-center gap-4">
                        <Label htmlFor="link-url">URL</Label>
                        <Input
                          id="link-url"
                          defaultValue="https://"
                          className="col-span-2"
                        />
                      </div>
                    </div>
                    <Button
                      onClick={() => {
                        const text = document.getElementById("link-text").value;
                        const url = document.getElementById("link-url").value;
                        insertLink(url, text);
                      }}
                    >
                      Insert Link
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <ImageIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none">Insert Image</h4>
                      <p className="text-sm text-muted-foreground">
                        Add an image to your content.
                      </p>
                    </div>
                    <div className="grid gap-2">
                      <div className="grid grid-cols-3 items-center gap-4">
                        <Label htmlFor="image-url">Image URL</Label>
                        <Input
                          id="image-url"
                          defaultValue="https://"
                          className="col-span-2"
                        />
                      </div>
                      <div className="grid grid-cols-3 items-center gap-4">
                        <Label htmlFor="image-alt">Alt Text</Label>
                        <Input
                          id="image-alt"
                          defaultValue="Image description"
                          className="col-span-2"
                        />
                      </div>
                    </div>
                    <Button
                      onClick={() => {
                        const url = document.getElementById("image-url").value;
                        const alt = document.getElementById("image-alt").value;
                        insertImage(url, alt);
                      }}
                    >
                      Insert Image
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              <div className="flex items-center border-l ml-1 pl-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => insertFormat("align-left")}
                >
                  <AlignLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => insertFormat("align-center")}
                >
                  <AlignCenter className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => insertFormat("align-right")}
                >
                  <AlignRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        <TabsContent value="visual" className="p-0">
          <div
            className="min-h-[200px] max-h-[500px] overflow-y-auto p-4 focus:outline-none"
            contentEditable
            dangerouslySetInnerHTML={{ __html: content }}
            onInput={(e) => handleContentChange(e.target.innerHTML)}
            ref={setEditorRef}
          />
        </TabsContent>

        <TabsContent value="html" className="p-0">
          <Textarea
            className="min-h-[200px] max-h-[500px] p-4 font-mono text-sm rounded-none border-0 resize-none focus-visible:ring-0"
            value={content}
            onChange={handleHTMLChange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
