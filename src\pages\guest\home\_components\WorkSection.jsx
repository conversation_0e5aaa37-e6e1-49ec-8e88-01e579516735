import step1 from "@/assets/svg/register.svg";
import step2 from "@/assets/svg/create-specification.svg";
import step3 from "@/assets/svg/create-quotation.svg";
import step4 from "@/assets/svg/payment.svg";
import step5 from "@/assets/svg/delivery.svg";
import StepSection from "./StepSection";

const WorkSection = () => {
  return (
    <div className="mt-24 px-4 sm:px-8 xl:px-32 bg-darkLight py-10">
      <span className="font-nunito font-semibold text-heading6 text-green1">{`How Zettabid Works`}</span>
      <div className="flex flex-col lg:flex-row gap-10 lg:justify-between w-full">
        <div className="w-full mt-12">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy lg:w-[38.375rem] leading-[2.875rem]">{`Quote Your Client Effortlessly and Efficiently`}</h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-darkTeal max-w-[50.375rem]">{`Streamline Your Quotations with Ease. Efficiency meets Precision. Elevate Your Processes with our Intuitive Software. Empower Your Team. Unleash Growth Potential.`}</p>
        </div>
      </div>
      <StepSection
        title="Lorem ipsum dolor sit
            amet."
        step="1"
        reverse={false}
        stepImg={step1}
        steps={[
          "Lorem ipsum dolor sit amet.",
          "Consectetur adipisicing elit.",
          "Sit amet consectetur adipisicing elit.",
        ]}
      />
      <StepSection
        title="Lorem ipsum dolor sit
            amet."
        step="2"
        reverse={true}
        stepImg={step2}
        steps={[
          "Lorem ipsum dolor sit amet.",
          "Consectetur adipisicing elit.",
          "Sit amet consectetur adipisicing elit.",
        ]}
      />
      <StepSection
        title="Lorem ipsum dolor sit
            amet."
        step="3"
        reverse={false}
        stepImg={step3}
        steps={[
          "Lorem ipsum dolor sit amet.",
          "Consectetur adipisicing elit.",
          "Sit amet consectetur adipisicing elit.",
        ]}
      />
      <StepSection
        title="Lorem ipsum dolor sit
            amet."
        step="4"
        reverse={true}
        stepImg={step4}
        steps={[
          "Lorem ipsum dolor sit amet.",
          "Consectetur adipisicing elit.",
          "Sit amet consectetur adipisicing elit.",
        ]}
      />
      <StepSection
        title="Lorem ipsum dolor sit
            amet."
        step="5"
        reverse={false}
        stepImg={step5}
        steps={[
          "Lorem ipsum dolor sit amet.",
          "Consectetur adipisicing elit.",
          "Sit amet consectetur adipisicing elit.",
        ]}
      />
      {/* <div className="w-full grid place-items-center py-10 mt-20">
        <div className="bg-yellow-100 w-full h-[15rem] xs:h-[20rem] sm:h-[25rem] md:h-[40rem] rounded-xl">
          <iframe
            width="100%"
            height="100%"
            src={`https://www.youtube.com/embed/rokGy0huYEA`}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture rounded-xl"
            allowFullScreen
            title="Embedded youtube"
          />
        </div>
      </div> */}
    </div>
  );
};

export default WorkSection;
