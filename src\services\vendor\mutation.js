import { useMutation, useQueryClient } from "@tanstack/react-query";
import { kycVerificationRequest, submitQuotation } from "./api";
import { setCookies } from "@/utils/setCookies";

export function useKycVerificationRequestMutation() {
  return useMutation({
    mutationFn: (data) => kycVerificationRequest(data),
    onSuccess: (data) => {
      console.log("✅ KYC submission successful:", data);
      // You can also trigger success toasts or redirects here
    },
    onError: (error) => {
      console.error("❌ KYC submission failed:", error.message);
    },
    onSettled: () => {
      console.log("🔁 Mutation settled (either success or error)");
    },
  });
}

export function useSubmitQuotationMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ specificationId, formData }) => submitQuotation(specificationId, formData),
    onSuccess: (response) => {
      console.log("✅ Quotation submission successful:", response);
      // Invalidate and refetch any related queries
      queryClient.invalidateQueries(["vendor-specifications"]);
      queryClient.invalidateQueries(["vendor-quotations"]);
    },
    onError: (error) => {
      console.error("❌ Quotation submission failed:", error.message);
    },
    onSettled: () => {
      console.log("🔁 Quotation mutation settled (either success or error)");
    },
  });
}

// export function useKycVerificationRequestMutation() {
//   return useMutation({
//     mutationFn: (data) => kycVerificationRequest(data),
//     onSettled: (response) => {
//     //   const {
//     //     token: accessToken,
//     //     user_type: userType,
//     //     data: { id: userId, name, email },
//     //   } = response;

//     //   // Set encrypted cookies
//     //   setCookies({ accessToken, userId, userType, name, email });

//     //   console.log("Login successful! Data stored in encrypted cookies.");
//     },
//     onError: (error) => {
//       console.error("Login failed:", error);
//     },
//   });
// }
