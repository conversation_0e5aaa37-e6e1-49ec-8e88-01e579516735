import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON>, Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const SupportTrainingFeedbackPage = () => {
  // Feedback state
  const [feedback, setFeedback] = useState([
    {
      id: 1,
      user: "<PERSON>",
      comment:
        "The new dashboard is much more intuitive and easier to navigate.",
      rating: 5,
      date: "2023-11-17",
    },
    {
      id: 2,
      user: "<PERSON>",
      comment:
        "Had some issues with the quotation comparison tool. It's a bit confusing to use.",
      rating: 3,
      date: "2023-11-16",
    },
    {
      id: 3,
      user: "<PERSON>",
      comment: "Great support team! They resolved my issue within hours.",
      rating: 5,
      date: "2023-11-15",
    },
    {
      id: 4,
      user: "Lisa Garcia",
      comment:
        "The file upload feature is too slow and sometimes fails without any error message.",
      rating: 2,
      date: "2023-11-14",
    },
  ]);

  // Calculate average rating
  const averageRating =
    feedback.length > 0
      ? (
          feedback.reduce((sum, item) => sum + item.rating, 0) / feedback.length
        ).toFixed(1)
      : 0;
  const [replyFeedback, setReplyFeedback] = useState(null);
  const [feedbackReplyText, setFeedbackReplyText] = useState("");
  const [showFeedbackReplyDialog, setShowFeedbackReplyDialog] = useState(false);

  // Handle feedback reply
  const handleFeedbackReply = () => {
    if (!feedbackReplyText.trim()) {
      toast({
        title: "Error",
        description: "Reply cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setFeedbackReplyText("");
    setShowFeedbackReplyDialog(false);

    toast({
      title: "Reply Sent",
      description: `Your reply to ${replyFeedback.user}'s feedback has been sent.`,
    });
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Feedback</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Average Rating:
            </span>
            <div className="flex items-center">
              <span className="font-medium mr-1">{averageRating}</span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-4 w-4 ${
                      star <= Math.round(averageRating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-muted-foreground"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Comment</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {feedback.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.user}</TableCell>
                  <TableCell>{item.comment}</TableCell>
                  <TableCell>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= item.rating
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-muted-foreground"
                          }`}
                        />
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{item.date}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setReplyFeedback(item);
                        setShowFeedbackReplyDialog(true);
                      }}
                    >
                      <Reply className="h-4 w-4 mr-2" />
                      Respond
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <Dialog
          open={showFeedbackReplyDialog}
          onOpenChange={setShowFeedbackReplyDialog}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Respond to Feedback</DialogTitle>
              <DialogDescription>
                Responding to {replyFeedback?.user}'s feedback
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="p-4 bg-muted rounded-md">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-4 w-4 ${
                        star <= (replyFeedback?.rating || 0)
                          ? "fill-yellow-400 text-yellow-400"
                          : "text-muted-foreground"
                      }`}
                    />
                  ))}
                </div>
                <p className="mt-2">{replyFeedback?.comment}</p>
              </div>
              <Label htmlFor="feedback-reply">Your Response</Label>
              <Textarea
                id="feedback-reply"
                placeholder="Type your response here..."
                value={feedbackReplyText}
                onChange={(e) => setFeedbackReplyText(e.target.value)}
                rows={4}
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowFeedbackReplyDialog(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleFeedbackReply}>
                <Send className="h-4 w-4 mr-2" />
                Send Response
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default SupportTrainingFeedbackPage;
