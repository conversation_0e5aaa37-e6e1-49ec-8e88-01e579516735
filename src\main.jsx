import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import "./index.css";
import QueryProvider from "@/context/QueryProvider";
import StoreProvider from "@/context/StoreProvider.jsx";
import { Toaster } from "@/components/ui/toaster.jsx";

createRoot(document.getElementById("root")).render(
  <StoreProvider>
    <QueryProvider>
      <App />
      <Toaster />
    </QueryProvider>
  </StoreProvider>
);
