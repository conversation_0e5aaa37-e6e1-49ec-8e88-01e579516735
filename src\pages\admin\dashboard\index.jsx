import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { AdminDataCard } from "./_components/AdminDataCard";
import { RecentActivity } from "./_components/RecentActivity";
import { QuickActions } from "./_components/QuickActions";
import { StatisticsChart } from "./_components/StatisticsChart";
import {
  Users,
  FileText,
  DollarSign,
  TrendingUp,
  UserCheck,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";

const AdminDashboardPage = () => {
  // Mock data - in a real app, this would come from APIs
  const dashboardData = {
    totalUsers: {
      count: 1247,
      growth: 12.5,
      trend: "Up",
      description: "Active users"
    },
    totalSpecifications: {
      count: 342,
      growth: 8.3,
      trend: "Up",
      description: "This month"
    },
    totalQuotations: {
      count: 189,
      growth: -2.1,
      trend: "Down",
      description: "Pending review"
    },
    revenue: {
      count: "Rs 45,231",
      growth: 15.2,
      trend: "Up",
      description: "Monthly revenue"
    },
    pendingApprovals: {
      count: 23,
      growth: -18.5,
      trend: "Down",
      description: "Awaiting action"
    },
    completedProjects: {
      count: 156,
      growth: 22.1,
      trend: "Up",
      description: "This quarter"
    },
    activeVendors: {
      count: 89,
      growth: 5.7,
      trend: "Up",
      description: "Verified vendors"
    },
    systemAlerts: {
      count: 7,
      growth: null,
      trend: null,
      description: "Require attention"
    }
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="space-y-6">
          {/* Welcome Section */}
          <div>
            <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's what's happening with your platform today.
            </p>
          </div>

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <AdminDataCard
              title="Total Users"
              content={dashboardData.totalUsers.count.toLocaleString()}
              percentage={dashboardData.totalUsers.growth}
              trend={dashboardData.totalUsers.trend}
              description={dashboardData.totalUsers.description}
              icon={Users}
            />
            <AdminDataCard
              title="Total Specifications"
              content={dashboardData.totalSpecifications.count.toLocaleString()}
              percentage={dashboardData.totalSpecifications.growth}
              trend={dashboardData.totalSpecifications.trend}
              description={dashboardData.totalSpecifications.description}
              icon={FileText}
            />
            <AdminDataCard
              title="Total Quotations"
              content={dashboardData.totalQuotations.count.toLocaleString()}
              percentage={dashboardData.totalQuotations.growth}
              trend={dashboardData.totalQuotations.trend}
              description={dashboardData.totalQuotations.description}
              icon={DollarSign}
            />
            <AdminDataCard
              title="Monthly Revenue"
              content={dashboardData.revenue.count}
              percentage={dashboardData.revenue.growth}
              trend={dashboardData.revenue.trend}
              description={dashboardData.revenue.description}
              icon={TrendingUp}
            />
          </div>

          {/* Secondary Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <AdminDataCard
              title="Pending Approvals"
              content={dashboardData.pendingApprovals.count.toLocaleString()}
              percentage={dashboardData.pendingApprovals.growth}
              trend={dashboardData.pendingApprovals.trend}
              description={dashboardData.pendingApprovals.description}
              icon={Clock}
            />
            <AdminDataCard
              title="Completed Projects"
              content={dashboardData.completedProjects.count.toLocaleString()}
              percentage={dashboardData.completedProjects.growth}
              trend={dashboardData.completedProjects.trend}
              description={dashboardData.completedProjects.description}
              icon={CheckCircle}
            />
            <AdminDataCard
              title="Active Vendors"
              content={dashboardData.activeVendors.count.toLocaleString()}
              percentage={dashboardData.activeVendors.growth}
              trend={dashboardData.activeVendors.trend}
              description={dashboardData.activeVendors.description}
              icon={UserCheck}
            />
            <AdminDataCard
              title="System Alerts"
              content={dashboardData.systemAlerts.count.toLocaleString()}
              percentage={dashboardData.systemAlerts.growth}
              trend={dashboardData.systemAlerts.trend}
              description={dashboardData.systemAlerts.description}
              icon={AlertCircle}
            />
          </div>

          {/* Charts and Analytics Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StatisticsChart />
            <QuickActions />
          </div>

          {/* Recent Activity Section */}
          <div className="grid grid-cols-1 gap-6">
            <RecentActivity />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardPage;