export async function apiRequest({
  url,
  method = "GET",
  headers = {},
  body = null,
  params = {},
  options = {},
  isFormData = false,
}) {
  try {
    // Construct query parameters
    const queryString = new URLSearchParams(params).toString();
    const fetchUrl = queryString ? `${url}?${queryString}` : url;

    // Set default headers
    const defaultHeaders = {
      "Content-Type": "application/json",
      Accept: "application/json",
      ...headers,
    };

    // Set headers for form data
    const formDataHeaders = {
      ...headers,
    };

    // Prepare fetch options
    const fetchOptions = {
      method,
      headers: isFormData ? formDataHeaders : defaultHeaders,
      ...options,
    };

    // Handle body for POST, PUT, DELETE requests
    if (body && method !== "GET") {
      fetchOptions.body = isFormData ? body : JSON.stringify(body);
    }

    // Make the API call
    const response = await fetch(fetchUrl, fetchOptions);

    // Check for network-level errors
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({})); // Fallback if response isn't JSON
      throw new Error(errorData.message || `HTTP error: ${response.status}`);
    }

    // Return parsed JSON response
    return await response.json();
  } catch (error) {
    // Log and rethrow the error for further handling
    console.error("API Request Error:", error.message);
    throw error;
  }
}
