"use client";
import { useWindowWidth } from "@wojtekmaj/react-hooks";
import { useEffect, useState } from "react";

export default function useIsTab() {
  const windowWidth = useWindowWidth();
  const [isTab, setIsTab] = useState(false);
  useEffect(() => {
    if ((windowWidth || 0) > 768 && (windowWidth || 0) < 1024) {
      setIsTab(true);
    } else {
      setIsTab(false);
    }
  }, [windowWidth]);

  return { isTab };
}
