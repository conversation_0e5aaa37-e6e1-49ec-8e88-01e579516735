import EditableTable from "@/components/table/EditableTable";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useExcelUpload } from "@/hooks/useExcelUpload";
import React from "react";

const ExcelUploader = () => {
  const { data, error, setData, handleFileUpload } = useExcelUpload();
console.log(data)
  return (
    <div className="col-span-2 flex flex-col gap-2">
      <h2 className="text-xl font-bold">Upload Execl Data or add product details</h2>
      <p className="text-sm max-w-[500px]">
        Please upload an Excel file with the following headers: <strong>Product Name</strong>,{" "}
        <strong>Unit</strong>, <strong>Quantity</strong>, and <strong>Description</strong>. You may also add rows manually.
      </p>
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label htmlFor="picture">Picture</Label>
        <Input id="picture" type="file" accept=".xlsx, .xls, .csv" onChange={handleFileUpload} />
      </div>
      {error && <div style={{ color: "red" }}>{error}</div>}
      <EditableTable data={data} setData={setData} />
    </div>
  );
};

export default ExcelUploader;
