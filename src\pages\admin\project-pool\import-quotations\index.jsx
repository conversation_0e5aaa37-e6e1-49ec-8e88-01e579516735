import { useState } from "react";
import { Upload, FileText, Check } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";

export default function ImportQuotationsPage() {
  // State for file upload
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);
      setUploadedFile(file);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);

        if (progress >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          toast({
            title: "Upload Complete",
            description: `${file.name} has been uploaded successfully.`,
          });
        }
      }, 300);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    if (uploadedFile) {
      toast({
        title: "Processing Started",
        description: "Your file is being processed. You will be notified when complete.",
      });
      
      // Reset form after submission
      setTimeout(() => {
        setUploadedFile(null);
        setUploadProgress(0);
        toast({
          title: "Processing Complete",
          description: "Your quotations have been imported successfully.",
        });
      }, 2000);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Import Quotations</h1>
      
      <div className="max-w-2xl mx-auto">
        <div className="border rounded-md p-8">
          <div className="text-center mb-6">
            <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Import Specifications</h3>
            <p className="text-muted-foreground">
              Upload Excel files with project specifications
            </p>
          </div>

          <div className="max-w-md mx-auto">
            <Label htmlFor="file-upload" className="cursor-pointer">
              <div className="border-2 border-dashed rounded-md p-6 text-center hover:bg-muted/50 transition-colors">
                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                <p>Drag and drop your file here or click to browse</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Supports Excel files (.xlsx, .xls)
                </p>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".xlsx,.xls"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </div>
            </Label>

            {isUploading && (
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
              </div>
            )}

            {uploadedFile && !isUploading && (
              <div className="mt-4 p-4 border rounded-md">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    <div>
                      <p className="font-medium">{uploadedFile.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {(uploadedFile.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                  </div>
                  <Check className="h-5 w-5 text-green-500" />
                </div>
              </div>
            )}

            <div className="mt-6 flex justify-end">
              <Button 
                disabled={!uploadedFile || isUploading}
                onClick={handleSubmit}
              >
                Submit
              </Button>
            </div>
          </div>
        </div>

        {/* Instructions Section */}
        <div className="mt-8 p-6 bg-muted/50 rounded-md">
          <h4 className="font-semibold mb-3">File Format Requirements</h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• File must be in Excel format (.xlsx or .xls)</li>
            <li>• First row should contain column headers</li>
            <li>• Required columns: Specification Name, Amount, Vendor, PO Number</li>
            <li>• Optional columns: Description, Delivery Time, Items</li>
            <li>• Maximum file size: 10MB</li>
            <li>• Maximum 1000 rows per file</li>
          </ul>
        </div>

        {/* Sample Template */}
        <div className="mt-6 p-6 border rounded-md">
          <h4 className="font-semibold mb-3">Download Sample Template</h4>
          <p className="text-sm text-muted-foreground mb-4">
            Use this template to ensure your data is formatted correctly for import.
          </p>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Download Template
          </Button>
        </div>
      </div>
    </div>
  );
}
