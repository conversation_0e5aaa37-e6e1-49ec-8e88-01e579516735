import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { products } from '@/mock-data/spec-mock-data'



export function ProductStep({ currentSpec, onProductChange, isEnabled }) {

  const subCategories = products[currentSpec.category]

  if (!subCategories || subCategories.length < 1) return
  return (
    <Card>
      <CardHeader>
        <CardTitle>Select Subcategory</CardTitle>
        <CardDescription>Choose a product or service for your specification</CardDescription>
      </CardHeader>
      <CardContent>
        <Select
          value={currentSpec.product}
          onValueChange={onProductChange}
          disabled={!isEnabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a subcategory" />
          </SelectTrigger>
          <SelectContent>
            {currentSpec.category && subCategories.map((product) => (
              <SelectItem key={product} value={product}>
                {product}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  )
}

