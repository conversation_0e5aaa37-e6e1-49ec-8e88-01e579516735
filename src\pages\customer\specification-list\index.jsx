'use client'

import { useState, useEffect } from 'react'
import { SpecificationList } from '../create-specification/_components/SpecificationList'
import { useSpecification } from '@/context/SpecificationContext';


export default function Specifications() {

  const {
    specifications,
    handleRemoveSpecification,
  } = useSpecification();

  console.log(specifications)
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-4">All Specifications</h1>
      {
        specifications.length > 0 && (
          <SpecificationList
            specifications={specifications}
            onRemoveSpecification={handleRemoveSpecification}
          />
        )
      }

    </div>
  )
}

