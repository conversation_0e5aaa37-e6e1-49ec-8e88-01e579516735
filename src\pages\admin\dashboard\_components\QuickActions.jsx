import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  UserPlus,
  FileText,
  Bell,
  Settings,
  Users,
  Calendar,
  Download,
  BarChart3
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";

export function QuickActions() {
  const navigate = useNavigate();
  const { adminId } = useParams();

  const quickActions = [
    {
      id: 1,
      title: "Add New User",
      description: "Create a new user account",
      icon: UserPlus,
      action: `user-management`,
      color: "bg-blue-500 hover:bg-blue-600",
    },
    {
      id: 2,
      title: "Create Notice",
      description: "Post a new announcement",
      icon: Bell,
      action: `notice`,
      color: "bg-green-500 hover:bg-green-600",
    },
    {
      id: 3,
      title: "Schedule Training",
      description: "Set up a training session",
      icon: Calendar,
      action: `support-and-training/training`,
      color: "bg-purple-500 hover:bg-purple-600",
    },
    {
      id: 4,
      title: "View Reports",
      description: "Access system reports",
      icon: BarChart3,
      action: `project-pool/all-transactions`,
      color: "bg-orange-500 hover:bg-orange-600",
    },
    {
      id: 5,
      title: "Manage Categories",
      description: "Update business categories",
      icon: Settings,
      action: `category`,
      color: "bg-indigo-500 hover:bg-indigo-600",
    },
    {
      id: 6,
      title: "Export Data",
      description: "Download system data",
      icon: Download,
      action: `project-pool/all-transactions`,
      color: "bg-teal-500 hover:bg-teal-600",
    },
  ];

  const handleActionClick = (actionPath) => {
    navigate(`/admin/${adminId}/${actionPath}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>Frequently used admin functions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Button
                key={action.id}
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all"
                onClick={() => handleActionClick(action.action)}
              >
                <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center`}>
                  <Icon className="w-5 h-5 text-white" />
                </div>
                <div className="text-center">
                  <p className="font-medium text-sm">{action.title}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
