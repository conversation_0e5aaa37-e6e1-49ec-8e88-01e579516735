import { useState } from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Search, FileText, CheckCircle, XCircle, Clock } from "lucide-react";

const DocumentVerificationPage = () => {
  const [documents, setDocuments] = useState([
    {
      id: 1,
      userId: "U12345",
      userName: "John Doe",
      documentType: "ID Card",
      submittedDate: "2023-04-10",
      status: "Pending",
      documentUrl: "/placeholder.svg?height=600&width=800",
      notes: "",
    },
    {
      id: 2,
      userId: "U67890",
      userName: "Jane Smith",
      documentType: "Passport",
      submittedDate: "2023-04-09",
      status: "Approved",
      documentUrl: "/placeholder.svg?height=600&width=800",
      notes: "All information verified successfully.",
    },
    {
      id: 3,
      userId: "U24680",
      userName: "Bob Johnson",
      documentType: "Driver's License",
      submittedDate: "2023-04-08",
      status: "Rejected",
      documentUrl: "/placeholder.svg?height=600&width=800",
      notes: "Document expired. Please submit a valid document.",
    },
    {
      id: 4,
      userId: "U13579",
      userName: "Alice Brown",
      documentType: "Utility Bill",
      submittedDate: "2023-04-07",
      status: "Pending",
      documentUrl: "/placeholder.svg?height=600&width=800",
      notes: "",
    },
    {
      id: 5,
      userId: "U97531",
      userName: "Charlie Wilson",
      documentType: "Bank Statement",
      submittedDate: "2023-04-06",
      status: "Approved",
      documentUrl: "/placeholder.svg?height=600&width=800",
      notes: "Address confirmed.",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);
  const [currentDocument, setCurrentDocument] = useState({
    userName: "",
    submittedDate: "",
    userId: "",
    documentUrl: "",
    status: "",
    notes: "",
  });
  const [verificationNotes, setVerificationNotes] = useState("");

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.documentType.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      doc.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  const openVerifyDialog = (document) => {
    setCurrentDocument(document);
    setVerificationNotes(document.notes);
    setIsVerifyDialogOpen(true);
  };

  const handleVerification = (status) => {
    setDocuments(
      documents.map((doc) =>
        doc.id === currentDocument.id
          ? { ...doc, status, notes: verificationNotes }
          : doc
      )
    );
    setIsVerifyDialogOpen(false);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "Pending":
        return (
          <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white">
            <Clock className="mr-1 h-3 w-3" /> Pending
          </Badge>
        );
      case "Approved":
        return (
          <Badge className="bg-green-500 hover:bg-green-600 text-white">
            <CheckCircle className="mr-1 h-3 w-3" /> Approved
          </Badge>
        );
      case "Rejected":
        return (
          <Badge className="bg-red-500 hover:bg-red-600 text-white">
            <XCircle className="mr-1 h-3 w-3" /> Rejected
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Document Verification</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="flex flex-col md:flex-row gap-4 items-center mb-6">
          <div className="relative flex-1 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="w-full md:w-auto">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Submitted Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDocuments.length > 0 ? (
                filteredDocuments.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{doc.userName}</div>
                        <div className="text-sm text-muted-foreground">
                          {doc.userId}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{doc.submittedDate}</TableCell>
                    <TableCell>{getStatusBadge(doc.status)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openVerifyDialog(doc)}
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        {doc.status === "Pending" ? "Verify" : "View"}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No documents found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <Dialog open={isVerifyDialogOpen} onOpenChange={setIsVerifyDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{currentDocument.userName}</DialogTitle>
              <DialogDescription>
                Submitted on {currentDocument.submittedDate} • User ID:{" "}
                {currentDocument.userId}
              </DialogDescription>
            </DialogHeader>

            <div className="flex justify-center border rounded-md p-2 bg-muted/20 my-4">
              <img
                src={currentDocument.documentUrl || "/placeholder.svg"}
                alt={`Document for ${currentDocument.userName}`}
                className="max-h-[400px] object-contain"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Verification Notes</Label>
              <Input
                id="notes"
                value={verificationNotes}
                onChange={(e) => setVerificationNotes(e.target.value)}
                placeholder="Add notes about this document..."
              />
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-2">
              {currentDocument.status === "Pending" ? (
                <>
                  <Button
                    variant="destructive"
                    onClick={() => handleVerification("Rejected")}
                    className="w-full sm:w-auto"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button
                    variant="default"
                    onClick={() => handleVerification("Approved")}
                    className="w-full sm:w-auto"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => handleVerification(currentDocument.status)}
                  className="w-full sm:w-auto"
                >
                  Update Notes
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default DocumentVerificationPage;
