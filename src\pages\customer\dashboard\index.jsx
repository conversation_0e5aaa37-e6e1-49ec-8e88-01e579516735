import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { DataCard } from "./_components/DataCard";
import cardData from "@/assets/png/cardData.png";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { CustomerTable } from "./_components/CustomerTable";
import { useNavigate } from "react-router-dom";
import { useGetCustomerProfileQuery } from "@/services/auth/query";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

const CustomerDashboardPage = () => {
  const navigate = useNavigate();
  const [showKycDialog, setShowKycDialog] = useState(false);

  const customerData = useGetCustomerProfileQuery();
  console.log(customerData.data.data.type);
  const customerType = customerData?.data?.data?.type;

  useEffect(() => {
    if (customerData?.data?.data?.kyc_status === "pending") {
      setShowKycDialog(true);
    }
  }, [customerData?.data?.data?.kyc_status]);

  const navigateToKYCUpdate = () => {
    if (customerType === "individual") {
      navigate("/verify");
    } else if (customerType === "organization") {
      navigate("/kyc-verify");
    }
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <h1 className="font-nunito text-3xl font-bold">Dashboard</h1>
        <div className="grid sm:grid-cols-2 xl:grid-cols-4 mt-4 gap-4">
          <DataCard
            title="Create project"
            content="100"
            percentage="2.5"
            trend="Down"
            image={cardData}
          />
          <DataCard
            title="Accepted Specifications"
            content="100"
            percentage="2.5"
            trend="Up"
            image={cardData}
          />
          <DataCard
            title="Engaged Vendors"
            content="100"
            percentage="2.5"
            trend="Down"
            image={cardData}
          />
          <DataCard
            title="Total Pending"
            content="100"
            percentage="2.5"
            trend="Up"
            image={cardData}
          />
        </div>

        <div className="mt-5">
          <CustomerTable />
        </div>
      </div>
      <Dialog open={showKycDialog} onOpenChange={setShowKycDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>KYC Verification Required</DialogTitle>
            <DialogDescription>
              Your KYC verification is currently pending. Please complete your
              KYC profile to access all features.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowKycDialog(false)}>
              Skip for Now
            </Button>
            <Button onClick={navigateToKYCUpdate}>Update KYC</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CustomerDashboardPage;
