// services/auth/authApi.js

const API_URL = import.meta.env.VITE_API_BASE_URL;

// Login user
export const loginUser = async (data) => {
  const response = await fetch(`${API_URL}/customer/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    return errorData;
  }

  return response.json(); // This should return token and user data
};

// Register user
export const registerCustomer = async (data) => {
  const response = await fetch(`${API_URL}/customer/register`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    return errorData;
  }

  return response.json(); // Return registered user data, should include token and user info
};

export const loginVendor = async (data) => {
  const response = await fetch(`${API_URL}/vendor/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    return errorData;
  }
};

export const registerVendor = async (data) => {
  console.log(data);
  const response = await fetch(`${API_URL}/vendor/register`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const errorData = await response.json();
    return errorData;
  }
};
