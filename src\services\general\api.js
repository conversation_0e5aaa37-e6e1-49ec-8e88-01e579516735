import { apiRequest } from "@/utils/apiRequest";
import { getAccessTokenByUserType } from "@/utils/setCookies";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Helper function to get access token based on current route or fallback
const getAccessToken = () => {
  const currentPath = window.location.pathname;
  let userType = null;

  if (currentPath.startsWith("/customer/")) {
    userType = "user";
  } else if (currentPath.startsWith("/vendor/")) {
    userType = "vendor";
  } else if (currentPath.startsWith("/admin/")) {
    userType = "admin";
  }

  // If we can determine user type from route, use it
  if (userType) {
    return getAccessTokenByUserType(userType);
  }

  // Fallback: try to find any authenticated user type
  const userTypes = ["user", "vendor", "admin"];
  for (const type of userTypes) {
    const token = getAccessTokenByUserType(type);
    if (token) {
      return token;
    }
  }

  return null;
};

export const getProvince = async () => {
  const accessToken = getAccessToken();
  const response = await apiRequest({
    url: `${BASE_URL}/provinces`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getDistrictByProvince = async (provinceId) => {
  const accessToken = getAccessToken();
  const response = await apiRequest({
    url: `${BASE_URL}/districts-by-province/${provinceId}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getMunicipalityByDistrict = async (districtId) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/municipalities-by-district/${districtId}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getFaqs = async () => {
  const response = await apiRequest({
    url: `${BASE_URL}/faq`,
    method: "GET",
  });
  console.log(response.data);
  return response.data;
};
