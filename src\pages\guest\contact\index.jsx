import { Mail, MapPin, Phone } from "lucide-react";
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";

const ContactPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = (data) => {
    console.log(data);
    // Handle form submission here
  };
  return (
    <div className="px-4 sm:px-8 xl:px-32 mt-16">
      <div className="flex flex-col justify-center items-center">
        <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy text-center leading-[2.875rem]">
          Contact Us
        </h1>
        <p className="font-nunito text-bodyLarge mt-4 leading-[1.625rem] text-center text-darkTeal max-w-[35.375rem]">
          Lorem ipsum, dolor sit amet consectetur adipisicing elit. Adipisci
          accusantium deserunt perspiciatis sequi, nostrum quaerat esse
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-10 lg:justify-between w-full mt-16">
        <div className="w-full md:w-1/2">
          <h1 className="font-nunito font-bold text-[2rem] lg:text-heading3 text-navy lg:w-[30.375rem] leading-[2.875rem]">{`Get In Touch`}</h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-darkTeal max-w-[50.375rem]">{`"Empower Your Business with Streamlined Quotation Management. Say Hello to Efficiency, Accuracy, and Growth with Our Cutting-Edge Software Solution."`}</p>
          <div className="mt-8 font-nunito">
            <div className="flex items-center gap-4 mb-6">
              <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center">
                <MapPin />
              </div>
              <div className="">
                <h1 className="font-bold text-bodyLarge text-navy">Address</h1>
                <p className="font-semibold text-darkTeal">Kathmandu, Nepal</p>
              </div>
            </div>
            <div className="flex items-center gap-4 mb-6">
              <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center">
                <Phone />
              </div>
              <div className="">
                <h1 className="font-bold text-bodyLarge text-navy">
                  Phone Number
                </h1>
                <p className="font-semibold text-darkTeal">Kathmandu, Nepal</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center">
                <Mail />
              </div>
              <div className="">
                <h1 className="font-bold text-bodyLarge text-navy">Email</h1>
                <p className="font-semibold text-darkTeal">Kathmandu, Nepal</p>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full lg:w-1/2">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <Input
                placeholder="Your Name"
                {...register("name", { required: "Name is required" })}
                className="font-nunito"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>
            <div>
              <Input
                type="email"
                placeholder="Your Email"
                {...register("email", {
                  required: "Email is required",
                  pattern: {
                    value: /\S+@\S+\.\S+/,
                    message: "Invalid email address",
                  },
                })}
                className="font-nunito"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div>
              <Input
                type="tel"
                placeholder="Your Phone"
                {...register("phone", { required: "Phone number is required" })}
                className="font-nunito"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.phone.message}
                </p>
              )}
            </div>
            <div>
              <Textarea
                placeholder="Your Message"
                {...register("message", { required: "Message is required" })}
                className="font-nunito min-h-[150px]"
              />
              {errors.message && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.message.message}
                </p>
              )}
            </div>
            <Button
              type="submit"
              className="w-full bg-primary text-white hover:bg-primary/90"
            >
              Send Message
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
