import { Separator } from "@/components/ui/separator";
import aboutIcon1 from "@/assets/png/about-icon1.png";
import aboutIcon2 from "@/assets/png/about-icon2.png";
import aboutIcon3 from "@/assets/png/about-icon3.png";

const AboutSection = () => {
  return (
    <div className="mt-24 px-4 sm:px-8 xl:px-32">
      <span className="font-nunito font-semibold text-heading6 text-green1">{`About Zettabid`}</span>
      <div className="flex flex-col lg:flex-row gap-10 lg:justify-between w-full">
        <div className="w-full mt-12">
          <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy lg:w-[30.375rem] leading-[2.875rem]">{`Quotation Management System`}</h1>
          <p className="font-nunito text-bodyLarge mt-6 leading-[1.625rem] text-darkTeal max-w-[50.375rem]">{`"Empower Your Business with Streamlined Quotation Management. Say Hello to Efficiency, Accuracy, and Growth with Our Cutting-Edge Software Solution."`}</p>
        </div>
        <div className="flex gap-3 flex-col xs:flex-row">
          <div className="border-2 border-green3 w-[12.408rem] h-[9.75rem] xl:w-[15.408rem] xl:h-[11.75rem] flex justify-center items-center flex-col rounded-xl">
            <div>
              <h2 className="font-poppins font-semibold text-heading2 text-navy leading-[2.875rem]">{`24+`}</h2>
              <span className="font-nunito font-bodyLarge text-navy leading-6">{`Vendors`}</span>
            </div>
          </div>
          <div className="border-2 border-green3 w-[12.408rem] h-[9.75rem] xl:w-[15.408rem] xl:h-[11.75rem] flex justify-center items-center flex-col rounded-xl">
            <div>
              <h2 className="font-poppins font-semibold text-heading2 text-navy leading-[2.875rem]">{`10+`}</h2>
              <span className="font-nunito font-bodyLarge text-navy leading-6">{`Templates`}</span>
            </div>
          </div>
        </div>
      </div>
      <Separator className="my-12 " />
      <div className="flex flex-col md:flex-row justify-between items-start gap-10">
        <div className="flex gap-4 w-full">
          <img
            loading="lazy"
            src={aboutIcon1}
            alt="company 1"
            className="w-auto h-5 lg:h-8"
          />
          <div className="flex flex-col gap-2">
            <h2 className="font-poppins text-bodyLarge leading-[1.625rem] font-semibold">{`Automate Quotation in a Click`}</h2>
            <span className="font-nunito text-bodyLarge leading-[1.625rem]">{`Highlight features such as automated quote generation, customizable templates to increase your efficiency and productivity.`}</span>
          </div>
        </div>
        <div className="flex gap-4 w-full">
          <img
            loading="lazy"
            src={aboutIcon2}
            alt="company 1"
            className="w-auto h-5 lg:h-8"
          />
          <div className="flex flex-col gap-2">
            <h2 className="font-poppins text-bodyLarge leading-[1.625rem] font-semibold">{`Customization and Flexibility`}</h2>
            <span className="font-nunito text-bodyLarge leading-[1.625rem]">{`Your Business can tailor the software to their specific products, including customizable templates, Clients to Quote in a regular basis.`}</span>
          </div>
        </div>
        <div className="flex gap-4 w-full">
          <img
            loading="lazy"
            src={aboutIcon3}
            alt="company 1"
            className="w-auto h-5 lg:h-8"
          />
          <div className="flex flex-col gap-2">
            <h2 className="font-poppins text-bodyLarge leading-[1.625rem] font-semibold">{`Reach your Client `}</h2>
            <span className="font-nunito text-bodyLarge leading-[1.625rem]">{`Business can add their client to the account and keep in touch in a regular basis.`}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutSection;
