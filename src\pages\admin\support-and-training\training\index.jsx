import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Video, Edit, Trash2, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import {
  useGetTrainingSessions,
  useCreateTrainingSession,
  useUpdateTrainingSession,
  useDeleteTrainingSession,
} from "@/services/admin/query";

const SupportTrainingTrainPage = () => {
  // API hooks
  const { data: trainingsData, isLoading, isError, refetch } = useGetTrainingSessions();
  const createTrainingMutation = useCreateTrainingSession();
  const updateTrainingMutation = useUpdateTrainingSession();
  const deleteTrainingMutation = useDeleteTrainingSession();

  // Get training sessions from API response
  const trainingSessions = Array.isArray(trainingsData?.trainingSessions)
    ? trainingsData.trainingSessions
    : [];

  // Local state
  const [newTraining, setNewTraining] = useState({
    title: "",
    datetime: "",
    link: "",
  });
  const [editingTraining, setEditingTraining] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [trainingToDelete, setTrainingToDelete] = useState(null);

  // Handle scheduling/updating training
  const handleScheduleTraining = (e) => {
    e.preventDefault();

    if (!newTraining.title || !newTraining.datetime || !newTraining.link) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    const trainingData = {
      title: newTraining.title,
      datetime: newTraining.datetime,
      link: newTraining.link,
      status: "Upcoming",
    };

    if (isEditMode && editingTraining) {
      // Update existing training
      updateTrainingMutation.mutate(
        { id: editingTraining.id, data: trainingData },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: "Training session updated successfully.",
            });
            resetForm();
            refetch();
          },
          onError: (error) => {
            toast({
              title: "Error",
              description: error.response?.data?.message || "Failed to update training session.",
              variant: "destructive",
            });
          },
        }
      );
    } else {
      // Create new training
      createTrainingMutation.mutate(trainingData, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Training session scheduled successfully.",
          });
          resetForm();
          refetch();
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: error.response?.data?.message || "Failed to schedule training session.",
            variant: "destructive",
          });
        },
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setNewTraining({ title: "", datetime: "", link: "" });
    setEditingTraining(null);
    setIsEditMode(false);
  };

  // Handle edit training
  const handleEditTraining = (training) => {
    setNewTraining({
      title: training.title,
      datetime: training.datetime,
      link: training.link,
    });
    setEditingTraining(training);
    setIsEditMode(true);
  };

  // Handle delete training
  const handleDeleteTraining = (training) => {
    setTrainingToDelete(training);
    setDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = () => {
    if (trainingToDelete) {
      deleteTrainingMutation.mutate(trainingToDelete.id, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Training session deleted successfully.",
          });
          setDeleteDialogOpen(false);
          setTrainingToDelete(null);
          refetch();
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: error.response?.data?.message || "Failed to delete training session.",
            variant: "destructive",
          });
        },
      });
    }
  };

  // Get status based on datetime
  const getTrainingStatus = (datetime) => {
    const now = new Date();
    const trainingDate = new Date(datetime);
    return trainingDate > now ? "Upcoming" : "Completed";
  };
  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Training</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {isEditMode ? "Edit Training Session" : "Schedule Training Session"}
              </CardTitle>
              <CardDescription>
                {isEditMode
                  ? "Update the training session details"
                  : "Create a new training session for users or vendors"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleScheduleTraining} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    placeholder="e.g., New Vendor Orientation"
                    value={newTraining.title}
                    onChange={(e) =>
                      setNewTraining({ ...newTraining, title: e.target.value })
                    }
                    disabled={createTrainingMutation.isPending || updateTrainingMutation.isPending}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="datetime">Date & Time</Label>
                  <Input
                    id="datetime"
                    type="datetime-local"
                    value={newTraining.datetime}
                    onChange={(e) =>
                      setNewTraining({
                        ...newTraining,
                        datetime: e.target.value,
                      })
                    }
                    disabled={createTrainingMutation.isPending || updateTrainingMutation.isPending}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="link">Meeting Link</Label>
                  <Input
                    id="link"
                    placeholder="e.g., https://zoom.us/j/123456789"
                    value={newTraining.link}
                    onChange={(e) =>
                      setNewTraining({ ...newTraining, link: e.target.value })
                    }
                    disabled={createTrainingMutation.isPending || updateTrainingMutation.isPending}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={createTrainingMutation.isPending || updateTrainingMutation.isPending}
                  >
                    {(createTrainingMutation.isPending || updateTrainingMutation.isPending) && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    <Calendar className="h-4 w-4 mr-2" />
                    {isEditMode ? "Update Training" : "Schedule Training"}
                  </Button>
                  {isEditMode && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={resetForm}
                      disabled={createTrainingMutation.isPending || updateTrainingMutation.isPending}
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming & Past Sessions</CardTitle>
              <CardDescription>
                View all scheduled training sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading training sessions...</span>
                </div>
              ) : isError ? (
                <div className="text-center py-8">
                  <p className="text-destructive">Failed to load training sessions</p>
                  <Button variant="outline" onClick={() => refetch()} className="mt-2">
                    Try Again
                  </Button>
                </div>
              ) : trainingSessions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No training sessions scheduled yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {trainingSessions.map((session) => (
                    <div key={session.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold">{session.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {new Date(session.datetime).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              getTrainingStatus(session.datetime) === "Upcoming"
                                ? "outline"
                                : "secondary"
                            }
                          >
                            {getTrainingStatus(session.datetime)}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditTraining(session)}
                            disabled={deleteTrainingMutation.isPending}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteTraining(session)}
                            disabled={deleteTrainingMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="mt-2">
                        <a
                          href={session.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline flex items-center"
                        >
                          <Video className="h-4 w-4 mr-1" />
                          Join Meeting
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Training Session</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{trainingToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteTrainingMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteTrainingMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteTrainingMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SupportTrainingTrainPage;
