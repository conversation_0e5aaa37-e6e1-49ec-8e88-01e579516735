export default function ProgressIndicator({ steps, currentStep }) {
  return (
    <div className="flex items-center justify-between mb-8 relative font-bold font-nunito">
      {steps.map((step, index) => (
        <div key={step} className="flex flex-col items-center relative w-full">
          {/* Line before each step except the first one */}
          {index > 0 && (
            <div
              className={`absolute top-[35%] left-0 w-1/2 h-1 transform -translate-y-1/2 ${
                index <= currentStep ? "bg-primary" : "bg-gray-200"
              }`}
            ></div>
          )}

          {/* Step Circle */}
          <div
            className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all z-10 ${
              index <= currentStep
                ? "bg-primary text-white"
                : "bg-gray-300 text-gray-600"
            }`}
          >
            {index + 1}
          </div>

          {/* Step Label */}
          <span
            className={`text-sm mt-2 ${
              index <= currentStep
                ? "text-primary font-medium"
                : "text-gray-500"
            }`}
          >
            {step}
          </span>

          {/* Line after each step except the last one */}
          {index < steps.length - 1 && (
            <div
              className={`absolute top-[35%] right-0 w-1/2 h-1 transform -translate-y-1/2 ${
                index < currentStep ? "bg-primary" : "bg-gray-200"
              }`}
            ></div>
          )}
        </div>
      ))}
    </div>
  );
}
