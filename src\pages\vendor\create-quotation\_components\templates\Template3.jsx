import { fixedFields } from '@/utils/specProductFields';
import React from 'react'

const Template3 = ({ data }) => {

    const { specificationProductData } = data;

    return (
        <div className="bg-gradient-to-r from-purple-400 to-pink-500 p-6 rounded-lg text-white">
            {
                specificationProductData?.length > 0 && (
                    <table className='w-full mt-4 border-collapse'>
                        <thead>
                            <tr className='border-t border-b'>
                                <th className="text-left p-2 border border-gray-300">S.N.</th>
                                {fixedFields.map(({ key, label }) => (
                                    <th key={key} className="text-left p-2 border border-gray-300">
                                        {label}
                                    </th>

                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {
                                specificationProductData.map((item, index) => (
                                    <tr key={index}>
                                        <td className="p-2 border border-gray-300">
                                            {index + 1}
                                        </td>
                                        <td className="p-2 border border-gray-300">
                                            <h2 className="text-sm font-bold">{item.itemName}</h2>
                                        </td>
                                        <td className="p-2 border border-gray-300">
                                            <p className="text-gray-600">{item.quantity}</p>
                                        </td>
                                        <td className="p-2 border border-gray-300">
                                            <p>{item.unit}</p>
                                        </td>
                                        <td className="p-2 border border-gray-300">
                                            <p className="text-gray-800">{item.specification}</p>
                                        </td>
                                        <td className="p-2 border border-gray-300">
                                            <div className="flex flex-wrap gap-2">
                                                {item.other}
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            }

                        </tbody>
                    </table>
                )
            }
        </div>
    )
}

export default Template3