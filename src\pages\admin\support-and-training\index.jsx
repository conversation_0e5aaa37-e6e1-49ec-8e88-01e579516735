import { SelectItem } from "@/components/ui/select";

import { SelectContent } from "@/components/ui/select";

import { SelectValue } from "@/components/ui/select";

import { SelectTrigger } from "@/components/ui/select";

import { Select } from "@/components/ui/select";

import { useState } from "react";
import {
  LifeBuoy,
  BookOpen,
  FileQuestion,
  MessageSquare,
  Upload,
  FileText,
  Video,
  Download,
  Eye,
  Star,
  Calendar,
  Search,
  AlertCircle,
  Edit,
  Trash2,
  Plus,
  Send,
  Reply,
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function SupportTrainingPage() {
  // State for active tab
  const [activeTab, setActiveTab] = useState("resources");

  // Resources state
  const [resources, setResources] = useState([
    {
      id: 1,
      name: "How to Submit a Quote",
      type: "PDF",
      date: "2023-11-15",
      url: "/documents/how-to-submit-quote.pdf",
      tags: ["quotes", "vendors"],
    },
    {
      id: 2,
      name: "Project Pool Overview",
      type: "Video",
      date: "2023-11-10",
      url: "/videos/project-pool-overview.mp4",
      tags: ["overview", "getting-started"],
    },
    {
      id: 3,
      name: "Vendor Onboarding Guide",
      type: "PDF",
      date: "2023-11-05",
      url: "/documents/vendor-onboarding.pdf",
      tags: ["vendors", "onboarding"],
    },
    {
      id: 4,
      name: "Customer Dashboard Tutorial",
      type: "Video",
      date: "2023-10-28",
      url: "/videos/customer-dashboard.mp4",
      tags: ["customers", "dashboard"],
    },
  ]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  // Helpdesk state
  const [tickets, setTickets] = useState([
    {
      id: "TKT-001",
      user: "John Smith",
      summary: "Cannot access vendor dashboard",
      status: "Open",
      date: "2023-11-18",
      isOld: true,
    },
    {
      id: "TKT-002",
      user: "Sarah Johnson",
      summary: "Error when uploading specifications",
      status: "Open",
      date: "2023-11-20",
      isOld: false,
    },
    {
      id: "TKT-003",
      user: "Michael Brown",
      summary: "Need help with quotation comparison",
      status: "Resolved",
      date: "2023-11-15",
      isOld: false,
    },
    {
      id: "TKT-004",
      user: "Emily Davis",
      summary: "Login issues after password reset",
      status: "Open",
      date: "2023-11-19",
      isOld: false,
    },
  ]);
  const [replyTicket, setReplyTicket] = useState(null);
  const [replyText, setReplyText] = useState("");
  const [showReplyDialog, setShowReplyDialog] = useState(false);

  // Training state
  const [trainingSessions, setTrainingSessions] = useState([
    {
      id: 1,
      title: "New Vendor Orientation",
      datetime: "2023-12-05T14:00",
      link: "https://zoom.us/j/123456789",
      status: "Upcoming",
    },
    {
      id: 2,
      title: "Advanced Quotation Techniques",
      datetime: "2023-12-10T10:00",
      link: "https://meet.google.com/abc-defg-hij",
      status: "Upcoming",
    },
    {
      id: 3,
      title: "Project Pool for Customers",
      datetime: "2023-11-15T15:30",
      link: "https://zoom.us/j/*********",
      status: "Completed",
    },
  ]);
  const [newTraining, setNewTraining] = useState({
    title: "",
    datetime: "",
    link: "",
  });

  // Knowledge Base state
  const [faqs, setFaqs] = useState([
    {
      id: 1,
      question: "How do I reset my password?",
      answer:
        "Click on the 'Forgot Password' link on the login page and follow the instructions sent to your email.",
      category: "Account",
    },
    {
      id: 2,
      question: "What is a PO Number?",
      answer:
        "A Purchase Order (PO) Number is a unique identifier assigned to a purchase order. It helps track the order throughout the procurement process.",
      category: "Orders",
    },
    {
      id: 3,
      question: "How do I submit a quotation?",
      answer:
        "Navigate to the 'Quotations' section, click on 'New Quotation', fill in the required details, and click 'Submit'.",
      category: "Quotations",
    },
    {
      id: 4,
      question: "What is KYC verification?",
      answer:
        "Know Your Customer (KYC) verification is a process where we verify the identity of our clients. This helps prevent fraud and ensures compliance with regulations.",
      category: "Account",
    },
  ]);
  const [searchQuery, setSearchQuery] = useState("");
  const [newFaq, setNewFaq] = useState({
    question: "",
    answer: "",
    category: "General",
  });
  const [editingFaq, setEditingFaq] = useState(null);

  // Feedback state
  const [feedback, setFeedback] = useState([
    {
      id: 1,
      user: "Robert Wilson",
      comment:
        "The new dashboard is much more intuitive and easier to navigate.",
      rating: 5,
      date: "2023-11-17",
    },
    {
      id: 2,
      user: "Jennifer Lee",
      comment:
        "Had some issues with the quotation comparison tool. It's a bit confusing to use.",
      rating: 3,
      date: "2023-11-16",
    },
    {
      id: 3,
      user: "David Miller",
      comment: "Great support team! They resolved my issue within hours.",
      rating: 5,
      date: "2023-11-15",
    },
    {
      id: 4,
      user: "Lisa Garcia",
      comment:
        "The file upload feature is too slow and sometimes fails without any error message.",
      rating: 2,
      date: "2023-11-14",
    },
  ]);
  const [replyFeedback, setReplyFeedback] = useState(null);
  const [feedbackReplyText, setFeedbackReplyText] = useState("");
  const [showFeedbackReplyDialog, setShowFeedbackReplyDialog] = useState(false);

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);
      setUploadedFile(file);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);

        if (progress >= 100) {
          clearInterval(interval);
          setIsUploading(false);

          // Add the new resource
          const fileType = file.name.endsWith(".pdf")
            ? "PDF"
            : file.name.endsWith(".mp4") || file.name.endsWith(".mov")
            ? "Video"
            : "Document";

          const newResource = {
            id: resources.length + 1,
            name: file.name.split(".")[0],
            type: fileType,
            date: format(new Date(), "yyyy-MM-dd"),
            url: URL.createObjectURL(file),
            tags: [],
          };

          setResources([...resources, newResource]);

          toast({
            title: "Upload Complete",
            description: `${file.name} has been uploaded successfully.`,
          });
        }
      }, 300);
    }
  };

  // Handle ticket reply
  const handleTicketReply = () => {
    if (!replyText.trim()) {
      toast({
        title: "Error",
        description: "Reply cannot be empty",
        variant: "destructive",
      });
      return;
    }

    // Update ticket status
    const updatedTickets = tickets.map((ticket) =>
      ticket.id === replyTicket.id ? { ...ticket, status: "Resolved" } : ticket
    );

    setTickets(updatedTickets);
    setReplyText("");
    setShowReplyDialog(false);

    toast({
      title: "Reply Sent",
      description: `Your reply to ticket ${replyTicket.id} has been sent.`,
    });
  };

  // Handle scheduling training
  const handleScheduleTraining = (e) => {
    e.preventDefault();

    if (!newTraining.title || !newTraining.datetime || !newTraining.link) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    const newSession = {
      id: trainingSessions.length + 1,
      ...newTraining,
      status: "Upcoming",
    };

    setTrainingSessions([...trainingSessions, newSession]);
    setNewTraining({ title: "", datetime: "", link: "" });

    toast({
      title: "Training Scheduled",
      description: `${newTraining.title} has been scheduled successfully.`,
    });
  };

  // Handle adding new FAQ
  const handleAddFaq = () => {
    if (!newFaq.question || !newFaq.answer) {
      toast({
        title: "Error",
        description: "Question and answer cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const newFaqItem = {
      id: faqs.length + 1,
      ...newFaq,
    };

    setFaqs([...faqs, newFaqItem]);
    setNewFaq({ question: "", answer: "", category: "General" });

    toast({
      title: "FAQ Added",
      description: "New FAQ has been added successfully.",
    });
  };

  // Handle editing FAQ
  const handleEditFaq = () => {
    if (!editingFaq.question || !editingFaq.answer) {
      toast({
        title: "Error",
        description: "Question and answer cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const updatedFaqs = faqs.map((faq) =>
      faq.id === editingFaq.id ? editingFaq : faq
    );

    setFaqs(updatedFaqs);
    setEditingFaq(null);

    toast({
      title: "FAQ Updated",
      description: "FAQ has been updated successfully.",
    });
  };

  // Handle deleting FAQ
  const handleDeleteFaq = (id) => {
    const updatedFaqs = faqs.filter((faq) => faq.id !== id);
    setFaqs(updatedFaqs);

    toast({
      title: "FAQ Deleted",
      description: "FAQ has been deleted successfully.",
    });
  };

  // Handle feedback reply
  const handleFeedbackReply = () => {
    if (!feedbackReplyText.trim()) {
      toast({
        title: "Error",
        description: "Reply cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setFeedbackReplyText("");
    setShowFeedbackReplyDialog(false);

    toast({
      title: "Reply Sent",
      description: `Your reply to ${replyFeedback.user}'s feedback has been sent.`,
    });
  };

  // Filter FAQs based on search query
  const filteredFaqs = faqs.filter(
    (faq) =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate average rating
  const averageRating =
    feedback.length > 0
      ? (
          feedback.reduce((sum, item) => sum + item.rating, 0) / feedback.length
        ).toFixed(1)
      : 0;

  // Render Resources Tab
  const renderResourcesTab = () => (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Resources</h2>
        <Label htmlFor="file-upload" className="cursor-pointer">
          <div className="flex items-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 rounded-md">
            <Upload className="h-4 w-4" />
            <span>Upload Resource</span>
            <Input
              id="file-upload"
              type="file"
              accept=".pdf,.mp4,.mov"
              className="hidden"
              onChange={handleFileUpload}
            />
          </div>
        </Label>
      </div>

      {isUploading && (
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-1">
            <span>Uploading {uploadedFile?.name}...</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-2" />
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Date Uploaded</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {resources.map((resource) => (
              <TableRow key={resource.id}>
                <TableCell className="font-medium">{resource.name}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {resource.type === "PDF" ? (
                      <FileText className="h-4 w-4 mr-2 text-red-500" />
                    ) : (
                      <Video className="h-4 w-4 mr-2 text-blue-500" />
                    )}
                    {resource.type}
                  </div>
                </TableCell>
                <TableCell>{resource.date}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="icon" asChild>
                      <a
                        href={resource.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View {resource.name}</span>
                      </a>
                    </Button>
                    <Button variant="ghost" size="icon" asChild>
                      <a href={resource.url} download={resource.name}>
                        <Download className="h-4 w-4" />
                        <span className="sr-only">
                          Download {resource.name}
                        </span>
                      </a>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );

  // Render Helpdesk Tab
  const renderHelpdeskTab = () => (
    <div>
      <h2 className="text-2xl font-bold mb-6">Helpdesk</h2>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Ticket ID</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Issue Summary</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tickets.map((ticket) => (
              <TableRow
                key={ticket.id}
                className={
                  ticket.isOld && ticket.status === "Open"
                    ? "border-l-4 border-l-red-500"
                    : ""
                }
              >
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {ticket.isOld && ticket.status === "Open" && (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    {ticket.id}
                  </div>
                </TableCell>
                <TableCell>{ticket.user}</TableCell>
                <TableCell>{ticket.summary}</TableCell>
                <TableCell>
                  <Badge
                    variant={ticket.status === "Open" ? "outline" : "success"}
                  >
                    {ticket.status}
                  </Badge>
                </TableCell>
                <TableCell>{ticket.date}</TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setReplyTicket(ticket);
                      setShowReplyDialog(true);
                    }}
                    disabled={ticket.status === "Resolved"}
                  >
                    <Reply className="h-4 w-4 mr-2" />
                    Reply
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reply to Ticket {replyTicket?.id}</DialogTitle>
            <DialogDescription>
              Responding to: {replyTicket?.summary}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Label htmlFor="reply">Your Response</Label>
            <Textarea
              id="reply"
              placeholder="Type your reply here..."
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              rows={5}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReplyDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleTicketReply}>
              <Send className="h-4 w-4 mr-2" />
              Send Reply
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );

  // Render Training Tab
  const renderTrainingTab = () => (
    <div>
      <h2 className="text-2xl font-bold mb-6">Training</h2>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Schedule Training Session</CardTitle>
            <CardDescription>
              Create a new training session for users or vendors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleScheduleTraining} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., New Vendor Orientation"
                  value={newTraining.title}
                  onChange={(e) =>
                    setNewTraining({ ...newTraining, title: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="datetime">Date & Time</Label>
                <Input
                  id="datetime"
                  type="datetime-local"
                  value={newTraining.datetime}
                  onChange={(e) =>
                    setNewTraining({ ...newTraining, datetime: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="link">Meeting Link</Label>
                <Input
                  id="link"
                  placeholder="e.g., https://zoom.us/j/123456789"
                  value={newTraining.link}
                  onChange={(e) =>
                    setNewTraining({ ...newTraining, link: e.target.value })
                  }
                />
              </div>
              <Button type="submit" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Training
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming & Past Sessions</CardTitle>
            <CardDescription>
              View all scheduled training sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trainingSessions.map((session) => (
                <div key={session.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold">{session.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(session.datetime).toLocaleString()}
                      </p>
                    </div>
                    <Badge
                      variant={
                        session.status === "Upcoming" ? "outline" : "secondary"
                      }
                    >
                      {session.status}
                    </Badge>
                  </div>
                  <div className="mt-2">
                    <a
                      href={session.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline flex items-center"
                    >
                      <Video className="h-4 w-4 mr-1" />
                      Join Meeting
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Render Knowledge Base Tab
  const renderKnowledgeBaseTab = () => (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Knowledge Base</h2>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search FAQs..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
            <CardDescription>
              Browse through common questions and answers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[400px] pr-4">
              <Accordion type="single" collapsible className="w-full">
                {filteredFaqs.map((faq) => (
                  <AccordionItem key={faq.id} value={`faq-${faq.id}`}>
                    <AccordionTrigger>
                      <div className="flex items-start text-left">
                        <span className="mr-2">Q:</span>
                        <span>{faq.question}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="flex items-start">
                        <span className="mr-2">A:</span>
                        <span>{faq.answer}</span>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <Badge variant="outline" className="text-xs">
                          {faq.category}
                        </Badge>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setEditingFaq({ ...faq })}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-destructive"
                            onClick={() => handleDeleteFaq(faq.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </ScrollArea>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{editingFaq ? "Edit FAQ" : "Add New FAQ"}</CardTitle>
            <CardDescription>
              {editingFaq
                ? "Update an existing FAQ"
                : "Create a new frequently asked question"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="question">Question</Label>
                <Input
                  id="question"
                  placeholder="e.g., How do I reset my password?"
                  value={editingFaq ? editingFaq.question : newFaq.question}
                  onChange={(e) =>
                    editingFaq
                      ? setEditingFaq({
                          ...editingFaq,
                          question: e.target.value,
                        })
                      : setNewFaq({ ...newFaq, question: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="answer">Answer</Label>
                <Textarea
                  id="answer"
                  placeholder="Provide a clear and concise answer..."
                  rows={4}
                  value={editingFaq ? editingFaq.answer : newFaq.answer}
                  onChange={(e) =>
                    editingFaq
                      ? setEditingFaq({ ...editingFaq, answer: e.target.value })
                      : setNewFaq({ ...newFaq, answer: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={editingFaq ? editingFaq.category : newFaq.category}
                  onValueChange={(value) =>
                    editingFaq
                      ? setEditingFaq({ ...editingFaq, category: value })
                      : setNewFaq({ ...newFaq, category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Account">Account</SelectItem>
                    <SelectItem value="Orders">Orders</SelectItem>
                    <SelectItem value="Quotations">Quotations</SelectItem>
                    <SelectItem value="Technical">Technical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            {editingFaq ? (
              <>
                <Button variant="outline" onClick={() => setEditingFaq(null)}>
                  Cancel
                </Button>
                <Button onClick={handleEditFaq}>Update FAQ</Button>
              </>
            ) : (
              <Button className="w-full" onClick={handleAddFaq}>
                <Plus className="h-4 w-4 mr-2" />
                Add FAQ
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );

  // Render Feedback Tab
  const renderFeedbackTab = () => (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">User Feedback</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Average Rating:</span>
          <div className="flex items-center">
            <span className="font-medium mr-1">{averageRating}</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-4 w-4 ${
                    star <= Math.round(averageRating)
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-muted-foreground"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Comment</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {feedback.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.user}</TableCell>
                <TableCell>{item.comment}</TableCell>
                <TableCell>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= item.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-muted-foreground"
                        }`}
                      />
                    ))}
                  </div>
                </TableCell>
                <TableCell>{item.date}</TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setReplyFeedback(item);
                      setShowFeedbackReplyDialog(true);
                    }}
                  >
                    <Reply className="h-4 w-4 mr-2" />
                    Respond
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog
        open={showFeedbackReplyDialog}
        onOpenChange={setShowFeedbackReplyDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Respond to Feedback</DialogTitle>
            <DialogDescription>
              Responding to {replyFeedback?.user}'s feedback
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="p-4 bg-muted rounded-md">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-4 w-4 ${
                      star <= (replyFeedback?.rating || 0)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-muted-foreground"
                    }`}
                  />
                ))}
              </div>
              <p className="mt-2">{replyFeedback?.comment}</p>
            </div>
            <Label htmlFor="feedback-reply">Your Response</Label>
            <Textarea
              id="feedback-reply"
              placeholder="Type your response here..."
              value={feedbackReplyText}
              onChange={(e) => setFeedbackReplyText(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFeedbackReplyDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleFeedbackReply}>
              <Send className="h-4 w-4 mr-2" />
              Send Response
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Support and Training</h1>

      <Tabs
        defaultValue={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="resources">
            <FileText className="h-4 w-4 mr-2" />
            Resources
          </TabsTrigger>
          <TabsTrigger value="helpdesk">
            <LifeBuoy className="h-4 w-4 mr-2" />
            Helpdesk
          </TabsTrigger>
          <TabsTrigger value="training">
            <BookOpen className="h-4 w-4 mr-2" />
            Training
          </TabsTrigger>
          <TabsTrigger value="knowledge-base">
            <FileQuestion className="h-4 w-4 mr-2" />
            Knowledge Base
          </TabsTrigger>
          <TabsTrigger value="feedback">
            <MessageSquare className="h-4 w-4 mr-2" />
            Feedback
          </TabsTrigger>
        </TabsList>
        <div className="mt-6">
          <TabsContent value="resources">{renderResourcesTab()}</TabsContent>
          <TabsContent value="helpdesk">{renderHelpdeskTab()}</TabsContent>
          <TabsContent value="training">{renderTrainingTab()}</TabsContent>
          <TabsContent value="knowledge-base">
            {renderKnowledgeBaseTab()}
          </TabsContent>
          <TabsContent value="feedback">{renderFeedbackTab()}</TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
