import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { isEmail, isText, isUrl } from "@/utils/validation";
import { useForm, Controller } from "react-hook-form";
import useSingleFileUpload from "@/hooks/useSingleFileUpload";
import { LuArrowRight, LuPaperclip } from "react-icons/lu";

const VendorProfilePage = () => {
  const {
    control,
    handleSubmit,
    // reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      companyName: "",
      email: "",
      companyRepresentative: "",
      phoneNo: "",
      province: "",
      district: "",
      municipality: "",
      streetAddress: "",
      businessCategory: "",
      website: "",
      companyRegistration: "",
      vatOrPan: "",
      taxClearance: "",
      logo: "",
      companyStamp: "",
      additionalDocument: "",
    },
  });

  const {
    fileInputRef: companyRegFileInputRef,
    uploadedFile: companyRegUploadedFile,
    openFileDialog: openCompanyRegFileDialog,
    handleFilesSelected: handleCompanyRegFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: vatOrPanFileInputRef,
    uploadedFile: vatOrPanUploadedFile,
    openFileDialog: openVatOrPanFileDialog,
    handleFilesSelected: handleVatOrPanFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: taxClearFileInputRef,
    uploadedFile: taxClearUploadedFile,
    openFileDialog: openTaxClearFileDialog,
    handleFilesSelected: handleTaxClearFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: logoFileInputRef,
    uploadedFile: logoUploadedFile,
    openFileDialog: openLogoFileDialog,
    handleFilesSelected: handleLogoFilesSelected,
  } = useSingleFileUpload([".png", ".jpg", ".jpeg"], false);

  const {
    fileInputRef: additionalDocFileInputRef,
    uploadedFile: additionalDocUploadedFile,
    openFileDialog: openAdditionalDocFileDialog,
    handleFilesSelected: handleAdditionalDocFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: companyStampFileInputRef,
    uploadedFile: companyStampUploadedFile,
    openFileDialog: openCompanyStampFileDialog,
    handleFilesSelected: handleCompanyStampFilesSelected,
  } = useSingleFileUpload([".png", ".jpg", ".jpeg"], false);

  const onSubmit = async (data) => {
    console.log(data);
  };

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">Profile</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="p-4 pt-0">
        <h1 className="font-nunito text-3xl font-bold">Profile</h1>
        <div className="mt-5">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="companyName"
                >
                  Company Name *
                </label>
                <Controller
                  name="companyName"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Company Name is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid Company Name";
                      }
                      if (value.length < 3) {
                        return "Company Name must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter company name"
                        autoComplete="off"
                        id="companyName"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.companyName && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.companyName?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="email"
                >
                  Email *
                </label>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Email is required",
                    },
                    validate: {
                      isValidEmail: (value) =>
                        isEmail(value) || "Invalid Email Address",
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="email"
                        placeholder="Enter email"
                        autoComplete="off"
                        id="email"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.email && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.email?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="companyRepresentative"
                >
                  Company Representative *
                </label>
                <Controller
                  name="companyRepresentative"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Company Representative is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid Company Representative Name";
                      }
                      if (value.length < 3) {
                        return "Company Representative Name must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter company representative name"
                        autoComplete="off"
                        id="companyRepresentative"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.companyRepresentative && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.companyRepresentative?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="phoneNo"
                >
                  Phone Number *
                </label>
                <Controller
                  name="phoneNo"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Phone Number is required",
                    },
                    pattern: {
                      value: /^[0-9]+$/,
                      message: "Invalid Phone Number",
                    },
                    minLength: {
                      value: 10,
                      message: "Phone Number should be at least 10 characters",
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter phone number"
                        autoComplete="off"
                        id="phoneNo"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.phoneNo
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.phoneNo && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.phoneNo?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="province"
                >
                  Province *
                </label>
                <Controller
                  name="province"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Province is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid Province";
                      }
                      if (value.length < 3) {
                        return "Province must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter province"
                        autoComplete="off"
                        id="province"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.province && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.province?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="district"
                >
                  District *
                </label>
                <Controller
                  name="district"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "District is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid District";
                      }
                      if (value.length < 3) {
                        return "District must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter district"
                        autoComplete="off"
                        id="district"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.district && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.district?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="municipality"
                >
                  Municipality *
                </label>
                <Controller
                  name="municipality"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Municipality is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid Municipality";
                      }
                      if (value.length < 3) {
                        return "Municipality must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter municipality"
                        autoComplete="off"
                        id="municipality"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.municipality && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.municipality?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="streetAddress"
                >
                  Street Address *
                </label>
                <Controller
                  name="streetAddress"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Street Address is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid Street Address";
                      }
                      if (value.length < 3) {
                        return "Street Address must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter street address"
                        autoComplete="off"
                        id="streetAddress"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.email
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.streetAddress && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.streetAddress?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="businessCategory"
                >
                  Business Category *
                </label>
                <Controller
                  name="businessCategory"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "Business category is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) {
                        return "Invalid business category";
                      }
                      if (value.length < 3) {
                        return "Business category must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter business category"
                        autoComplete="off"
                        id="businessCategory"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.businessCategory
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.businessCategory && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.businessCategory?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="website"
                >
                  Website
                </label>
                <Controller
                  name="website"
                  control={control}
                  rules={{
                    validate: (value) => {
                      if (!isUrl(value)) {
                        return "Invalid website";
                      }
                      if (value.length < 3) {
                        return "Website must be at least 3 characters";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="https://websitename.com"
                        autoComplete="off"
                        id="website"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.website
                            ? "border-lightBrown"
                            : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.website && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                          {errors?.website?.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="companyRegistration"
                >
                  Company Registration
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openCompanyRegFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          companyRegUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {companyRegUploadedFile?.name
                          ? companyRegUploadedFile?.name
                          : "Upload Company Registration"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={companyRegFileInputRef}
                    className="hidden"
                    onChange={(e) => handleCompanyRegFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="vatOrPan"
                >
                  VAT/PAN
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openVatOrPanFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          vatOrPanUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {vatOrPanUploadedFile?.name
                          ? vatOrPanUploadedFile?.name
                          : "Upload VAT/PAN"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={vatOrPanFileInputRef}
                    className="hidden"
                    onChange={(e) => handleVatOrPanFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="taxClearance"
                >
                  Tax Clearance Certificate
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openTaxClearFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          taxClearUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {taxClearUploadedFile?.name
                          ? taxClearUploadedFile?.name
                          : "Upload tax clearance"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={taxClearFileInputRef}
                    className="hidden"
                    onChange={(e) => handleTaxClearFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="logo"
                >
                  Logo
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openLogoFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          logoUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {logoUploadedFile?.name
                          ? logoUploadedFile?.name
                          : "Upload logo"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={logoFileInputRef}
                    className="hidden"
                    onChange={(e) => handleLogoFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="companyStamp"
                >
                  Company Stamp
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openCompanyStampFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          companyStampUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {companyStampUploadedFile?.name
                          ? companyStampUploadedFile?.name
                          : "Upload company stamp"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={companyStampFileInputRef}
                    className="hidden"
                    onChange={(e) => handleCompanyStampFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
              <div>
                <label
                  className="ml-1 text-bodySmall font-medium font-nunito"
                  htmlFor="additionalDocument"
                >
                  Additional Document
                </label>
                <div>
                  <div className="w-full">
                    <Button
                      className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                      onClick={(e) => openAdditionalDocFileDialog(e)}
                    >
                      <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                      <span
                        className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                          additionalDocUploadedFile?.name
                            ? "text-darkNavy"
                            : "text-lightGray1"
                        }`}
                      >
                        {additionalDocUploadedFile?.name
                          ? additionalDocUploadedFile?.name
                          : "Upload additional document"}
                      </span>
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={additionalDocFileInputRef}
                    className="hidden"
                    onChange={(e) => handleAdditionalDocFilesSelected(e)}
                    accept=".pdf"
                    multiple={false}
                  />
                </div>
              </div>
            </div>
            <div className="w-full flex justify-end mt-10">
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider"
                type="submit"
              >
                Next Step
                <LuArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default VendorProfilePage;
