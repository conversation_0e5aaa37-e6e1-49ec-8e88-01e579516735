// useExcelUpload.js
import { useState, useCallback } from "react";
import * as XLSX from "xlsx";
import { fixedFields } from "@/utils/specProductFields";

export const useExcelUpload = () => {
  const [data, setData] = useState([]);
  const [error, setError] = useState(null);

  const handleFileUpload = useCallback((event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file extension
    if (!file.name.match(/\.(xls|xlsx|csv)$/)) {
      setError("Please upload a valid Excel file (.xls or .xlsx)");
      return;
    }

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const binaryStr = e.target.result;
        // Using binary string here; you may switch to ArrayBuffer if desired.
        const workbook = XLSX.read(binaryStr, { type: "binary" });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        // Get the data as an array of arrays (rows)
        const rawData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        if (!rawData || rawData.length === 0) {
          setError("No data found in the sheet.");
          return;
        }

        // Assume the first row contains headers.
        const [headers, ...rows] = rawData;

        // Normalize headers: trim and convert to lower case for comparison.
        const normalizedHeaders = headers.map((h) => h?.toString().trim().toLowerCase());

        // Map each row to an object with keys defined in fixedFields.
        const mappedData = rows.map((row) => {
          const obj = {};
          fixedFields.forEach(({ key, label }) => {
            // Find the index of the header that matches the expected label.
            const headerIndex = normalizedHeaders.findIndex(
              (h) => h === label.toLowerCase()
            );
            // If found, use the corresponding cell value, else default to an empty string.
            obj[key] = headerIndex !== -1 ? row[headerIndex] : "";
          });
          return obj;
        });

        setData(mappedData);
        setError(null);
      } catch (err) {
        setError("Error parsing file");
        console.error("Parsing error:", err);
      }
    };

    reader.onerror = (err) => {
      setError("Error reading file");
      console.error("Reader error:", err);
    };

    reader.readAsBinaryString(file);
  }, []);

  return { data, error, setData, handleFileUpload };
};
