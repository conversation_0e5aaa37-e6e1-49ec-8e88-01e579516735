// SpecificationReviewPage.jsx

import { useState, useEffect } from 'react';
import { SpecificationReview } from '../_components/SpecificationReview';
import { useNavigate, useParams } from 'react-router-dom';
import { useSpecification } from '@/context/SpecificationContext';

export default function SpecificationReviewPage() {
  const { slug } = useParams();
  const navigate = useNavigate();

  const { specifications, updateSpecification } = useSpecification();

  const specification = specifications.find((item) => item.id === slug);

  const handleUpdate = (updatedSpec) => {
    updateSpecification(slug, updatedSpec);
    console.log('Specification updated:', updatedSpec);
    navigate('/customer/1/specification-list');
  };

  const handleFinalize = (id) => {
    console.log('Specification finalized:', id);
    navigate('/customer/1/specification-list');
  };

  if (!specification) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-4">Review Specification</h1>
      <SpecificationReview
        specification={specification}
        onUpdate={handleUpdate}
        onFinalize={handleFinalize}
      />
    </div>
  );
}
