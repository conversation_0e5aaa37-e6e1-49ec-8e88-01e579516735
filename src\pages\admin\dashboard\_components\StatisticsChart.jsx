import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Mock data for the chart (in a real app, this would come from an API)
const monthlyData = [
  { month: "Jan", specifications: 45, quotations: 38, users: 12 },
  { month: "Feb", specifications: 52, quotations: 45, users: 18 },
  { month: "Mar", specifications: 48, quotations: 41, users: 15 },
  { month: "Apr", specifications: 61, quotations: 55, users: 22 },
  { month: "May", specifications: 55, quotations: 49, users: 19 },
  { month: "Jun", specifications: 67, quotations: 62, users: 25 },
];

const currentMonth = monthlyData[monthlyData.length - 1];
const previousMonth = monthlyData[monthlyData.length - 2];

const calculateGrowth = (current, previous) => {
  return ((current - previous) / previous * 100).toFixed(1);
};

export function StatisticsChart() {
  const specGrowth = calculateGrowth(currentMonth.specifications, previousMonth.specifications);
  const quotGrowth = calculateGrowth(currentMonth.quotations, previousMonth.quotations);
  const userGrowth = calculateGrowth(currentMonth.users, previousMonth.users);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Statistics</CardTitle>
        <CardDescription>Performance overview for the last 6 months</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Simple bar chart representation */}
        <div className="space-y-6">
          {/* Chart area */}
          <div className="h-48 flex items-end justify-between space-x-2">
            {monthlyData.map((data, index) => (
              <div key={data.month} className="flex-1 flex flex-col items-center space-y-1">
                <div className="w-full flex flex-col items-center space-y-1">
                  {/* Specifications bar */}
                  <div 
                    className="w-full bg-blue-500 rounded-t-sm"
                    style={{ height: `${(data.specifications / 70) * 100}px` }}
                    title={`Specifications: ${data.specifications}`}
                  />
                  {/* Quotations bar */}
                  <div 
                    className="w-full bg-green-500"
                    style={{ height: `${(data.quotations / 70) * 100}px` }}
                    title={`Quotations: ${data.quotations}`}
                  />
                  {/* Users bar */}
                  <div 
                    className="w-full bg-purple-500 rounded-b-sm"
                    style={{ height: `${(data.users / 70) * 100}px` }}
                    title={`New Users: ${data.users}`}
                  />
                </div>
                <span className="text-xs text-muted-foreground">{data.month}</span>
              </div>
            ))}
          </div>

          {/* Legend */}
          <div className="flex flex-wrap gap-4 justify-center">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm">Specifications</span>
              <Badge variant="outline" className="text-xs">
                {specGrowth > 0 ? '+' : ''}{specGrowth}%
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm">Quotations</span>
              <Badge variant="outline" className="text-xs">
                {quotGrowth > 0 ? '+' : ''}{quotGrowth}%
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-500 rounded"></div>
              <span className="text-sm">New Users</span>
              <Badge variant="outline" className="text-xs">
                {userGrowth > 0 ? '+' : ''}{userGrowth}%
              </Badge>
            </div>
          </div>

          {/* Current month summary */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{currentMonth.specifications}</p>
              <p className="text-sm text-muted-foreground">Specifications</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{currentMonth.quotations}</p>
              <p className="text-sm text-muted-foreground">Quotations</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{currentMonth.users}</p>
              <p className="text-sm text-muted-foreground">New Users</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
