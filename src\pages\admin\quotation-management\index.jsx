import { useState } from "react";
import { CalendarIcon, SearchIcon, SlidersHorizontal } from "lucide-react";
import { format } from "date-fns";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useNavigate } from "react-router-dom";

// Mock data for demonstration
const mockQuotations = [
  {
    id: "QT-2023-001",
    customer: "Acme Corp",
    vendor: "Tech Supplies Ltd",
    amount: 12500.0,
    date: new Date(2023, 4, 15),
    status: "pending",
  },
  {
    id: "QT-2023-002",
    customer: "Globex Inc",
    vendor: "Office Solutions",
    amount: 8750.5,
    date: new Date(2023, 4, 18),
    status: "approved",
  },
  {
    id: "QT-2023-003",
    customer: "Wayne Enterprises",
    vendor: "Industrial Equipment Co",
    amount: 34250.75,
    date: new Date(2023, 4, 20),
    status: "rejected",
  },
  {
    id: "QT-2023-004",
    customer: "Stark Industries",
    vendor: "Tech Supplies Ltd",
    amount: 18900.25,
    date: new Date(2023, 4, 22),
    status: "pending",
  },
  {
    id: "QT-2023-005",
    customer: "Umbrella Corp",
    vendor: "Lab Supplies Inc",
    amount: 9650.0,
    date: new Date(2023, 4, 25),
    status: "approved",
  },
  {
    id: "QT-2023-006",
    customer: "Oscorp",
    vendor: "Chemical Solutions",
    amount: 15750.8,
    date: new Date(2023, 4, 28),
    status: "draft",
  },
  {
    id: "QT-2023-007",
    customer: "LexCorp",
    vendor: "Construction Materials",
    amount: 42500.0,
    date: new Date(2023, 5, 1),
    status: "pending",
  },
  {
    id: "QT-2023-008",
    customer: "Cyberdyne Systems",
    vendor: "Tech Supplies Ltd",
    amount: 28750.6,
    date: new Date(2023, 5, 3),
    status: "approved",
  },
];

// Status options
const statusOptions = [
  { value: "draft", label: "Draft", color: "bg-slate-400" },
  { value: "pending", label: "Pending", color: "bg-yellow-400" },
  { value: "approved", label: "Approved", color: "bg-green-400" },
  { value: "rejected", label: "Rejected", color: "bg-red-400" },
];

// Vendors and customers for filter options
const vendors = [...new Set(mockQuotations.map((q) => q.vendor))];
const customers = [...new Set(mockQuotations.map((q) => q.customer))];

export default function QuotationManagementPage() {
  const router = useNavigate();
  const [quotations, setQuotations] = useState(mockQuotations);
  const [search, setSearch] = useState("");
  const [vendorFilter, setVendorFilter] = useState("");
  const [customerFilter, setCustomerFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [dateRange, setDateRange] = useState({
    from: undefined,
    to: undefined,
  });

  // Function to handle status change
  const handleStatusChange = (quotationId, newStatus) => {
    setQuotations(
      quotations.map((quotation) =>
        quotation.id === quotationId
          ? { ...quotation, status: newStatus }
          : quotation
      )
    );
  };

  // Function to navigate to detail view
  const navigateToDetail = (quotationId) => {
    router.push(`/quotations/${quotationId}`);
  };

  // Function to filter quotations based on all filters
  const filteredQuotations = quotations.filter((quotation) => {
    // Search filter
    const searchMatch =
      search === "" ||
      quotation.id.toLowerCase().includes(search.toLowerCase()) ||
      quotation.customer.toLowerCase().includes(search.toLowerCase()) ||
      quotation.vendor.toLowerCase().includes(search.toLowerCase());

    // Vendor filter
    const vendorMatch =
      vendorFilter === "" || quotation.vendor === vendorFilter;

    // Customer filter
    const customerMatch =
      customerFilter === "" || quotation.customer === customerFilter;

    // Status filter
    const statusMatch =
      statusFilter === "" || quotation.status === statusFilter;

    // Date range filter
    let dateMatch = true;
    if (dateRange.from && dateRange.to) {
      dateMatch =
        quotation.date >= dateRange.from && quotation.date <= dateRange.to;
    } else if (dateRange.from) {
      dateMatch = quotation.date >= dateRange.from;
    } else if (dateRange.to) {
      dateMatch = quotation.date <= dateRange.to;
    }

    return (
      searchMatch && vendorMatch && customerMatch && statusMatch && dateMatch
    );
  });

  // Function to reset all filters
  const resetFilters = () => {
    setSearch("");
    setVendorFilter("");
    setCustomerFilter("");
    setStatusFilter("");
    setDateRange({ from: undefined, to: undefined });
  };

  return (
    <Card>
      <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
        <CardTitle className="text-2xl font-bold">All Quotations</CardTitle>
        <div className="flex items-center space-x-2">
          <div className="relative w-full sm:w-64">
            <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search quotations..."
              className="pl-8"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="sr-only">Filter</span>
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Quotations</SheetTitle>
              </SheetHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Vendor</label>
                  <Select value={vendorFilter} onValueChange={setVendorFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Vendors" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Vendors</SelectItem>
                      {vendors.map((vendor) => (
                        <SelectItem key={vendor} value={vendor}>
                          {vendor}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Customer</label>
                  <Select
                    value={customerFilter}
                    onValueChange={setCustomerFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Customers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Customers</SelectItem>
                      {customers.map((customer) => (
                        <SelectItem key={customer} value={customer}>
                          {customer}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Date Range</label>
                  <div className="grid gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.from ? (
                            dateRange.to ? (
                              <>
                                {format(dateRange.from, "LLL dd, y")} -{" "}
                                {format(dateRange.to, "LLL dd, y")}
                              </>
                            ) : (
                              format(dateRange.from, "LLL dd, y")
                            )
                          ) : (
                            <span>Pick a date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="range"
                          selected={dateRange}
                          onSelect={setDateRange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <Button onClick={resetFilters}>Reset Filters</Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Quotation ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead className="hidden md:table-cell">Date</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQuotations.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={6}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No quotations found matching your filters
                  </TableCell>
                </TableRow>
              ) : (
                filteredQuotations.map((quotation) => (
                  <TableRow
                    key={quotation.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => navigateToDetail(quotation.id)}
                  >
                    <TableCell className="font-medium">
                      {quotation.id}
                    </TableCell>
                    <TableCell>{quotation.customer}</TableCell>
                    <TableCell>{quotation.vendor}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      {format(quotation.date, "MMM dd, yyyy")}
                    </TableCell>
                    <TableCell className="text-right">
                      Rs {quotation.amount.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </TableCell>
                    <TableCell
                      onClick={(e) => e.stopPropagation()}
                      className="w-[150px]"
                    >
                      <Select
                        value={quotation.status}
                        onValueChange={(value) =>
                          handleStatusChange(quotation.id, value)
                        }
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue>
                            <div className="flex items-center">
                              <Badge
                                className={`mr-2 ${
                                  statusOptions.find(
                                    (s) => s.value === quotation.status
                                  )?.color
                                }`}
                                variant="outline"
                              >
                                &nbsp;
                              </Badge>
                              {
                                statusOptions.find(
                                  (s) => s.value === quotation.status
                                )?.label
                              }
                            </div>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {statusOptions.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              <div className="flex items-center">
                                <Badge
                                  className={`mr-2 ${status.color}`}
                                  variant="outline"
                                >
                                  &nbsp;
                                </Badge>
                                {status.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
