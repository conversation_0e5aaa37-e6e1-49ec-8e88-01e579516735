import { useEffect, useRef } from "react";
import "@/pages/guest/home/<USER>/home.css";
import TestimonialCard from "@/pages/guest/home/<USER>/TestimonialCard";

const TestimonialSection = () => {
  const scrollRef = useRef(null);

  useEffect(() => {
    const scrollContainer = scrollRef.current;

    const handleScroll = () => {
      if (scrollContainer.scrollLeft >= scrollContainer.scrollWidth / 2) {
        scrollContainer.scrollLeft = 0;
      }
    };

    scrollContainer.addEventListener("scroll", handleScroll);

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="py-20 bg-darkLight">
      <span className="font-nunito font-semibold text-heading6 text-green1 w-full text-center flex justify-center">
        {`Testimonials`}
      </span>
      <div className="w-full mt-9 flex flex-col justify-center items-center">
        <h1 className="font-nunito font-extrabold text-[2rem] lg:text-heading3 text-navy leading-[2.875rem] text-center">
          {`What Our Users Are Saying`}
        </h1>
      </div>
      <div
        className="mt-10 flex w-full overflow-hidden relative"
        style={{ scrollbarWidth: "none" }}
      >
        <div
          ref={scrollRef}
          className="flex gap-5 animate-scroll"
          style={{
            display: "flex",
            animation: "scroll 120s linear infinite", // Adjust timing here based on the speed
          }}
        >
          {/* Cards - Original set of cards */}
          {[...Array(2)].map((_, idx) => (
            <div key={idx} className="flex gap-5">
              <TestimonialCard />
              <TestimonialCard />
              <TestimonialCard />
            </div>
          ))}
          {/* Duplicate the set to make it seamless */}
          {[...Array(2)].map((_, idx) => (
            <div key={`dup-${idx}`} className="flex gap-5">
              <TestimonialCard />
              <TestimonialCard />
              <TestimonialCard />
            </div>
          ))}
        </div>
      </div>
      <div
        className="mt-5 flex w-full overflow-hidden relative"
        style={{ scrollbarWidth: "none" }}
      >
        <div
          ref={scrollRef}
          className="flex gap-5 animate-scroll"
          style={{
            display: "flex",
            animation: "scrollOpposite 120s linear infinite", // Adjust timing here based on the speed
          }}
        >
          {/* Cards - Original set of cards */}
          {[...Array(2)].map((_, idx) => (
            <div key={idx} className="flex gap-5">
              <TestimonialCard />
              <TestimonialCard />
              <TestimonialCard />
            </div>
          ))}
          {/* Duplicate the set to make it seamless */}
          {[...Array(2)].map((_, idx) => (
            <div key={`dup-${idx}`} className="flex gap-5">
              <TestimonialCard />
              <TestimonialCard />
              <TestimonialCard />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;
