import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";

export default function TermsOfServicePage() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-4xl font-bold mb-8 text-navy">Terms of Service</h1>

      <Card className="mb-8">
        <CardContent className="prose max-w-none">
          <h2 className="text-2xl font-semibold mb-4 text-navy">
            1. Acceptance of Terms
          </h2>
          <p className="mb-4  text-darkTeal">
            By accessing and using this service, you accept and agree to be
            bound by the terms and provision of this agreement.
          </p>

          <h2 className="text-2xl font-semibold mb-4 text-navy">
            2. Use of Service
          </h2>
          <p className="mb-4 text-darkTeal">
            You agree to use this service only for purposes permitted by these
            Terms of Service as well as any applicable law, regulation or
            generally accepted practices or guidelines in the relevant
            jurisdictions.
          </p>

          <h2 className="text-2xl font-semibold mb-4 text-navy">
            3. Modifications to Service
          </h2>
          <p className="mb-4 text-darkTeal">
            We reserve the right to modify or discontinue, temporarily or
            permanently, the service (or any part thereof) with or without
            notice at any time.
          </p>

          <p className="mt-8 text-darkTeal">
            For our Privacy Policy, please visit our{" "}
            <Link href="/privacy-policy" className="text-navy hover:underline">
              Privacy Policy page
            </Link>
            .
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
