import { useToast } from "@/components/ui/use-toast";
import { useState } from "react";
import { loginVendor, registerVendor } from "@/services/authApi";

// Custom hook to handle user registration and login
export const useAuth = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Function to handle user login
  const login = async (credentials) => {
    setLoading(true);
    try {
      const response = await loginVendor(credentials);
      if (!response.success) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Credentials are incorrect",
          duration: 1500,
          isclosable: true,
        });
      }
      return response;
    } catch (error) {
      console.log(error);
      toast({
        title: "Login Failed",
        description: error.message || "Error while logging in",
        variant: "destructive",
        duration: 1500,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to handle user registration
  const register = async (userData) => {
    setLoading(true);
    try {
      const response = await registerVendor(userData);
      if (!response.success) {
        toast({
          variant: "destructive",
          title: "Error",
          description:
            response?.errors?.email ||
            "An error occurred while registering user",
          duration: 1500,
          isclosable: true,
        });
      }
      return response;
    } catch (error) {
      toast({
        title: "Registration Failed",
        description: error.email || "Error while registering",
        variant: "destructive",
        duration: 1500,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    login,
    register,
    loading,
  };
};
