// Mock API functions - replace with actual API calls
export const fetchSpecifications = async (
  page = 1,
  search = "",
  status = "all",
  category = "all",
  subcategory = "all",
) => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const mockData = [
    {
      id: "1",
      title: "Custom Steel Brackets Manufacturing",
      description: "Need 500 units of custom steel brackets with specific dimensions and coating requirements.",
      datePosted: "2024-01-15",
      customerId: "cust-1",
      customerName: "ABC Manufacturing Co.",
      status: "open",
      category: "Manufacturing",
      subcategory: "Metal Fabrication",
      budget: 15000,
    },
    {
      id: "2",
      title: "Electronic Component Assembly",
      description:
        "Looking for a vendor to assemble 1000 electronic control units according to provided specifications.",
      datePosted: "2024-01-14",
      customerId: "cust-2",
      customerName: "TechCorp Industries",
      status: "open",
      category: "Electronics",
      subcategory: "PCB Assembly",
    },
    {
      id: "3",
      title: "Packaging Design and Production",
      description: "Require eco-friendly packaging solution for consumer products with custom branding.",
      datePosted: "2024-01-13",
      customerId: "cust-3",
      customerName: "GreenPack Solutions",
      status: "quoted",
      category: "Packaging",
      subcategory: "Sustainable Packaging",
    },
    {
      id: "4",
      title: "Industrial Machinery Parts",
      description: "Custom machined parts for industrial equipment with tight tolerances and quality requirements.",
      datePosted: "2024-01-12",
      customerId: "cust-4",
      customerName: "Heavy Industries Ltd.",
      status: "open",
      category: "Manufacturing",
      subcategory: "Precision Machining",
      budget: 25000,
    },
    {
      id: "5",
      title: "Software Integration Services",
      description: "Need integration services for connecting multiple software systems with API development.",
      datePosted: "2024-01-11",
      customerId: "cust-5",
      customerName: "Digital Solutions Inc.",
      status: "closed",
      category: "Software",
      subcategory: "API Development",
    },
    {
      id: "6",
      title: "Custom Plastic Molding",
      description: "Need custom plastic molded parts for consumer electronics with specific material requirements.",
      datePosted: "2024-01-10",
      customerId: "cust-6",
      customerName: "Consumer Tech Inc.",
      status: "open",
      category: "Manufacturing",
      subcategory: "Plastic Molding",
      budget: 18000,
    },
    {
      id: "7",
      title: "Mobile App Development",
      description: "Looking for a vendor to develop a cross-platform mobile application with specific features.",
      datePosted: "2024-01-09",
      customerId: "cust-7",
      customerName: "MobileTech Solutions",
      status: "open",
      category: "Software",
      subcategory: "Mobile Development",
      budget: 35000,
    },
  ]

  // Filter by search, status, category, and subcategory
  let filteredData = mockData
  if (search) {
    filteredData = filteredData.filter(
      (spec) =>
        spec.title.toLowerCase().includes(search.toLowerCase()) ||
        spec.description.toLowerCase().includes(search.toLowerCase()),
    )
  }
  if (status !== "all") {
    filteredData = filteredData.filter((spec) => spec.status === status)
  }
  if (category !== "all") {
    filteredData = filteredData.filter((spec) => spec.category === category)
  }
  if (subcategory !== "all") {
    filteredData = filteredData.filter((spec) => spec.subcategory === subcategory)
  }

  return {
    data: filteredData,
    total: filteredData.length,
    pages: Math.ceil(filteredData.length / 10),
  }
}

export const submitQuotation = async (data) => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 2000))

  // Simulate file upload URLs
  const certificationUrls = data.certifications.map(
    (_, index) => `https://example.com/certs/cert-${Date.now()}-${index}.pdf`,
  )
  const productImageUrls = data.productImages.map(
    (_, index) => `https://example.com/images/img-${Date.now()}-${index}.jpg`,
  )

  const quotation = {
    id: `quote-${Date.now()}`,
    specificationId: data.specificationId,
    vendorId: "vendor-123",
    productDetails: data.productDetails,
    price: data.price,
    quantity: data.quantity,
    deliveryTimeline: data.deliveryTimeline,
    termsAndConditions: data.termsAndConditions,
    certificationUrls,
    productImageUrls,
    status: "pending",
    createdAt: new Date().toISOString(),
  }

  return quotation
}
