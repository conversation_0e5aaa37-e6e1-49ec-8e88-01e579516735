import PropTypes from "prop-types";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "@/redux/store"; // Adjust this path to your project structure
import { HashLoader } from "react-spinners";

const StoreProvider = ({ children }) => {
  return (
    <Provider store={store}>
      <PersistGate loading={<HashLoader />} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
};
StoreProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default StoreProvider;
