import { useSpecification } from '@/context/SpecificationContext';
import React, { useEffect, useState } from 'react'
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import Template1 from '../_components/templates/Template1';
import Template2 from '../_components/templates/Template2';
import Template3 from '../_components/templates/Template3';
import { redirect, useNavigate } from 'react-router-dom';
import EditableTemplate from '../_components/templates/EditableTemplate';
import template1 from "@/assets/png/template1.png";
import template2 from "@/assets/png/template2.png";
import template3 from "@/assets/png/template3.png";

const templates = [
    { id: 1, name: "Simple", imageUrl: template1, component: Template1 },
    { id: 2, name: "Professional", imageUrl: template2, component: Template2 },
    { id: 3, name: "Modern", imageUrl: template3, component: Template3 },
]

const SpecPreview = () => {

    const navigate = useNavigate()
    const {
        specificationProductData,
        setSpecificationProductData
    } = useSpecification();

    const [selectedTemplate, setSelectedTemplate] = useState(1)
    const [isEditMode, setIsEditMode] = useState(false);

    const SelectedTemplateComponent = templates.find((t) => t.id === selectedTemplate).component

    useEffect(() => {
        if (!specificationProductData || specificationProductData.length < 1) {
            navigate('/customer/1/create-specification');
        }
    }, [specificationProductData, navigate]);

    if (!specificationProductData || specificationProductData.length < 1) {
        return null;
    }

    return (
        <div>
            <header className="flex h-12 shrink-0 items-center gap-2">
                <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <Breadcrumb className="font-nunito">
                        <BreadcrumbList>
                            <BreadcrumbItem className="hidden md:block">
                                <BreadcrumbLink href="#">Specifications Preview</BreadcrumbLink>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
            </header>

            <div className='w-full'>
                {
                    isEditMode ? (
                        <EditableTemplate
                            data={{ specificationProductData }}
                            onSave={(newData) => {
                                setSpecificationProductData(newData);
                                setIsEditMode(false);
                            }}
                        />
                    ) : (
                        <div className='w-full min-h-screen grid grid-cols-4'>
                            <div className='col-span-3'>
                                <div className="w-full p-4 rounded-lg">
                                    <h2 className="text-lg font-semibold mb-4">Preview:</h2>

                                    <SelectedTemplateComponent data={{ specificationProductData }} />

                                </div>

                                {
                                    !isEditMode && (
                                        <div className='w-full flex justify-end gap-3 p-4'>
                                            <button
                                                onClick={() => setIsEditMode(!isEditMode)}
                                                className='w-[100px] font-bold tracking-wider flex items-center justify-center border-2 border-primary text-primary px-6 py-3 rounded-lg'
                                            >
                                                Edit
                                            </button>
                                            <button className='w-[100px] font-bold tracking-wider flex items-center justify-center border-2 border-primary bg-primary text-black rounded-lg px-6 py-3'>
                                                Continue
                                            </button>
                                        </div>
                                    )
                                }

                            </div>
                            <div className='col-span-1 max-h-screen overflow-y-auto p-2 border-l'>
                                <div className="mb-6">
                                    <h2 className="text-lg font-semibold mb-2">Select a Template:</h2>
                                    <div className="flex flex-col space-y-4">
                                        {templates.map((template) => (
                                            <button
                                                key={template.id}
                                                onClick={() => setSelectedTemplate(template.id)}
                                                className={`px-4 py-2 rounded ${selectedTemplate === template.id
                                                    ? " border-2 border-primary"
                                                    : " text-gray-800 hover:bg-gray-300"
                                                    } bg-gray-100`}
                                            >
                                                {template.name}
                                                <img src={template.imageUrl} />
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )
                }

            </div>
        </div>
    )
}

export default SpecPreview