import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const items = [
  {
    id: 1,
    description: "Lorem ipsum Dolor",
    price: 50.0,
    quantity: 1,
    total: 50.0,
  },
  {
    id: 2,
    description: "Pellentesque id neque ligula",
    price: 20.0,
    quantity: 3,
    total: 60.0,
  },
  {
    id: 3,
    description: "Interdum et malesuada fames",
    price: 10.0,
    quantity: 2,
    total: 20.0,
  },
  {
    id: 4,
    description: "Vivamus volutpat feugibus",
    price: 90.0,
    quantity: 1,
    total: 90.0,
  },
];

export default function SpecTemplate3() {
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <div className="relative overflow-hidden">
        {/* Pink diagonal background */}
        <div className="absolute top-0 left-0 w-full h-40 bg-pink-500 transform -skew-y-6 origin-top-left" />

        <div className="relative p-8">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div className="text-white">
              <h1 className="text-4xl font-bold mb-6">INVOICE</h1>
              <div className="space-y-1">
                <p className="font-medium">Invoice to:</p>
                <p>Org name Clark</p>
                <p>24 Kennedy Street Area,</p>
                <p>Location, Lorem ipsum,</p>
                <p>ST12345</p>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-gray-600">Brand Name</p>
                <div className="space-y-1">
                  <div className="flex justify-between gap-8">
                    <span className="text-sm font-medium">Invoice#</span>
                    <span className="text-sm">52145</span>
                  </div>
                  <div className="flex justify-between gap-8">
                    <span className="text-sm font-medium">Date</span>
                    <span className="text-sm">03/02/2020</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="mt-8 bg-white rounded-lg shadow-sm">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">SL.</TableHead>
                  <TableHead>Item Description</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Qty</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.id}</TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell className="text-right">
                      Rs {item.price.toFixed(2)}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.quantity}
                    </TableCell>
                    <TableCell className="text-right">
                      Rs {item.total.toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Footer */}
          <div className="mt-8 flex justify-between">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Payment Info:</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>Account Number: **** **** **** 1234</p>
                  <p>ACH Name: Your bank details</p>
                  <p>Bank Details: Add your bank details</p>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Terms & Conditions:</h3>
                <p className="text-sm text-gray-600 max-w-md">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce
                  dignissim venenatis elit. Proin dignissim consequat.
                </p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="space-y-1">
                <div className="flex justify-between gap-8">
                  <span className="font-medium">Sub Total:</span>
                  <span>$220.00</span>
                </div>
                <div className="flex justify-between gap-8">
                  <span className="font-medium">Tax:</span>
                  <span>0.00%</span>
                </div>
                <div className="flex justify-between gap-8 text-lg font-bold">
                  <span>Total:</span>
                  <span>$220.00</span>
                </div>
              </div>
              <div className="pt-8 border-t">
                <p className="font-medium mb-2">Authorized Sign</p>
                <div className="h-12"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
