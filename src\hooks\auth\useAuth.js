import { useCustomerAuth } from "./useCustomerAuth";
import { useVendorA<PERSON> } from "./useVendorAuth";
import { useAdminAuth } from "./useAdminAuth";
import { getUserTypeFromRoute } from "@/utils/authUtils";
import { getAuthenticatedUserTypes } from "@/utils/setCookies";

/**
 * Unified authentication hook that automatically determines
 * which specific auth hook to use based on the current route
 */
export const useAuth = () => {
  const customerAuth = useCustomerAuth();
  const vendorAuth = useVendorAuth();
  const adminAuth = useAdminAuth();

  // Determine which auth context to use based on current route
  const getCurrentAuth = () => {
    const currentPath = window.location.pathname;
    const userType = getUserTypeFromRoute(currentPath);

    switch (userType) {
      case "user": // customer
        return customerAuth;
      case "vendor":
        return vendorAuth;
      case "admin":
        return adminAuth;
      default:
        // If no specific route match, return the first authenticated user type
        const authenticatedTypes = getAuthenticatedUserTypes();
        if (authenticatedTypes.includes("user")) return customerAuth;
        if (authenticatedTypes.includes("vendor")) return vendorAuth;
        if (authenticatedTypes.includes("admin")) return adminAuth;
        
        // Default to customer auth if no authenticated sessions
        return customerAuth;
    }
  };

  const currentAuth = getCurrentAuth();

  // Multi-session utilities
  const getAllAuthenticatedSessions = () => {
    const sessions = [];
    
    if (customerAuth.isAuthenticated) {
      sessions.push({
        userType: "customer",
        user: customerAuth.user,
        auth: customerAuth,
      });
    }
    
    if (vendorAuth.isAuthenticated) {
      sessions.push({
        userType: "vendor",
        user: vendorAuth.user,
        auth: vendorAuth,
      });
    }
    
    if (adminAuth.isAuthenticated) {
      sessions.push({
        userType: "admin",
        user: adminAuth.user,
        auth: adminAuth,
      });
    }
    
    return sessions;
  };

  const getAuthByUserType = (userType) => {
    switch (userType) {
      case "user":
      case "customer":
        return customerAuth;
      case "vendor":
        return vendorAuth;
      case "admin":
        return adminAuth;
      default:
        return null;
    }
  };

  const logoutAll = () => {
    customerAuth.logout();
    vendorAuth.logout();
    adminAuth.logout();
  };

  const isAnyAuthenticated = () => {
    return customerAuth.isAuthenticated || vendorAuth.isAuthenticated || adminAuth.isAuthenticated;
  };

  const getCurrentUserType = () => {
    const currentPath = window.location.pathname;
    return getUserTypeFromRoute(currentPath);
  };

  return {
    // Current route-based auth (primary interface)
    ...currentAuth,
    
    // Multi-session utilities
    getAllAuthenticatedSessions,
    getAuthByUserType,
    logoutAll,
    isAnyAuthenticated,
    getCurrentUserType,
    
    // Direct access to specific auth hooks
    customerAuth,
    vendorAuth,
    adminAuth,
    
    // Convenience properties
    isMultiSession: getAllAuthenticatedSessions().length > 1,
    authenticatedSessions: getAllAuthenticatedSessions(),
  };
};

export default useAuth;
