import {
  Badge<PERSON><PERSON><PERSON>,
  Bell,
  Book<PERSON><PERSON>,
  Bot,
  ChevronRight,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Settings2,
  Sparkles,
  SquareTerminal,
  UserPen,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import logo from "@/assets/png/logo.png";
import useAuthHooks from "@/hooks/authUser/useAuthUser";
import { useEffect } from "react";
import { useGetCustomerProfileQuery } from "@/services/auth/query";
import getShortName from "@/utils/getShortName";
import { Link, useNavigate } from "react-router-dom";

const DashboardSidebar = () => {
  const { handleLogout, isLogout } = useAuthHooks();
  const navigate = useNavigate();

  const customerData = useGetCustomerProfileQuery();
  console.log(customerData.data.data.type);
  const customerType = customerData?.data?.data?.type;

  const navigateToKYCUpdate = () => {
    if (customerType === "individual") {
      navigate("/verify");
    } else if (customerType === "organization") {
      navigate("/kyc-verify");
    }
  };

  useEffect(() => {
    if (isLogout) {
      window.location.href = "/";
    }
  }, [isLogout]);

  const data = {
    user: {
      name: customerData?.data?.data?.name,
      email: customerData?.data?.data?.email,
      avatar:
        customerData?.data?.data?.profile || "/avatars/shadcn.jpg",
    },
    navMain: [
      {
        title: "Overview",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Feeds",
            url: "/customer/1/feeds",
          },
          {
            title: "Notices",
            url: "/customer/1/notices",
          },
          {
            title: "Dashboard",
            url: "/customer/1/dashboard",
          },
        ],
      },
      {
        title: "Specification",
        url: "#",
        icon: Bot,
        items: [
          {
            title: "Create Specification",
            url: "/customer/1/create-specification",
          },
          {
            title: "View Specification",
            url: "/customer/1/view-all-specification",
          },
        ],
      },
      {
        title: "Quotation",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "Received Quotation",
            url: "#",
          },
        ],
      },
      {
        title: "Administration",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "Invoices",
            url: "#",
          },
          {
            title: "Purchase Orders",
            url: "#",
          },
          {
            title: "Transactions",
            url: "#",
          },
          {
            title: "Reports",
            url: "#",
          },
          {
            title: "Settings",
            url: "#",
          },
        ],
      },
    ],
  };

  return (
    <Sidebar variant="inset" className="font-nunito">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <img
                src={logo}
                alt="Zettabid"
                loading="lazy"
                className="object-contain w-[10rem] h-full"
              />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent style={{ scrollbarWidth: "thin" }}>
        <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {data?.navMain.map((item) => (
              <Collapsible
                key={item?.title}
                asChild
                defaultOpen={item?.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item?.title}>
                      {item?.icon && <item.icon />}
                      <span>{item?.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item?.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem?.url}>
                              <span>{subItem?.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={data?.user?.avatar}
                      alt={data?.user?.name}
                    />
                    <AvatarFallback className="rounded-lg uppercase">
                      {getShortName(data?.user?.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold capitalize">
                      {data?.user?.name}
                    </span>
                    <span className="truncate text-xs">
                      {data?.user?.email}
                    </span>
                  </div>
                  <ChevronsUpDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg font-nunito"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage
                        src={data?.user?.avatar}
                        alt={data?.user?.name}
                      />
                      <AvatarFallback className="rounded-lg uppercase">
                        {getShortName(data?.user?.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold capitalize">
                        {data?.user?.name}
                      </span>
                      <span className="truncate text-xs">
                        {data?.user?.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Upgrade to Pro
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <BadgeCheck className="w-4 h-4 mr-2" />
                    Account
                  </DropdownMenuItem>

                  <DropdownMenuItem onClick={navigateToKYCUpdate}>
                    <UserPen className="w-4 h-4 mr-2" />
                    Update KYC
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default DashboardSidebar;
