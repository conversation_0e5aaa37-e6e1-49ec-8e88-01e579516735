import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { isText, isUrl } from "@/utils/validation";
import { useForm, Controller } from "react-hook-form";
import { LuArrowLeft, LuArrowRight } from "react-icons/lu";
import useSingleFileUpload from "@/hooks/useSingleFileUpload";
import { LuPaperclip } from "react-icons/lu";

const BusinessInfoForm = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      businessCategory: setup?.businessCategory || "",
      website: setup?.website || "",
      companyRegistration: setup?.companyRegistration || "",
      vatOrPan: setup?.vatOrPan || "",
    },
  });

  // Using custom hooks for file upload (separate for each field)
  const {
    fileInputRef: companyRegFileInputRef,
    uploadedFile: companyRegUploadedFile,
    openFileDialog: openCompanyRegFileDialog,
    handleFilesSelected: handleCompanyRegFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: vatOrPanFileInputRef,
    uploadedFile: vatOrPanUploadedFile,
    openFileDialog: openVatOrPanFileDialog,
    handleFilesSelected: handleVatOrPanFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const handleBack = () => {
    dispatch(
      setSetup({
        ...setup,
        step: 2,
      })
    );
  };

  const onSubmit = (data) => {
    console.log(data);
    console.log("Company Registration File:", companyRegUploadedFile);
    console.log("VAT/PAN File:", vatOrPanUploadedFile);
    dispatch(
      setSetup({
        ...setup,
        businessCategory: data?.businessCategory,
        website: data?.website,
        companyRegistration: companyRegUploadedFile?.name,
        vatOrPan: vatOrPanUploadedFile?.name,
        step: 4,
      })
    );
  };

  return (
    <div className="max-w-[40rem] sm:min-w-[40rem]">
      <div className="flex flex-col">
        <h2 className="font-nunito text-bodyLarge sm:text-[1.8rem] text-center font-bold">{`Vendor Verification`}</h2>
        <span className="font-nunito text-center">{`Basic Details`}</span>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-10">
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="businessCategory"
            >
              Business Category *
            </label>
            <Controller
              name="businessCategory"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Business category is required",
                },
                validate: (value) => {
                  if (!isText(value)) {
                    return "Invalid business category";
                  }
                  if (value.length < 3) {
                    return "Business category must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter business category"
                    autoComplete="off"
                    id="businessCategory"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.businessCategory
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.businessCategory && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.businessCategory?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="website"
            >
              Website
            </label>
            <Controller
              name="website"
              control={control}
              rules={{
                validate: (value) => {
                  if (!isUrl(value)) {
                    return "Invalid website";
                  }
                  if (value.length < 3) {
                    return "Website must be at least 3 characters";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <input
                    {...field}
                    type="text"
                    placeholder="https://websitename.com"
                    autoComplete="off"
                    id="website"
                    className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                      errors.website
                        ? "border-lightBrown"
                        : "border-[#CFCFCF] focus:border-[#006AFF]"
                    } mb-1 focus:outline-none`}
                  />
                  {errors?.website && (
                    <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                      {errors?.website?.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="companyRegistration"
            >
              Company Registration
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openCompanyRegFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      companyRegUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {companyRegUploadedFile?.name
                      ? companyRegUploadedFile?.name
                      : "Upload Company Registration"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={companyRegFileInputRef}
                className="hidden"
                onChange={(e) => handleCompanyRegFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="vatOrPan"
            >
              VAT/PAN
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openVatOrPanFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      vatOrPanUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {vatOrPanUploadedFile?.name
                      ? vatOrPanUploadedFile?.name
                      : "Upload VAT/PAN"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={vatOrPanFileInputRef}
                className="hidden"
                onChange={(e) => handleVatOrPanFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
        </div>
        <div className="float-right mt-5 flex flex-col xs:flex-row items-center gap-4">
          <Button
            variant="outline"
            className="font-nunito flex items-center text-bodyMedium font-extrabold hover:bg-green1 text-darkNavy h-[3rem] px-10 sm:px-8 rounded-xl tracking-wider"
            onClick={handleBack}
          >
            <LuArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider"
            type="submit"
          >
            Next Step
            <LuArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BusinessInfoForm;
