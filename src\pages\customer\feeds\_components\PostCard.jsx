import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { LuThumbsUp, LuSend, LuEllipsisVertical, LuDot } from "react-icons/lu";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const PostCard = ({id, author, role, avatar, content, tags, image, likes, isVendor }) => {

  const navigate = useNavigate();

  return (
    <div className="border border-darkMedium py-2 md:py-4 rounded-xl shadow-sm">
      <div className="flex justify-between items-center px-2 md:px-4 border-b border-darkMedium pb-3">
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarImage src={avatar} alt={author} />
            <AvatarFallback>{author?.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col pt-2">
            <span className="text-bodySmall font-medium font-nunito leading-3">
              {author}
            </span>
            <span className="text-textSmall font-nunito">{role}</span>
          </div>
        </div>
        <Button className="bg-transparent px-2 transition-all duration-200">
          <LuEllipsisVertical className="w-4 h-4 text-navy" />
        </Button>
      </div>

      <div className="my-4 px-2 md:px-4">
        <p>{content}</p>
      </div>

      <div className="flex items-center font-nunito px-2 md:px-4 gap-1">
        {tags.map((tag, index) => (
          <div key={index} className="flex items-center gap-1">
            <span className="text-textSmall font-bold">{tag}</span>
            {index < tags.length - 1 && <LuDot className="w-4 h-4" />}
          </div>
        ))}
      </div>

      <div className="my-2">
        <img src={image} alt="post" className="w-full h-auto rounded-md" />
      </div>

      <div className="flex items-center gap-2 pt-2 border-t border-darkMedium px-2 md:px-4">
        <button className="flex items-center gap-2 w-fit h-[2rem] px-2 py-1 rounded-xl hover:bg-darkMedium">
          <LuThumbsUp className="w-4 h-4" />
          <span>{likes}</span>
        </button>
        <button className="px-2 py-1 h-[2rem] rounded-xl hover:bg-darkMedium">
          <LuSend className="w-4 h-4" />
        </button>
        {
          isVendor && (
            <button onClick={()=> navigate(`/vendor/1/feeds/${id}/request-quote`)} className="px-2 py-1 h-[2rem] rounded-xl hover:bg-darkMedium">
              Send Quote
            </button>
          )
        }
      </div>
    </div>
  );
};

export default PostCard;
