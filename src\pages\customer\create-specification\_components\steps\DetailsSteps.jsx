import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'



export function DetailsStep({ currentSpec, onSpecChange, onAddSpecification, isEnabled }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Step 3: Product Details</CardTitle>
        <CardDescription>Provide details for the selected product or service</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Name of the product or service</Label>
            <Input
              id="name"
              value={currentSpec.name}
              onChange={(e) => onSpecChange('name', e.target.value)}
              disabled={!isEnabled}
            />
          </div>
          <div>
            <Label htmlFor="features">Features or additional details</Label>
            <Textarea
              id="features"
              value={currentSpec.features}
              onChange={(e) => onSpecChange('features', e.target.value)}
              disabled={!isEnabled}
            />
          </div>
          <div>
            <Label htmlFor="quantity">Required quantity</Label>
            <Input
              id="quantity"
              type="number"
              value={currentSpec.quantity}
              onChange={(e) => onSpecChange('quantity', e.target.value)}
              disabled={!isEnabled}
            />
          </div>
          <div>
            <Label htmlFor="brand">Brand preferences (optional)</Label>
            <Input
              id="brand"
              value={currentSpec.brand}
              onChange={(e) => onSpecChange('brand', e.target.value)}
              disabled={!isEnabled}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={onAddSpecification} disabled={!isEnabled || !currentSpec.name || !currentSpec.quantity}>
          Add Specification
        </Button>
      </CardFooter>
    </Card>
  )
}

