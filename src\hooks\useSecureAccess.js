import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useAppSelector } from "@/hooks/StoreHooks";
import { useToast } from "@/components/ui/use-toast";
import { validateRouteAccess } from "@/utils/secureApiRequest";
import { getAuthenticationStatus } from "@/utils/authUtils";

/**
 * Custom hook for secure access validation
 * Validates user permissions and handles unauthorized access attempts
 */
export function useSecureAccess(routeType, options = {}) {
  const {
    redirectOnUnauthorized = true,
    showToastOnError = true,
    validateUserId = true,
  } = options;

  const { user } = useAppSelector((state) => state.auth);
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const { toast } = useToast();

  const [accessState, setAccessState] = useState({
    isLoading: true,
    isAuthorized: false,
    error: null,
  });

  useEffect(() => {
    const validateAccess = () => {
      try {
        // Validate route access
        const validation = validateRouteAccess(params, routeType);

        if (!validation.isValid) {
          setAccessState({
            isLoading: false,
            isAuthorized: false,
            error: validation.error,
          });

          if (showToastOnError) {
            toast({
              title: "Access Denied",
              description: validation.error,
              variant: "destructive",
            });
          }

          if (redirectOnUnauthorized && validation.redirectTo) {
            navigate(validation.redirectTo, { replace: true });
          }

          return;
        }

        // Additional user ID validation if required
        if (validateUserId) {
          const authStatus = getAuthenticationStatus(user);
          const userIdParam = `${routeType}Id`;
          const urlUserId = params[userIdParam];

          if (urlUserId && authStatus.userId !== urlUserId) {
            setAccessState({
              isLoading: false,
              isAuthorized: false,
              error: "You can only access your own account.",
            });

            if (showToastOnError) {
              toast({
                title: "Access Denied",
                description: "You can only access your own account.",
                variant: "destructive",
              });
            }

            if (redirectOnUnauthorized) {
              navigate(authStatus.dashboardPath, { replace: true });
            }

            return;
          }
        }

        // Access is valid
        setAccessState({
          isLoading: false,
          isAuthorized: true,
          error: null,
        });

      } catch (error) {
        console.error("Access validation error:", error);
        setAccessState({
          isLoading: false,
          isAuthorized: false,
          error: "An error occurred while validating access.",
        });

        if (showToastOnError) {
          toast({
            title: "Error",
            description: "An error occurred while validating access.",
            variant: "destructive",
          });
        }
      }
    };

    validateAccess();
  }, [user, params, routeType, navigate, toast, location.pathname, redirectOnUnauthorized, showToastOnError, validateUserId]);

  return accessState;
}

/**
 * Hook to validate specific user access (for accessing other users' data)
 */
export function useUserAccess(targetUserId, requiredUserType = null) {
  const { user } = useAppSelector((state) => state.auth);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const authStatus = getAuthenticationStatus(user);
    
    if (!authStatus.isAuthenticated) {
      setHasAccess(false);
      return;
    }

    // Check user type if specified
    if (requiredUserType && authStatus.userType !== requiredUserType) {
      setHasAccess(false);
      return;
    }

    // Check if user is accessing their own data or is admin
    const canAccess = authStatus.userId === targetUserId || authStatus.userType === "admin";
    setHasAccess(canAccess);

  }, [user, targetUserId, requiredUserType]);

  return hasAccess;
}

/**
 * Hook to validate API access before making requests
 */
export function useApiAccess() {
  const { user } = useAppSelector((state) => state.auth);

  const validateApiCall = (endpoint, method = "GET", targetUserId = null) => {
    const authStatus = getAuthenticationStatus(user);
    
    if (!authStatus.isAuthenticated) {
      throw new Error("Authentication required");
    }

    // Validate user-specific endpoints
    if (targetUserId && authStatus.userId !== targetUserId && authStatus.userType !== "admin") {
      throw new Error("Access denied. You can only access your own data.");
    }

    // Validate endpoint access based on user type
    const endpointRules = {
      "/customer/": ["user"],
      "/vendor/": ["vendor"],
      "/admin/": ["admin"],
    };

    for (const [pattern, allowedTypes] of Object.entries(endpointRules)) {
      if (endpoint.includes(pattern) && !allowedTypes.includes(authStatus.userType)) {
        throw new Error("Access denied. Insufficient permissions.");
      }
    }

    return true;
  };

  return { validateApiCall };
}

/**
 * Hook for role-based access control
 */
export function useRoleAccess(requiredRoles = []) {
  const { user } = useAppSelector((state) => state.auth);
  const [hasRole, setHasRole] = useState(false);

  useEffect(() => {
    const authStatus = getAuthenticationStatus(user);
    
    if (!authStatus.isAuthenticated) {
      setHasRole(false);
      return;
    }

    // Check if user has any of the required roles
    const userHasRole = requiredRoles.length === 0 || requiredRoles.includes(authStatus.userType);
    setHasRole(userHasRole);

  }, [user, requiredRoles]);

  return hasRole;
}
