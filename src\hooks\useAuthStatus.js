import { useState, useEffect } from "react";
import { useAppSelector } from "@/hooks/StoreHooks";
import { getAuthenticationStatus } from "@/utils/authUtils";

/**
 * Custom hook to get authentication status
 * @returns {Object} Authentication status and user info
 */
export const useAuthStatus = () => {
  const { user } = useAppSelector((state) => state.auth);
  const [authStatus, setAuthStatus] = useState({
    isAuthenticated: false,
    userType: null,
    userId: null,
    dashboardPath: null,
    isLoading: true,
  });

  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const status = getAuthenticationStatus(user);
        setAuthStatus({
          ...status,
          isLoading: false,
        });
      } catch (error) {
        console.error("Error checking auth status:", error);
        setAuthStatus({
          isAuthenticated: false,
          userType: null,
          userId: null,
          dashboardPath: null,
          isLoading: false,
        });
      }
    };

    checkAuthStatus();
  }, [user]);

  return authStatus;
};

export default useAuthStatus;
