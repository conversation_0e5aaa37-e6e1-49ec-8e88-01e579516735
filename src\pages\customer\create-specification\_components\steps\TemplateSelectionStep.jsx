import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import basic from "@/assets/png/template1.png";
import detailed from "@/assets/png/template2.png";
import technical from "@/assets/png/template3.png";

const templates = [
  {
    id: "basic",
    name: "Basic Template",
    description: "A simple form for basic product details",
    img: basic,
  },
  {
    id: "detailed",
    name: "Detailed Template",
    description: "An extensive form for comprehensive product information",
    img: detailed,
  },
  {
    id: "technical",
    name: "Technical Template",
    description: "Specialized form for technical product specifications",
    img: technical,
  },
];

export function TemplateSelectionStep({ onSelectTemplate }) {
  const navigate = useNavigate();

  return (
    <Card className="p-6 shadow-lg rounded-lg bg-white">
      <CardHeader className="mb-4 text-center">
        <CardTitle className="text-xl font-bold text-gray-800">
          Step 3: Select a Template
        </CardTitle>
        <CardDescription className="text-gray-600">
          Choose a template for entering product details
        </CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <div
            key={template.id}
            className="cursor-pointer border rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 hover:bg-gray-50"
            onClick={() =>
              navigate(
                `/customer/1/create-specification?template=${template.id}`
              )
            }
          >
            <div
              className="w-full"
              onClick={() => onSelectTemplate(template.id)}
            >
              <h3 className="font-semibold text-lg text-gray-800">
                {template.name}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {template.description}
              </p>
              <img
                className="w-full h-auto rounded-lg object-cover"
                src={template.img}
                alt={template.name}
              />
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
