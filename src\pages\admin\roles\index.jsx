import { useState } from "react";
import { Plus, Edit, Trash2, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

// API imports
import { useGetRolesQuery, useGetPermissionsQuery } from "@/services/admin/queries";
import {
  useCreateRoleMutation,
  useUpdateRoleMutation,
  useDeleteRoleMutation,
} from "@/services/admin/mutation";

export default function RolesPage() {
  const { toast } = useToast();
  
  // API hooks
  const { data: rolesData, isLoading: rolesLoading } = useGetRolesQuery();
  const { data: permissionsData, isLoading: permissionsLoading } = useGetPermissionsQuery();
  const createRoleMutation = useCreateRoleMutation();
  const updateRoleMutation = useUpdateRoleMutation();
  const deleteRoleMutation = useDeleteRoleMutation();

  // Local state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentRole, setCurrentRole] = useState(null);
  const [newRole, setNewRole] = useState({
    name: "",
    display_name: "",
    description: "",
    permissions: [],
  });
  const [isEditMode, setIsEditMode] = useState(false);

  // Default permissions for when API is not available
  const defaultPermissions = [
    {
      id: 1,
      name: "manage_users",
      display_name: "Manage Users",
      description: "Create, edit, and delete users",
      group: "User Management"
    },
    {
      id: 2,
      name: "view_users",
      display_name: "View Users",
      description: "View user information",
      group: "User Management"
    },
    {
      id: 3,
      name: "manage_content",
      display_name: "Manage Content",
      description: "Create and edit content",
      group: "Content Management"
    },
    {
      id: 4,
      name: "publish_content",
      display_name: "Publish Content",
      description: "Publish and unpublish content",
      group: "Content Management"
    },
    {
      id: 5,
      name: "manage_roles",
      display_name: "Manage Roles",
      description: "Create and edit roles and permissions",
      group: "System Administration"
    },
    {
      id: 6,
      name: "manage_settings",
      display_name: "Manage Settings",
      description: "Access and modify system settings",
      group: "System Administration"
    },
    {
      id: 7,
      name: "view_reports",
      display_name: "View Reports",
      description: "Access system reports and analytics",
      group: "Reporting"
    },
    {
      id: 8,
      name: "export_data",
      display_name: "Export Data",
      description: "Export system data and reports",
      group: "Reporting"
    }
  ];

  const roles = rolesData?.data || [];

  // Handle different possible API response structures and fallbacks
  let permissions = [];
  let isUsingFallbackData = false;

  if (permissionsData?.data && Array.isArray(permissionsData.data) && permissionsData.data.length > 0) {
    // API returned valid data
    permissions = permissionsData.data;
  } else if (permissionsData && Array.isArray(permissionsData) && permissionsData.length > 0) {
    // API returned data directly as array
    permissions = permissionsData;
  } else if (!permissionsLoading) {
    // If not loading, use default permissions (API might not be implemented yet)
    permissions = defaultPermissions;
    isUsingFallbackData = true;
  }

  const isLoading = rolesLoading || permissionsLoading;

  // Check for errors
  const hasError = rolesData?.error || permissionsData?.error;



  // Handle opening the add role dialog
  const handleAddRole = () => {
    setIsEditMode(false);
    setNewRole({
      name: "",
      display_name: "",
      description: "",
      permissions: [],
    });
    setIsAddDialogOpen(true);
  };

  // Handle opening the edit role dialog
  const handleEditRole = (role) => {
    setIsEditMode(true);
    setNewRole({
      name: role.name,
      display_name: role.display_name,
      description: role.description,
      permissions: role.permissions?.map(p => p.id) || [],
    });
    setCurrentRole(role);
    setIsAddDialogOpen(true);
  };

  // Handle viewing role details
  const handleViewRole = (role) => {
    setCurrentRole(role);
    setIsViewDialogOpen(true);
  };

  // Handle saving a new or edited role
  const handleSaveRole = async () => {
    try {
      if (isEditMode) {
        await updateRoleMutation.mutateAsync({
          id: currentRole.id,
          data: newRole,
        });
        toast({
          title: "Success",
          description: "Role updated successfully.",
          variant: "success",
        });
      } else {
        await createRoleMutation.mutateAsync(newRole);
        toast({
          title: "Success",
          description: "Role created successfully.",
          variant: "success",
        });
      }
      setIsAddDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Something went wrong.",
        variant: "destructive",
      });
    }
  };

  // Handle opening the delete confirmation dialog
  const handleDeleteClick = (role) => {
    setCurrentRole(role);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming role deletion
  const handleDeleteConfirm = async () => {
    try {
      await deleteRoleMutation.mutateAsync(currentRole.id);
      toast({
        title: "Success",
        description: "Role deleted successfully.",
        variant: "success",
      });
      setIsDeleteDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete role.",
        variant: "destructive",
      });
    }
  };

  // Handle permission selection
  const handlePermissionChange = (permissionId, checked) => {
    if (checked) {
      setNewRole({
        ...newRole,
        permissions: [...newRole.permissions, permissionId],
      });
    } else {
      setNewRole({
        ...newRole,
        permissions: newRole.permissions.filter(id => id !== permissionId),
      });
    }
  };

  // Early returns for loading and error states
  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (hasError) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-destructive mb-2">Failed to load data</p>
            <p className="text-sm text-muted-foreground">
              {hasError?.message || "Please try refreshing the page"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group permissions by group for better UI - with defensive programming
  const groupedPermissions = Array.isArray(permissions)
    ? permissions.reduce((acc, permission) => {
        const group = permission?.group || "Other";
        if (!acc[group]) {
          acc[group] = [];
        }
        acc[group].push(permission);
        return acc;
      }, {})
    : {};



  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold">Roles Management</CardTitle>
          <Button onClick={handleAddRole}>
            <Plus className="h-4 w-4 mr-2" /> Add Role
          </Button>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Display Name</TableHead>
                  <TableHead className="hidden md:table-cell">Description</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(roles) && roles.length > 0 ? (
                  roles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-mono text-sm">
                        {role.name}
                      </TableCell>
                      <TableCell className="font-medium">
                        {role.display_name}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {role.description}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {role.permissions?.length || 0} permissions
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewRole(role)}
                          >
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditRole(role)}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteClick(role)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <p className="text-muted-foreground mb-2">No roles found</p>
                        <Button variant="outline" onClick={handleAddRole}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create your first role
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Role Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit Role" : "Add New Role"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the role details and permissions below."
                : "Fill in the details to create a new role."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Role Name</Label>
              <Input
                id="name"
                value={newRole.name}
                onChange={(e) =>
                  setNewRole({ ...newRole, name: e.target.value })
                }
                placeholder="e.g., admin"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="display_name">Display Name</Label>
              <Input
                id="display_name"
                value={newRole.display_name}
                onChange={(e) =>
                  setNewRole({ ...newRole, display_name: e.target.value })
                }
                placeholder="e.g., Administrator"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newRole.description}
                onChange={(e) =>
                  setNewRole({ ...newRole, description: e.target.value })
                }
                placeholder="Describe what this role can do"
                rows={3}
              />
            </div>
            <div className="grid gap-2">
              <Label>Permissions</Label>
              {isUsingFallbackData && (
                <div className="text-xs text-amber-600 bg-amber-50 border border-amber-200 rounded-md p-2 mb-2">
                  ⚠️ Using default permissions (API endpoint may not be available)
                </div>
              )}
              <div className="space-y-4 max-h-60 overflow-y-auto border rounded-md p-4">
                {permissionsLoading ? (
                  <div className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-muted-foreground text-sm">Loading permissions...</p>
                  </div>
                ) : Object.keys(groupedPermissions).length > 0 ? (
                  Object.entries(groupedPermissions).map(([group, groupPermissions]) => (
                    <div key={group}>
                      <h4 className="font-medium text-sm mb-2">{group}</h4>
                      <div className="space-y-2 ml-4">
                        {Array.isArray(groupPermissions) && groupPermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`permission-${permission.id}`}
                              checked={newRole.permissions.includes(permission.id)}
                              onCheckedChange={(checked) =>
                                handlePermissionChange(permission.id, checked)
                              }
                            />
                            <Label
                              htmlFor={`permission-${permission.id}`}
                              className="text-sm font-normal"
                            >
                              {permission.display_name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground text-sm mb-2">
                      No permissions available.
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {permissionsData?.error
                        ? "Failed to load permissions. Please try refreshing the page."
                        : "Please create permissions first using the Permissions tab."
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              onClick={handleSaveRole}
              disabled={
                !newRole.name ||
                !newRole.display_name ||
                createRoleMutation.isPending ||
                updateRoleMutation.isPending
              }
            >
              {createRoleMutation.isPending || updateRoleMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              {isEditMode ? "Update Role" : "Add Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Role Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Role Details</DialogTitle>
            <DialogDescription>
              View the details and permissions for this role.
            </DialogDescription>
          </DialogHeader>
          {currentRole && (
            <div className="grid gap-4 py-4">
              <div>
                <Label className="text-sm font-medium">Role Name</Label>
                <p className="text-sm text-muted-foreground font-mono">{currentRole.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Display Name</Label>
                <p className="text-sm text-muted-foreground">{currentRole.display_name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Description</Label>
                <p className="text-sm text-muted-foreground">{currentRole.description}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Permissions ({currentRole.permissions?.length || 0})</Label>
                <div className="mt-2 space-y-1 max-h-40 overflow-y-auto">
                  {currentRole.permissions?.map((permission) => (
                    <Badge key={permission.id} variant="outline" className="mr-1 mb-1">
                      {permission.display_name}
                    </Badge>
                  )) || <p className="text-sm text-muted-foreground">No permissions assigned</p>}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Close</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the role "{currentRole?.display_name}".
              This action cannot be undone and may affect users assigned to this role.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground"
              disabled={deleteRoleMutation.isPending}
            >
              {deleteRoleMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
