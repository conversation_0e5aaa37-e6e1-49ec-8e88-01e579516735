import { useState } from "react";
import {
  ChevronDown,
  ChevronUp,
  Download,
  FileText,
  Filter,
  X,
  Calendar,
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { toast } from "@/components/ui/use-toast";
import { useCSVExport, useExcelExport, usePDFExport } from "@/hooks/admin/useExportSimple";

export default function AllTransactionsPage() {
  // Export hooks
  const exportToCSV = useCSVExport();
  const exportToExcel = useExcelExport();
  const exportToPDF = usePDFExport();

  // State for expanded rows
  const [expandedRows, setExpandedRows] = useState({});

  // State for filter drawer
  const [showFilters, setShowFilters] = useState(false);

  // State for export modal
  const [showExportModal, setShowExportModal] = useState(false);

  // State for export format
  const [exportFormat, setExportFormat] = useState("csv");

  // State for filters
  const [filters, setFilters] = useState({
    status: "",
    startDate: null,
    endDate: null,
    userRole: "",
    minAmount: "",
    maxAmount: "",
  });

  // Sample transaction data
  const transactions = [
    {
      id: "TRX-001",
      specification: "Office Furniture Set",
      amount: 5000,
      userRole: "Customer",
      poNumber: "PO-2023-001",
      status: "Success",
      date: "2023-10-15",
      details: {
        specificationUrl: "/documents/spec-001.pdf",
        items: [
          { name: "Executive Desk", price: 2000 },
          { name: "Office Chair", price: 1500 },
          { name: "Bookshelf", price: 1500 },
        ],
        vendor: "Office Solutions Inc.",
        deliveryTime: "2 weeks",
      },
    },
    {
      id: "TRX-002",
      specification: "IT Equipment",
      amount: 12000,
      userRole: "Vendor",
      poNumber: "PO-2023-002",
      status: "Failed",
      date: "2023-10-20",
      details: {
        specificationUrl: "/documents/spec-002.pdf",
        items: [
          { name: "Laptops (5x)", price: 7500 },
          { name: "Monitors (5x)", price: 2500 },
          { name: "Peripherals", price: 2000 },
        ],
        vendor: "Tech Supplies Co.",
        deliveryTime: "1 week",
      },
    },
    {
      id: "TRX-003",
      specification: "Conference Room Setup",
      amount: 8500,
      userRole: "Customer",
      poNumber: "PO-2023-003",
      status: "Success",
      date: "2023-11-05",
      details: {
        specificationUrl: "/documents/spec-003.pdf",
        items: [
          { name: "Conference Table", price: 3000 },
          { name: "Chairs (10x)", price: 3500 },
          { name: "Projector System", price: 2000 },
        ],
        vendor: "Office Solutions Inc.",
        deliveryTime: "3 weeks",
      },
    },
    {
      id: "TRX-004",
      specification: "Software Licenses",
      amount: 4500,
      userRole: "Vendor",
      poNumber: "PO-2023-004",
      status: "Success",
      date: "2023-11-10",
      details: {
        specificationUrl: "/documents/spec-004.pdf",
        items: [
          { name: "Design Software (3x)", price: 2700 },
          { name: "Project Management Tool", price: 1800 },
        ],
        vendor: "Software Solutions Ltd.",
        deliveryTime: "Immediate",
      },
    },
    {
      id: "TRX-005",
      specification: "Office Renovation",
      amount: 25000,
      userRole: "Customer",
      poNumber: "PO-2023-005",
      status: "Failed",
      date: "2023-11-15",
      details: {
        specificationUrl: "/documents/spec-005.pdf",
        items: [
          { name: "Flooring", price: 10000 },
          { name: "Painting", price: 5000 },
          { name: "Electrical Work", price: 7000 },
          { name: "Miscellaneous", price: 3000 },
        ],
        vendor: "Construction Experts Inc.",
        deliveryTime: "1 month",
      },
    },
  ];

  // Toggle row expansion
  const toggleRowExpansion = (id) => {
    setExpandedRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Apply filters to transactions
  const filteredTransactions = transactions.filter((transaction) => {
    // Filter by status
    if (filters.status && transaction.status !== filters.status) {
      return false;
    }

    // Filter by user role
    if (filters.userRole && transaction.userRole !== filters.userRole) {
      return false;
    }

    // Filter by amount range
    if (
      filters.minAmount &&
      transaction.amount < Number.parseFloat(filters.minAmount)
    ) {
      return false;
    }

    if (
      filters.maxAmount &&
      transaction.amount > Number.parseFloat(filters.maxAmount)
    ) {
      return false;
    }

    // Filter by date range
    if (filters.startDate) {
      const transactionDate = new Date(transaction.date);
      if (transactionDate < filters.startDate) {
        return false;
      }
    }

    if (filters.endDate) {
      const transactionDate = new Date(transaction.date);
      if (transactionDate > filters.endDate) {
        return false;
      }
    }

    return true;
  });

  // Handle export
  const handleExport = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) {
      toast({
        title: "No Data to Export",
        description: "Please ensure there are transactions to export.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Export Started",
      description: `Exporting ${filteredTransactions.length} transactions as ${exportFormat.toUpperCase()}`,
    });

    let success = false;
    const fileName = `transactions_${new Date().toISOString().split('T')[0]}`;

    try {
      switch (exportFormat) {
        case "csv":
          success = exportToCSV(filteredTransactions, fileName);
          break;
        case "excel":
          success = exportToExcel(filteredTransactions, fileName);
          break;
        case "pdf":
          success = exportToPDF(filteredTransactions, fileName);
          break;
        default:
          success = exportToCSV(filteredTransactions, fileName);
      }

      if (success) {
        toast({
          title: "Export Complete",
          description: `Your ${exportFormat.toUpperCase()} file has been downloaded successfully.`,
        });
        setShowExportModal(false);
      } else {
        toast({
          title: "Export Failed",
          description: "There was an error exporting your data. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your data. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">All Transactions</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
          <Button onClick={() => setShowExportModal(true)}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filter Section */}
      {showFilters && (
        <div className="border rounded-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Filters</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowFilters(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="Success">Success</SelectItem>
                  <SelectItem value="Failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="userRole">User Role</Label>
              <Select
                value={filters.userRole}
                onValueChange={(value) => handleFilterChange("userRole", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="Customer">Customer</SelectItem>
                  <SelectItem value="Vendor">Vendor</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.startDate
                      ? format(filters.startDate, "PPP")
                      : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filters.startDate}
                    onSelect={(date) => handleFilterChange("startDate", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label>End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.endDate
                      ? format(filters.endDate, "PPP")
                      : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filters.endDate}
                    onSelect={(date) => handleFilterChange("endDate", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label htmlFor="minAmount">Min Amount ($)</Label>
              <Input
                id="minAmount"
                type="number"
                value={filters.minAmount}
                onChange={(e) => handleFilterChange("minAmount", e.target.value)}
                placeholder="0"
              />
            </div>

            <div>
              <Label htmlFor="maxAmount">Max Amount ($)</Label>
              <Input
                id="maxAmount"
                type="number"
                value={filters.maxAmount}
                onChange={(e) => handleFilterChange("maxAmount", e.target.value)}
                placeholder="100000"
              />
            </div>
          </div>

          <div className="flex justify-end mt-4 space-x-2">
            <Button
              variant="outline"
              onClick={() =>
                setFilters({
                  status: "",
                  startDate: null,
                  endDate: null,
                  userRole: "",
                  minAmount: "",
                  maxAmount: "",
                })
              }
            >
              Reset
            </Button>
            <Button onClick={() => setShowFilters(false)}>Apply Filters</Button>
          </div>
        </div>
      )}

      {/* Transactions Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Specification</TableHead>
              <TableHead>Quotation Amount</TableHead>
              <TableHead>User Role</TableHead>
              <TableHead>PO Number</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTransactions.length > 0 ? (
              filteredTransactions.map((transaction) => (
                <>
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">
                      {transaction.id}
                    </TableCell>
                    <TableCell>{transaction.specification}</TableCell>
                    <TableCell>${transaction.amount.toLocaleString()}</TableCell>
                    <TableCell>{transaction.userRole}</TableCell>
                    <TableCell>{transaction.poNumber}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          transaction.status === "Success"
                            ? "success"
                            : "destructive"
                        }
                      >
                        {transaction.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleRowExpansion(transaction.id)}
                      >
                        {expandedRows[transaction.id] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                  {expandedRows[transaction.id] && (
                    <TableRow>
                      <TableCell colSpan={8}>
                        <div className="p-4 bg-muted/50 rounded-md">
                          <div className="flex justify-between mb-4">
                            <div>
                              <h4 className="font-semibold mb-2">
                                Specification Details
                              </h4>
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2" />
                                <a
                                  href={transaction.details.specificationUrl}
                                  className="text-primary hover:underline"
                                >
                                  View Specification PDF
                                </a>
                              </div>
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">Vendor</h4>
                              <p>{transaction.details.vendor}</p>
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">
                                Delivery Time
                              </h4>
                              <p>{transaction.details.deliveryTime}</p>
                            </div>
                          </div>

                          <h4 className="font-semibold mb-2">
                            Quotation Breakdown
                          </h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Item</TableHead>
                                <TableHead className="text-right">
                                  Price
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {transaction.details.items.map((item, index) => (
                                <TableRow key={index}>
                                  <TableCell>{item.name}</TableCell>
                                  <TableCell className="text-right">
                                    ${item.price.toLocaleString()}
                                  </TableCell>
                                </TableRow>
                              ))}
                              <TableRow>
                                <TableCell className="font-bold">Total</TableCell>
                                <TableCell className="text-right font-bold">
                                  ${transaction.amount.toLocaleString()}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={8}
                  className="h-24 text-center"
                >
                  No transactions found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Export Modal */}
      <Dialog open={showExportModal} onOpenChange={setShowExportModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Reports</DialogTitle>
            <DialogDescription>
              Choose a format to export your transaction data.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <RadioGroup
              value={exportFormat}
              onValueChange={setExportFormat}
              className="flex flex-col space-y-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv">CSV (Comma Separated Values)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pdf" id="pdf" />
                <Label htmlFor="pdf">PDF Document</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="excel" id="excel" />
                <Label htmlFor="excel">Excel Spreadsheet</Label>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
