import { apiRequest } from "@/utils/apiRequest";
import { getAccessTokenByUserType } from "@/utils/setCookies";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Notice CRUD operations
export const getNotices = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/notice`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createNotice = async (formData) => {
  const accessToken = getAccessTokenByUserType("admin");

  const response = await apiRequest({
    url: `${BASE_URL}/admin/notice`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: formData,
    isFormData: true,
  });
  return response;
};

export const updateNotice = async (id, formData) => {
  const accessToken = getAccessTokenByUserType("admin");

  const response = await apiRequest({
    url: `${BASE_URL}/admin/notice/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: formData,
    isFormData: true,
  });
  return response;
};

export const deleteNotice = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/notice/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

// Training CRUD operations
export const getTrainingSessions = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/training`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createTrainingSession = async (trainingData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/training`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: trainingData,
  });
  return response;
};

export const updateTrainingSession = async (id, trainingData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/training/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: trainingData,
  });
  return response;
};

export const deleteTrainingSession = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/training/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getTrainingSessionById = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/training/${id}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

// Permission CRUD operations
export const getPermissions = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/permissions`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createPermission = async (permissionData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/permissions`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: permissionData,
  });
  return response;
};

export const updatePermission = async (id, permissionData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/permissions/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: permissionData,
  });
  return response;
};

export const deletePermission = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/permissions/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

// Role CRUD operations
export const getRoles = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/roles`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createRole = async (roleData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/roles`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: roleData,
  });
  return response;
};

export const updateRole = async (id, roleData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/roles/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: roleData,
  });
  return response;
};

export const deleteRole = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/roles/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

//Category CRUD operations
export const getCategories = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createCategory = async (categoryData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: categoryData,
  });
  return response;
};

export const updateCategory = async (id, categoryData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: categoryData,
  });
  return response;
};

export const deleteCategory = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

// Sub-category CRUD operations
export const getSubCategories = async () => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const createSubCategory = async (subCategoryData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: subCategoryData, // This should include parent_id
  });
  return response;
};

export const updateSubCategory = async (id, subCategoryData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: subCategoryData, // This should include parent_id
  });
  return response;
};

export const deleteSubCategory = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/business-category/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

// Specification CRUD operations
export const getAdminSpecifications = async (page = 1, perPage = 15) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/specifications?page=${page}&per_page=${perPage}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const updateSpecificationStatus = async (id, statusData) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/specifications/${id}/status`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: statusData,
  });
  return response;
};

export const getAdminSpecificationById = async (id) => {
  const accessToken = getAccessTokenByUserType("admin");
  const response = await apiRequest({
    url: `${BASE_URL}/admin/specifications/${id}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};
