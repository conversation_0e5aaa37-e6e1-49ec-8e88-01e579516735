import { useState } from "react";

const EditableTemplate = ({ data, onSave }) => {
    const [editedData, setEditedData] = useState(data.specificationProductData || []);

    const handleChange = (index, field, value) => {
        const newData = [...editedData];
        newData[index][field] = value;
        setEditedData(newData);
    };

    const handleSave = () => {
        // Preserve complete data structure when saving
        const completeData = {
            ...data,
            specificationProductData: editedData
        };
        onSave(completeData);
    };

    return (
        <div className="w-full p-4">
            <table className='w-full mt-4 border-collapse'>
                <thead>
                    <tr>
                        <th className="p-2 border border-gray-300 text-left">S.N.</th>
                        <th className="p-2 border border-gray-300 text-left">Item Name</th>
                        <th className="p-2 border border-gray-300 text-left">Quantity</th>
                        <th className="p-2 border border-gray-300 text-left">Unit</th>
                        <th className="p-2 border border-gray-300 text-left">
                            Attributes
                            <div className="text-xs text-gray-500 font-normal">(comma separated)</div>
                        </th>
                        <th className="p-2 border border-gray-300 text-left">Other</th>
                    </tr>
                </thead>
                <tbody className="w-full">
                    {editedData.map((item, index) => (
                        <tr key={index} className="w-full">
                            <td className="p-2 border border-gray-300">{index + 1}</td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    value={item.itemName}
                                    onChange={(e) => handleChange(index, 'itemName', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="number"
                                    value={item.quantity}
                                    onChange={(e) => handleChange(index, 'quantity', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="text"
                                    value={item.unit}
                                    onChange={(e) => handleChange(index, 'unit', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="text"
                                    value={item.attributes}
                                    onChange={(e) => handleChange(index, 'attributes', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="text"
                                    value={item.other}
                                    onChange={(e) => handleChange(index, 'other', e.target.value)}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <div className="w-full flex justify-end mt-4">
                <button
                    onClick={handleSave}
                    className='w-[100px] font-bold tracking-wider flex items-center justify-center border-2 border-primary bg-primary text-black rounded-lg px-6 py-3'
                >
                    Save
                </button>
            </div>
        </div>
    );
};

export default EditableTemplate