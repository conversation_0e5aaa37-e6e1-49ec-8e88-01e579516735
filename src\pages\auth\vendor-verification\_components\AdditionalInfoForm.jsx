import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/StoreHooks";
import { setSetup } from "@/redux/slice/auth/authSlice";
import { useForm } from "react-hook-form";
import { Lu<PERSON>rrowLeft, LuArrowRight } from "react-icons/lu";
import useSingleFileUpload from "@/hooks/useSingleFileUpload";
import { LuPaperclip } from "react-icons/lu";

const BusinessInfoForm = () => {
  const { setup } = useAppSelector((state) => state.auth || {});
  const dispatch = useAppDispatch();

  const { handleSubmit } = useForm({
    defaultValues: {
      taxClearance: setup?.taxClearance || "",
      logo: setup?.logo || "",
      additionalDocument: setup?.additionalDocument || "",
      companyStamp: setup?.companyStamp || "",
      authorizedSignature: setup?.authorizedSignature || "",
    },
  });

  // Using custom hooks for file upload (separate for each field)
  const {
    fileInputRef: taxClearFileInputRef,
    uploadedFile: taxClearUploadedFile,
    openFileDialog: openTaxClearFileDialog,
    handleFilesSelected: handleTaxClearFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: logoFileInputRef,
    uploadedFile: logoUploadedFile,
    openFileDialog: openLogoFileDialog,
    handleFilesSelected: handleLogoFilesSelected,
  } = useSingleFileUpload([".png", ".jpg", ".jpeg"], false);

  const {
    fileInputRef: additionalDocFileInputRef,
    uploadedFile: additionalDocUploadedFile,
    openFileDialog: openAdditionalDocFileDialog,
    handleFilesSelected: handleAdditionalDocFilesSelected,
  } = useSingleFileUpload([".pdf"], false);

  const {
    fileInputRef: companyStampFileInputRef,
    uploadedFile: companyStampUploadedFile,
    openFileDialog: openCompanyStampFileDialog,
    handleFilesSelected: handleCompanyStampFilesSelected,
  } = useSingleFileUpload([".png", ".jpg", ".jpeg"], false);

  const handleBack = () => {
    dispatch(
      setSetup({
        ...setup,
        step: 3,
      })
    );
  };

  const onSubmit = (data) => {
    console.log(data);
    console.log("Tax Clearance File:", taxClearUploadedFile);
    console.log("Logo File:", logoUploadedFile);
    console.log("Additional Document File:", additionalDocUploadedFile);
    console.log("Company Stamp File:", companyStampUploadedFile);
    dispatch(
      setSetup({
        ...setup,
        taxClearance: taxClearUploadedFile?.name,
        logo: logoUploadedFile?.name,
        additionalDocument: additionalDocUploadedFile?.name,
        companyStamp: companyStampUploadedFile?.name,
        authorizedSignature: data?.authorizedSignature,
        step: 5,
      })
    );
  };

  return (
    <div className="max-w-[40rem] sm:min-w-[40rem]">
      <div className="flex flex-col">
        <h2 className="font-nunito text-bodyLarge sm:text-[1.8rem] text-center font-bold">{`Vendor Verification`}</h2>
        <span className="font-nunito text-center">{`Business Details`}</span>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-10">
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="taxClearance"
            >
              Tax Clearance Certificate
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openTaxClearFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      taxClearUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {taxClearUploadedFile?.name
                      ? taxClearUploadedFile?.name
                      : "Upload tax clearance"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={taxClearFileInputRef}
                className="hidden"
                onChange={(e) => handleTaxClearFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="logo"
            >
              Logo
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openLogoFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      logoUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {logoUploadedFile?.name
                      ? logoUploadedFile?.name
                      : "Upload logo"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={logoFileInputRef}
                className="hidden"
                onChange={(e) => handleLogoFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="companyStamp"
            >
              Company Stamp
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openCompanyStampFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      companyStampUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {companyStampUploadedFile?.name
                      ? companyStampUploadedFile?.name
                      : "Upload company stamp"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={companyStampFileInputRef}
                className="hidden"
                onChange={(e) => handleCompanyStampFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
          <div>
            <label
              className="ml-1 text-bodySmall font-medium font-nunito"
              htmlFor="additionalDocument"
            >
              Additional Document
            </label>
            <div>
              <div className="w-full">
                <Button
                  className={`border border-[#CFCFCF] rounded-lg h-[45px] mt-1 focus:border-[#006AFF] w-full flex items-center justify-start p-2 bg-transparent hover:bg-[#f2f6fd]`}
                  onClick={(e) => openAdditionalDocFileDialog(e)}
                >
                  <LuPaperclip className="mx-2 h-4 w-4 text-lightGray1" />
                  <span
                    className={`text-xs font-nunito text-[14px] leading-5 tracking-wide ${
                      additionalDocUploadedFile?.name
                        ? "text-darkNavy"
                        : "text-lightGray1"
                    }`}
                  >
                    {additionalDocUploadedFile?.name
                      ? additionalDocUploadedFile?.name
                      : "Upload additional document"}
                  </span>
                </Button>
              </div>
              <input
                type="file"
                ref={additionalDocFileInputRef}
                className="hidden"
                onChange={(e) => handleAdditionalDocFilesSelected(e)}
                accept=".pdf"
                multiple={false}
              />
            </div>
          </div>
        </div>
        <div className="float-right mt-5 flex flex-col xs:flex-row items-center gap-4">
          <Button
            variant="outline"
            className="font-nunito flex items-center text-bodyMedium font-extrabold hover:bg-green1 text-darkNavy h-[3rem] px-10 sm:px-8 rounded-xl tracking-wider"
            onClick={handleBack}
          >
            <LuArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] px-6 rounded-xl tracking-wider"
            type="submit"
          >
            Next Step
            <LuArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BusinessInfoForm;
