import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { LuArrowDownRight, LuArrowUpRight } from "react-icons/lu"; // Importing Up arrow as well for "Up" condition

// eslint-disable-next-line react/prop-types
export function DataCard({ title, content, percentage, trend, image }) {
  const isUp = trend === "Up"; // Check if the trend is "Up"

  return (
    <Card className="p-2 font-nunito rounded-2xl">
      <CardHeader className="flex flex-row p-1 w-full justify-between">
        <div>
          <CardTitle className="text-[0.8rem] text-darkTeal font-semibold">
            {title}
          </CardTitle>
          <CardDescription className="text-[1.75rem] mt-1 text-black font-bold">
            {content}
          </CardDescription>
        </div>
        <img
          className="w-12 h-12"
          src={image}
          alt="card-image"
          loading="lazy"
        />
      </CardHeader>
      <CardContent className="flex gap-2 items-center text-[0.8rem] p-1 mt-2">
        {isUp ? (
          <LuArrowUpRight size={25} className="text-green-500" />
        ) : (
          <LuArrowDownRight size={25} className="text-red-500" />
        )}
        <h1>
          <span className={isUp ? "text-green-500 mr-1" : "text-red-500 mr-1"}>
            {percentage}%
          </span>
          <span className="mr-1">{trend}</span>
          from past week
        </h1>
      </CardContent>
    </Card>
  );
}
