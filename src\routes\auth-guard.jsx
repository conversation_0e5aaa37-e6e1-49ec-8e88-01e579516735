import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import {
  getAuthenticationStatusForUserType,
  getUserTypeFromRoute,
  isAuthRoute,
  isSignupRoute,
  isPasswordResetRoute
} from "@/utils/authUtils";

/**
 * AuthGuard component to prevent authenticated users from accessing signin pages
 * and redirect them to their appropriate dashboard
 */
const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    const currentPath = location.pathname;

    // Only check authentication for auth routes
    if (!isAuthRoute(currentPath)) {
      return;
    }

    // Determine which user type this auth route is for
    const expectedUserType = getUserTypeFromRoute(currentPath);

    // If we can't determine user type from route, skip auth guard
    if (!expectedUserType) {
      return;
    }

    // Check if user is authenticated for ANY user type
    const allUserTypes = ["user", "vendor", "admin"];
    let authenticatedUserType = null;
    let authenticatedAuthStatus = null;

    for (const userType of allUserTypes) {
      const authStatus = getAuthenticationStatusForUserType(userType);
      if (authStatus.isAuthenticated) {
        authenticatedUserType = userType;
        authenticatedAuthStatus = authStatus;
        break;
      }
    }

    // Get authentication status for the specific expected user type
    const expectedAuthStatus = getAuthenticationStatusForUserType(expectedUserType);

    // If user is authenticated for this specific user type and trying to access auth routes
    if (expectedAuthStatus.isAuthenticated) {
      // Allow access to password reset routes even if authenticated
      if (isPasswordResetRoute(currentPath)) {
        return;
      }

      // Allow access to signup routes (user might want to create additional accounts)
      if (isSignupRoute(currentPath)) {
        return;
      }

      // Show toast notification
      toast({
        title: "Already Signed In",
        description: `You are already signed in as ${expectedUserType === 'user' ? 'customer' : expectedUserType}. Redirecting to your dashboard.`,
        variant: "default",
        duration: 3000,
      });

      // Redirect authenticated users away from signin pages
      navigate(expectedAuthStatus.dashboardPath, {
        replace: true,
        state: {
          message: "You are already signed in.",
          redirectedFrom: currentPath
        }
      });
    }
    // If user is authenticated as a different user type and trying to access auth routes
    else if (authenticatedUserType && authenticatedUserType !== expectedUserType) {
      // Show informative toast
      toast({
        title: "Different User Type",
        description: `You are already signed in as ${authenticatedUserType === 'user' ? 'customer' : authenticatedUserType}. Redirecting to your dashboard.`,
        variant: "default",
        duration: 3000,
      });

      // Redirect to their correct dashboard
      navigate(authenticatedAuthStatus.dashboardPath, {
        replace: true,
        state: {
          message: "You are already signed in as a different user type.",
          redirectedFrom: currentPath
        }
      });
    }
  }, [location.pathname, navigate, toast]);

  return <>{children}</>;
};

export default AuthGuard;
