import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Eye, Calendar, Package, User } from "lucide-react";
import { useState } from "react";
import SpecificationDetailsModal from "./SpecificationDetailsModal";
import { QuotationFormModal } from "@/pages/vendor/quotation/_components/QuotationFormModal";

const SpecificationFeedCard = ({ specification }) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showQuotationModal, setShowQuotationModal] = useState(false);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "open":
      case "pending":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return "N/A";
    }
  };

  return (
    <>
      <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={specification.customer_avatar} alt={specification.customer_name} />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
                  {specification.title}
                </h3>
                <p className="text-sm text-gray-600">
                  by {specification.customer_name}
                </p>
              </div>
            </div>
            <Badge className={getStatusColor(specification.status)}>
              {specification.status || "pending"}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Description */}
          {specification.description && (
            <p className="text-gray-700 line-clamp-2">
              {specification.description}
            </p>
          )}

          {/* Category and Subcategory */}
          <div className="flex flex-wrap gap-2">
            {specification.category && (
              <Badge variant="outline" className="text-xs">
                {typeof specification.category === "object" ? specification.category.name : specification.category}
              </Badge>
            )}
            {specification.subCategory && (
              <Badge variant="outline" className="text-xs">
                {specification.subCategory}
              </Badge>
            )}
          </div>

          {/* Metadata */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(specification.createdAt)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Package className="h-4 w-4" />
                <span>{specification.items_count || 0} items</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 pt-2 border-t border-gray-100">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetailsModal(true)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              View Details
            </Button>
            <Button
              size="sm"
              className="flex items-center gap-2"
              onClick={() => setShowQuotationModal(true)}
            >
              Send Quote
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Specification Details Modal */}
      <SpecificationDetailsModal
        specification={specification}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
      />

      {/* Quotation Form Modal */}
      <QuotationFormModal
        specification={specification}
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
      />
    </>
  );
};

export default SpecificationFeedCard;
