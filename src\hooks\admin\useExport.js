import { useCallback } from 'react'
import * as XLSX from 'xlsx'
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const formatCurrency = (amount) => {
    return `Rs ${new Intl.NumberFormat('en-US').format(amount)}`
}

const formatDate = (dateString) => {
    try {
        const date = new Date(dateString)
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    } catch (error) {
        return dateString
    }
}

const prepareDataForExport = (transactions) => {
    if (!Array.isArray(transactions)) {
        console.error('Transactions must be an array')
        return []
    }

    return transactions.map(transaction => {
        return {
            'Transaction ID': transaction.id || '',
            'Specification': transaction.specification || '',
            'Amount': transaction.amount ? formatCurrency(transaction.amount) : 'Rs 0',
            'User Role': transaction.userRole || '',
            'PO Number': transaction.poNumber || '',
            'Status': transaction.status || '',
            'Date': transaction.date ? formatDate(transaction.date) : '',
            'Vendor': transaction.details?.vendor || '',
            'Delivery Time': transaction.details?.deliveryTime || '',
        }
    })
}

export function useCSVExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        if (!transactions || transactions.length === 0) {
            console.error('No data to export')
            return false
        }

        try {
            const data = prepareDataForExport(transactions)
            const headers = Object.keys(data[0])

            // Create CSV content with BOM for proper Excel compatibility
            let csvContent = '\uFEFF' + headers.join(',') + '\n'

            data.forEach(row => {
                const values = headers.map(header => {
                    let value = row[header] || ''
                    // Handle special characters and ensure proper CSV formatting
                    value = String(value).replace(/"/g, '""')
                    // Wrap in quotes if contains comma, newline, or quote
                    if (value.includes(',') || value.includes('\n') || value.includes('"')) {
                        return `"${value}"`
                    }
                    return value
                })
                csvContent += values.join(',') + '\n'
            })

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
            const link = document.createElement('a')
            const url = URL.createObjectURL(blob)

            link.setAttribute('href', url)
            link.setAttribute('download', `${fileName}.csv`)
            link.style.visibility = 'hidden'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)

            // Clean up
            URL.revokeObjectURL(url)

            return true
        } catch (error) {
            console.error('Error exporting to CSV:', error)
            return false
        }
    }, [])
}

export function useExcelExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        if (!transactions || transactions.length === 0) {
            console.error('No data to export')
            return false
        }

        try {
            const data = prepareDataForExport(transactions)
            if (data.length === 0) {
                console.error('No data prepared for export')
                return false
            }

            const worksheet = XLSX.utils.json_to_sheet(data)
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Transactions')

            // Set specific column widths for better display
            const columnWidths = [
                { wch: 15 }, // Transaction ID
                { wch: 30 }, // Specification
                { wch: 15 }, // Amount
                { wch: 12 }, // User Role
                { wch: 15 }, // PO Number
                { wch: 10 }, // Status
                { wch: 15 }, // Date
                { wch: 25 }, // Vendor
                { wch: 15 }, // Delivery Time
            ]

            worksheet['!cols'] = columnWidths

            XLSX.writeFile(workbook, `${fileName}.xlsx`)
            return true
        } catch (error) {
            console.error('Error exporting to Excel:', error)
            return false
        }
    }, [])
}

export function usePDFExport() {
    return useCallback((transactions, fileName = 'transactions') => {
        if (!transactions || transactions.length === 0) {
            console.error('No data to export')
            return false
        }

        try {
            const data = prepareDataForExport(transactions)
            if (data.length === 0) {
                console.error('No data prepared for export')
                return false
            }

            const doc = new jsPDF('landscape') // Use landscape for better table fit

            // Add title
            doc.setFontSize(16)
            doc.text('Transaction Report', 14, 20)
            doc.setFontSize(10)
            doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 28)

            // Prepare table data
            const headers = [
                'Transaction ID',
                'Specification',
                'Amount',
                'User Role',
                'PO Number',
                'Status',
                'Date',
                'Vendor',
                'Delivery Time'
            ]

            const rows = data.map(row => [
                row['Transaction ID'] || '',
                row['Specification'] || '',
                row['Amount'] || '',
                row['User Role'] || '',
                row['PO Number'] || '',
                row['Status'] || '',
                row['Date'] || '',
                row['Vendor'] || '',
                row['Delivery Time'] || ''
            ])

            // Add table
            doc.autoTable({
                head: [headers],
                body: rows,
                startY: 35,
                styles: {
                    fontSize: 8,
                    cellPadding: 3,
                    overflow: 'linebreak',
                    halign: 'left'
                },
                headStyles: {
                    fillColor: [52, 152, 219],
                    textColor: 255,
                    fontSize: 9,
                    fontStyle: 'bold',
                    halign: 'center'
                },
                columnStyles: {
                    0: { cellWidth: 25 }, // Transaction ID
                    1: { cellWidth: 40 }, // Specification
                    2: { cellWidth: 25 }, // Amount
                    3: { cellWidth: 20 }, // User Role
                    4: { cellWidth: 25 }, // PO Number
                    5: { cellWidth: 18 }, // Status
                    6: { cellWidth: 25 }, // Date
                    7: { cellWidth: 35 }, // Vendor
                    8: { cellWidth: 25 }, // Delivery Time
                },
                margin: { top: 35, left: 14, right: 14 },
                didDrawPage: function(pageData) {
                    // Add page number
                    const pageCount = doc.internal.getNumberOfPages()
                    doc.setFontSize(8)
                    doc.text(
                        `Page ${pageData.pageNumber} of ${pageCount}`,
                        pageData.settings.margin.left,
                        doc.internal.pageSize.height - 10
                    )
                }
            })

            doc.save(`${fileName}.pdf`)
            return true
        } catch (error) {
            console.error('Error exporting to PDF:', error)
            console.error('Error details:', error.message)
            return false
        }
    }, [])
}
