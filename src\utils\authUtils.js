import Cookies from "js-cookie";
import {
  decryptData,
  getCookiesByUserType,
  getUserIdByUserType,
  getAccessTokenByUserType,
  clearCookiesByUserType,
  getAuthenticatedUserTypes
} from "./setCookies";

/**
 * Check if user is authenticated by verifying cookies and Redux state
 * @param {Object} reduxUser - User object from Redux state
 * @param {string} forUserType - Specific user type to check (optional)
 * @returns {Object} Authentication status and user info
 */
export const getAuthenticationStatus = (reduxUser = null, forUserType = null) => {
  try {
    // If specific user type is requested, check only that type
    if (forUserType) {
      return getAuthenticationStatusForUserType(forUserType, reduxUser);
    }

    // Legacy behavior: check old cookie format first for backward compatibility
    const legacyAccessToken = Cookies.get("accessToken");
    const legacyEncryptedUserId = Cookies.get("userId");
    const legacyUserType = Cookies.get("userType");

    if (legacyAccessToken && legacyEncryptedUserId && legacyUserType) {
      const userId = decryptData(legacyEncryptedUserId);
      if (userId) {
        return {
          isAuthenticated: true,
          userType: legacyUserType,
          userId,
          dashboardPath: getDashboardPath(legacyUserType, userId),
        };
      }
    }

    // Check for any authenticated user type in new format
    const authenticatedUserTypes = getAuthenticatedUserTypes();
    if (authenticatedUserTypes.length > 0) {
      // Return the first authenticated user type found
      const userType = authenticatedUserTypes[0];
      return getAuthenticationStatusForUserType(userType, reduxUser);
    }

    return {
      isAuthenticated: false,
      userType: null,
      userId: null,
      dashboardPath: null,
    };
  } catch (error) {
    console.error("Error checking authentication status:", error);
    return {
      isAuthenticated: false,
      userType: null,
      userId: null,
      dashboardPath: null,
    };
  }
};

/**
 * Check authentication status for a specific user type
 * @param {string} userType - User type to check
 * @param {Object} reduxUser - User object from Redux state
 * @returns {Object} Authentication status and user info
 */
export const getAuthenticationStatusForUserType = (userType, reduxUser = null) => {
  try {
    const cookies = getCookiesByUserType(userType);
    const userId = getUserIdByUserType(userType);

    // If no cookies or decryption fails, user is not authenticated
    if (!cookies.accessToken || !cookies.userId || !cookies.userType || !userId) {
      return {
        isAuthenticated: false,
        userType: null,
        userId: null,
        dashboardPath: null,
      };
    }

    // Double-check with Redux state if available and matches user type
    if (
      reduxUser &&
      reduxUser.isLogin &&
      reduxUser.userId &&
      reduxUser.userType &&
      reduxUser.userType === userType
    ) {
      // Ensure Redux state matches cookies
      if (reduxUser.userId === userId && reduxUser.userType === cookies.userType) {
        return {
          isAuthenticated: true,
          userType: reduxUser.userType,
          userId: reduxUser.userId,
          dashboardPath: getDashboardPath(reduxUser.userType, reduxUser.userId),
        };
      }
    }

    // If Redux state doesn't match or isn't available, rely on cookies
    return {
      isAuthenticated: true,
      userType: cookies.userType,
      userId,
      dashboardPath: getDashboardPath(cookies.userType, userId),
    };
  } catch (error) {
    console.error(`Error checking authentication status for ${userType}:`, error);
    return {
      isAuthenticated: false,
      userType: null,
      userId: null,
      dashboardPath: null,
    };
  }
};

/**
 * Get the appropriate dashboard path based on user type and ID
 * @param {string} userType - Type of user (customer, vendor, admin)
 * @param {string} userId - User ID
 * @returns {string} Dashboard path
 */
export const getDashboardPath = (userType, userId) => {
  switch (userType) {
    case "user":
      return `/customer/${userId}/dashboard`;
    case "vendor":
      return `/vendor/${userId}/dashboard`;
    case "admin":
      return `/admin/${userId}/dashboard`;
    default:
      return "/";
  }
};

/**
 * Check if the current path is an authentication route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's an auth route
 */
export const isAuthRoute = (pathname) => {
  const authRoutes = [
    "/customer/signin",
    "/vendor/signin",
    "/admin/signin",
    "/customer/signup",
    "/vendor/signup",
    "/customer/forgot-password",
    "/vendor/forgot-password",
    "/admin/forgot-password",
    "/customer/reset-password",
    "/vendor/reset-password",
    "/admin/reset-password",
  ];

  return authRoutes.includes(pathname);
};

/**
 * Check if the current path is a signup route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's a signup route
 */
export const isSignupRoute = (pathname) => {
  const signupRoutes = ["/customer/signup", "/vendor/signup"];

  return signupRoutes.includes(pathname);
};

/**
 * Check if the current path is a password reset route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's a password reset route
 */
export const isPasswordResetRoute = (pathname) => {
  const resetRoutes = [
    "/customer/forgot-password",
    "/vendor/forgot-password",
    "/admin/forgot-password",
    "/customer/reset-password",
    "/vendor/reset-password",
    "/admin/reset-password",
  ];

  return resetRoutes.includes(pathname);
};

/**
 * Get the expected user type based on the current route
 * @param {string} path - Current route path
 * @returns {string|null} Expected user type or null if not a protected route
 */
export const getUserTypeFromRoute = (path) => {
  if (path.startsWith("/customer/")) {
    return "user"; // customer type is "user" in the system
  } else if (path.startsWith("/vendor/")) {
    return "vendor";
  } else if (path.startsWith("/admin/")) {
    return "admin";
  }
  return null;
};

/**
 * Clear all authentication data
 * @param {string} userType - Specific user type to clear (optional)
 */
export const clearAuthData = (userType = null) => {
  if (userType) {
    // Clear cookies for specific user type
    clearCookiesByUserType(userType);
  } else {
    // Clear all authentication data (legacy and new format)

    // Remove legacy cookies
    Cookies.remove("accessToken");
    Cookies.remove("userId");
    Cookies.remove("userType");
    Cookies.remove("name");
    Cookies.remove("email");

    // Remove new format cookies for all user types
    const userTypes = ["user", "vendor", "admin"];
    userTypes.forEach(type => {
      clearCookiesByUserType(type);
    });

    // Clear localStorage
    localStorage.clear();
  }
};
