"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import googleIcon from "@/assets/png/google-icon.png"
import { useForm, Controller } from "react-hook-form"
import { AiFillEye, AiFillEyeInvisible, AiOutlineLoading3Quarters } from "react-icons/ai"
import signup from "@/assets/svg/signup.svg"
import { Checkbox } from "@/components/ui/checkbox"
import { Link, useNavigate } from "react-router-dom"
import { isText } from "@/utils/validation"
import { useAppDispatch } from "@/hooks/StoreHooks"
import {
  setInitialAuth,
  setInitialSetup,
  setInitialToken,
  setInitialUser,
  setToken,
  setUser,
} from "@/redux/slice/auth/authSlice"
import { useToast } from "@/components/ui/use-toast"
import { useRegisterCustomerMutation } from "@/services/auth/mutation"
import {
  Select,
  SelectContent,
  SelectGroup,
  Select<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Select<PERSON>rigger,
  SelectValue,
} from "@/components/ui/select"

const CustomerSignUpPage = () => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
      passwordConfirmation: "",
      firstName: "",
      lastName: "",
      customer_type: "",
    },
  })

  const { toast } = useToast()

  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [showPassword, setShowPassword] = useState(false)
  const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false)
  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState)
  }
  const togglePasswordConfirmationVisibility = () => {
    setShowPasswordConfirmation((prevState) => !prevState)
  }

  const password = watch("password")

  useEffect(() => {
    dispatch(setInitialUser())
    dispatch(setInitialToken())
    dispatch(setInitialAuth())
    dispatch(setInitialSetup())
  }, [dispatch])

  const registerCustomerMutation = useRegisterCustomerMutation()

  const onSubmit = async (data) => {
    const response = await registerCustomerMutation.mutateAsync({
      name: `${data.firstName} ${data.lastName}`,
      email: data.email,
      password: data.password,
      password_confirmation: data.passwordConfirmation,
      type: data.customer_type,
    })

    if (response.success) {
      toast({
        title: "Registration Successful",
        description: "Welcome to the family!",
        variant: "success",
        duration: 1500,
        isclosable: true,
      })

      dispatch(
        setUser({
          email: response?.data?.email,
          userId: response?.data?.id,
          address: response?.data?.address,
          profile: response?.data?.profile,
          name: response?.data?.name,
          userType: response?.user_type,
          isLogin: true,
        }),
      )

      dispatch(
        setToken({
          accessToken: response.token,
          refreshToken: "",
        }),
      )
      reset()
      navigate(`/customer/${response?.data?.id}/dashboard`)
    } else {
      toast({
        title: "Registration Failed",
        description: response.message,
        variant: "danger",
        duration: 1500,
        isclosable: true,
      })
    }
  }

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 sm:mt-10">
      <div className="w-full py-5">
        <div className="">
          <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">{`Create a Customer Account`}</h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">
            {`Do you want to create a vendor account? `}
            <Link to="/vendor/signup" className="text-green1 font-nunito text-bodySmall hover:underline">
              Create Vendor Account
            </Link>
          </p>
        </div>

        <div className="lg:max-w-[30rem] mt-5">
          <Button className="flex h-12 w-full justify-center items-center border bg-white text-[#3F3F3F] rounded-lg gap-4 font-nunito font-semibold hover:bg-gray-100">
            <img className="h-7 w-7" src={googleIcon || "/placeholder.svg"} alt="Google Icon" /> Login with Google
          </Button>
        </div>
        <div className="lineParent lg:max-w-[30rem]">
          <div className="frameChild" />
          <div className="or">or</div>
          <div className="frameChild" />
        </div>
        <div className="lg:max-w-[30rem] mt-5 pb-20">
          <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col xs:flex-row sm:flex-col lg:flex-row gap-4">
              <div>
                <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="firstName">
                  First
                </label>
                <Controller
                  name="firstName"
                  control={control}
                  rules={{
                    required: {
                      value: true,
                      message: "First Name is required",
                    },
                    validate: (value) => {
                      if (!isText(value)) return "Invalid First Name"
                      if (value.length < 3) return "First Name must be at least 3 characters"
                      return true
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter your first name"
                        autoComplete="off"
                        id="firstName"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.firstName ? "border-lightBrown" : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.firstName && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">{errors?.firstName?.message}</p>
                      )}
                    </>
                  )}
                />
              </div>
              <div>
                <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="lastName">
                  Last Name
                </label>
                <Controller
                  name="lastName"
                  control={control}
                  rules={{
                    required: { value: true, message: "Last Name is required" },
                    validate: (value) => {
                      if (!isText(value)) return "Invalid Last Name"
                      if (value.length < 3) return "Last Name must be at least 3 characters"
                      return true
                    },
                  }}
                  render={({ field }) => (
                    <>
                      <input
                        {...field}
                        type="text"
                        placeholder="Enter your last name"
                        autoComplete="off"
                        id="lastName"
                        className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                          errors.lastName ? "border-lightBrown" : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                      />
                      {errors?.lastName && (
                        <p className="ml-3 text-lightBrown text-bodySmall font-nunito">{errors?.lastName?.message}</p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="email">
                Email
              </label>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: { value: true, message: "Email is required" },
                }}
                render={({ field }) => (
                  <>
                    <input
                      {...field}
                      type="email"
                      id="email"
                      placeholder="Email"
                      autoComplete="off"
                      className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${
                        errors.email ? "border-lightBrown" : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    {errors.email && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">{errors.email.message}</p>
                    )}
                  </>
                )}
              />
            </div>

            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="customer_type">
                Customer Type
              </label>
              <Controller
                name="customer_type"
                control={control}
                rules={{
                  required: { value: true, message: "Customer type is required" },
                }}
                render={({ field }) => (
                  <>
                    <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                      <SelectTrigger id="customer_type" className="w-full py-2.5 mt-1">
                        <SelectValue placeholder="Select customer type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Customer type</SelectLabel>
                          <SelectItem value="individual">Individual</SelectItem>
                          <SelectItem value="organization">Organization</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    {errors.customer_type && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">{errors.customer_type.message}</p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Password Field */}
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="password">
                Password
              </label>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: { value: true, message: "Password is required" },
                  minLength: {
                    value: 6,
                    message: "Password must be at least 6 characters",
                  },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      autoComplete="off"
                      id="password"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${
                        errors.password ? "border-lightBrown" : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-[1.9rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.password && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">{errors.password.message}</p>
                    )}
                  </div>
                )}
              />
            </div>

            {/* Password Confirmation Field */}
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito" htmlFor="passwordConfirmation">
                Confirm Password
              </label>
              <Controller
                name="passwordConfirmation"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Password Confirmation is required",
                  },
                  validate: (value) => value === password || "Passwords do not match", // Ensure it matches the password field
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showPasswordConfirmation ? "text" : "password"}
                      placeholder="Confirm Password"
                      autoComplete="off"
                      id="passwordConfirmation"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${
                        errors.passwordConfirmation ? "border-lightBrown" : "border-[#CFCFCF] focus:border-[#006AFF]"
                      } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordConfirmationVisibility}
                      className="absolute right-3 top-[1.9rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showPasswordConfirmation ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.passwordConfirmation && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.passwordConfirmation.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Checkbox id="terms" />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Remember Me
                </label>
              </div>
              <Link to="#" className="text-blueGray font-nunito text-bodySmall hover:underline">
                Forgot Password ?
              </Link>
            </div>

            {/* Submit Button */}
            <div>
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
                type="submit"
                disabled={registerCustomerMutation?.isPending}
              >
                {registerCustomerMutation?.isPending ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Sign Up"
                )}
              </Button>
            </div>
          </form>
          <div className="flex items-end gap-2 mt-5 mb-20 sm:mb-0">
            <span className="font-nunito text-bodySmall">Already have an account?</span>
            <Link to="/customer/signin" className="text-green1 font-nunito text-bodySmall hover:underline">
              Sign in
            </Link>
          </div>
        </div>
      </div>
      <div className="hidden sm:block w-full">
        <img className="sm:block hidden h-full" src={signup || "/placeholder.svg"} alt="Signup Image" loading="lazy" />
      </div>
    </div>
  )
}

export default CustomerSignUpPage

