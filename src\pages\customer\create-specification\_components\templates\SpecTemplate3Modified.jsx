import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useGetCustomerProfileQuery } from "@/services/auth/query";

export default function SpecTemplate3Modified({ data }) {
  const { specificationProductData, category, subCategory, specificationType } = data || {};
  const customerData = useGetCustomerProfileQuery();
  const { name, phone, email, address } =
    customerData?.data?.data || {};

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <div className="relative overflow-hidden">
        {/* Diagonal background */}
        <div className="absolute top-0 left-0 w-full h-64 bg-primary transform -skew-y-12 origin-top-left" />

        <div className="relative p-8">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div className="text-black">
              <h1 className="text-4xl font-bold mb-4 text-white">
                Specification Sheet
              </h1>
              <div className="space-y-1">
                <p className="font-blod text-2xl">Customer:</p>
                <p className="text-base font-semibold">
                  {name || "Customer Name"}
                </p>
                <p className="text-base font-semibold">
                  {address || "Kathmandu, Nepal"}
                </p>
                <p className="text-base font-semibold">
                  {email || "<EMAIL>"}
                </p>
                <p className="text-base font-semibold">
                  {phone || "+977-9812343435"}
                </p>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="text-sm text-gray-600">
                <p className="font-semibold mb-2">Document Info</p>
                <div className="space-y-1">
                  <div className="flex justify-between gap-8">
                    <span className="text-sm font-medium">Date</span>
                    <span className="text-sm">
                      {new Date().toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Specification Details */}
          <div className="mb-6 p-4 bg-white rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold mb-3">Specification Details</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Type:</span> {specificationType || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Category:</span> {category || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Sub-Category:</span> {subCategory || 'N/A'}
              </div>
            </div>
          </div>

          {/* Specification Table */}
          <div className="mt-8 bg-white rounded-lg shadow-sm">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">S.N.</TableHead>
                  <TableHead>Item Name</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Attributes</TableHead>
                  <TableHead>Other</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {specificationProductData?.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{item.itemName}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>{item.attributes}</TableCell>
                    <TableCell>{item.other}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Footer */}
          <div className="mt-8 flex justify-between">
            <div className="space-y-4 text-sm text-gray-600">
              <div>
                <h3 className="font-medium mb-2">Remarks:</h3>
                <p>
                  All specifications are based on the customer’s provided data
                  and may be subject to change upon mutual agreement.
                </p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Terms & Conditions:</h3>
                <p>
                  Please verify all information before proceeding. Contact
                  support for clarification or modifications.
                </p>
              </div>
            </div>
            <div className="space-y-4 text-sm">
              <div className="pt-8 border-t">
                <p className="font-medium mb-2">Authorized Sign</p>
                <div className="h-12"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
