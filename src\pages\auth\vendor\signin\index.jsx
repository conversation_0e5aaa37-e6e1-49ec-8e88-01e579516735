import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import googleIcon from "@/assets/png/google-icon.png";
import { useForm, Controller } from "react-hook-form";
import {
  AiFillEye,
  AiFillEyeInvisible,
  AiOutlineLoading3Quarters,
} from "react-icons/ai";
import signin from "@/assets/svg/signin.svg";
import { Checkbox } from "@/components/ui/checkbox";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useLoginVendorMutation } from "@/services/auth/mutation";
import { useToast } from "@/components/ui/use-toast";
import { useAppDispatch } from "@/hooks/StoreHooks";
import { setToken, setUser } from "@/redux/slice/auth/authSlice";

const VendorSignInPage = () => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const { toast } = useToast();

  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  const loginVendorMutation = useLoginVendorMutation();

  const onSubmit = async (data) => {
    const response = await loginVendorMutation.mutateAsync(data);

    if (response.success) {
      toast({
        title: "Login Successful",
        description: "Welcome back!",
        variant: "success",
        duration: 1500,
        isclosable: true,
      });

      // Dispatch Redux actions for user and token management
      dispatch(
        setUser({
          email: response?.data?.email,
          userId: response?.data?.id,
          name: response?.data?.name,
        })
      );
      dispatch(setToken(response.token));
      reset();

      const from = location.state?.from || `/vendor/${response?.data?.id}/feeds`;

      if (from) {
        navigate(from, { replace: true });
      } else {
        navigate(`/vendor/${response?.data?.id}/feeds`);
      }
    } else {
      toast({
        title: "Login Failed",
        description: response.message,
        variant: "danger",
        duration: 1500,
        isclosable: true,
      });
    }
  };

  return (
    <div className="flex flex-col sm:flex-row w-full px-4 py-4 sm:gap-10 sm:px-8 xl:px-32 items-center sm:mt-10">
      <div className="w-full py-5">
        <div>
          <h1 className="font-poppins font-semibold text-heading4 leading-[50px] text-navy">{`Login with Vendor Account`}</h1>
          <p className="font-nunito text-bodyMedium leading-5 text-blueGray">
            {`Do you want to login with customer account? `}
            <Link
              to="/customer/signin"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Login with Customer Account
            </Link>
          </p>
        </div>
        <div className="lg:max-w-[30rem] mt-5">
          <Button className="flex h-12 w-full justify-center items-center border bg-white text-[#3F3F3F] rounded-lg gap-4 font-nunito font-semibold hover:bg-gray-100">
            <img className="h-7 w-7" src={googleIcon} alt="Google Icon" /> Login
            with Google
          </Button>
        </div>
        <div className="lineParent lg:max-w-[30rem]">
          <div className="frameChild" />
          <div className="or">or</div>
          <div className="frameChild" />
        </div>
        <div className="lg:max-w-[30rem] mt-5">
          <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Email
              </label>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Email is required",
                  },
                }}
                render={({ field }) => (
                  <>
                    <input
                      {...field}
                      type="email"
                      placeholder="Email"
                      autoComplete="off"
                      className={`w-full py-2.5 rounded-lg px-4 font-nunito cursor-pointer border mt-1 ${errors.email
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                    />
                    {errors.email && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.email.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Password Field */}
            <div>
              <label className="ml-1 text-bodySmall font-medium font-nunito">
                Password
              </label>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Password is required",
                  },
                  minLength: {
                    value: 6,
                    message: "Password must be at least 6 characters",
                  },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      autoComplete="off"
                      className={`w-full py-2.5 font-nunito rounded-lg px-4 cursor-pointer border mt-1 ${errors.password
                          ? "border-lightBrown"
                          : "border-[#CFCFCF] focus:border-[#006AFF]"
                        } mb-1 focus:outline-none`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-[1.7rem] transform -translate-y-1/2 text-xl text-gray-500"
                    >
                      {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                    </button>
                    {errors.password && (
                      <p className="ml-3 text-lightBrown text-bodySmall font-nunito">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Checkbox id="terms" />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Remember Me
                </label>
              </div>
              <Link
                to="/vendor/forgot-password"
                className="text-blueGray font-nunito text-bodySmall hover:underline"
              >
                Forgot Password ?
              </Link>
            </div>

            {/* Submit Button */}
            <div>
              <Button
                className="font-nunito text-bodyMedium font-extrabold bg-green2 hover:bg-green1 text-darkNavy h-[3rem] w-full px-12 rounded-xl tracking-wider"
                type="submit"
                disabled={loginVendorMutation?.isPending}
              >
                {loginVendorMutation?.isPending ? (
                  <AiOutlineLoading3Quarters className="text-xl animate-spin" />
                ) : (
                  "Sign In"
                )}
              </Button>
            </div>
          </form>
          <div className="flex items-end gap-2 mt-5">
            <span className="font-nunito text-bodySmall">
              Not registered yet?
            </span>
            <Link
              to="/vendor/signup"
              className="text-green1 font-nunito text-bodySmall hover:underline"
            >
              Create an Vendor Account
            </Link>
          </div>
        </div>
      </div>
      <div className="hidden sm:block w-full">
        <img
          className="sm:block hidden h-full"
          src={signin}
          alt="Signin Image"
        />
      </div>
    </div>
  );
};

export default VendorSignInPage;
