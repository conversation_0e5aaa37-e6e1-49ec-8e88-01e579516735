"use client";

import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export default function DocumentUploadForm({
  onNext,
  onPrevious,
  initialData,
  companyType,
}) {
  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: initialData || { idProof: null, addressProof: null },
  });

  const onSubmit = (data) => {
    onNext({ documents: data });
  };
  console.log(companyType);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* First Row */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-8">
        <div className="w-full md:w-1/2">
          <Label htmlFor="idProof">Company registration Doc.</Label>
          <Controller
            name="idProof"
            control={control}
            rules={{ required: "ID Proof is required" }}
            render={({ field }) => (
              <Input
                id="idProof"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.idProof && (
            <p className="text-red-500 text-sm">{errors.idProof.message}</p>
          )}
        </div>

        <div className="w-full md:w-1/2">
          <Label htmlFor="vat">VAT/PAN</Label>
          <Controller
            name="vat"
            control={control}
            rules={{ required: "VAT/PAN Proof is required" }}
            render={({ field }) => (
              <Input
                id="vat"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.vat && (
            <p className="text-red-500 text-sm">{errors.vat.message}</p>
          )}
        </div>
      </div>

      {/* Conditional Row for Tax Clearance and Audit Report */}
      {companyType !== "government" && (
        <div className="flex flex-col md:flex-row gap-4 md:gap-8">
          <div className="w-full md:w-1/2">
            <Label htmlFor="tax"> Recent Tax Clearance</Label>
            <Controller
              name="tax"
              control={control}
              rules={{ required: "Tax Clearance is required" }}
              render={({ field }) => (
                <Input
                  id="tax"
                  type="file"
                  onChange={(e) => field.onChange(e.target.files?.[0] || null)}
                />
              )}
            />
            {errors.tax && (
              <p className="text-red-500 text-sm">{errors.tax.message}</p>
            )}
          </div>

          <div className="w-full md:w-1/2">
            <Label htmlFor="audit">Audit Report</Label>
            <Controller
              name="audit"
              control={control}
              rules={{ required: "Audit Report is required" }}
              render={({ field }) => (
                <Input
                  id="audit"
                  type="file"
                  onChange={(e) => field.onChange(e.target.files?.[0] || null)}
                />
              )}
            />
            {errors.audit && (
              <p className="text-red-500 text-sm">{errors.audit.message}</p>
            )}
          </div>
        </div>
      )}

      {/* Third Row */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-8">
        <div className="w-full md:w-1/2">
          <Label htmlFor="logo">Company Logo</Label>
          <Controller
            name="logo"
            control={control}
            rules={{ required: "Company Logo is required" }}
            render={({ field }) => (
              <Input
                id="logo"
                type="file"
                accept="image/*"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.logo && (
            <p className="text-red-500 text-sm">{errors.logo.message}</p>
          )}
        </div>

        <div className="w-full md:w-1/2">
          <Label htmlFor="stamp">Company Stamp</Label>
          <Controller
            name="stamp"
            control={control}
            rules={{ required: "Company Stamp is required" }}
            render={({ field }) => (
              <Input
                id="stamp"
                type="file"
                accept="image/*"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.stamp && (
            <p className="text-red-500 text-sm">{errors.stamp.message}</p>
          )}
        </div>
      </div>

      {/* Fourth Row */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-8">
        <div className="w-full md:w-1/2">
          <Label htmlFor="sign">Authorized Signature</Label>
          <Controller
            name="sign"
            control={control}
            rules={{ required: "Authorized Signature is required" }}
            render={({ field }) => (
              <Input
                id="sign"
                type="file"
                onChange={(e) => field.onChange(e.target.files?.[0] || null)}
              />
            )}
          />
          {errors.sign && (
            <p className="text-red-500 text-sm">{errors.sign.message}</p>
          )}
        </div>

        <div className="w-full md:w-1/2">
          <Label htmlFor="website">Website</Label>
          <Input id="website" {...register("website")} />
          {errors.website && (
            <p className="text-red-500 text-sm">{errors.website.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-6 mt-8">
        <Button
          className="w-48"
          type="button"
          variant="outline"
          onClick={onPrevious}
        >
          Previous
        </Button>
        <Button className="w-48" type="submit">
          Next
        </Button>
      </div>
    </form>
  );
}
