import { useState } from "react";

const EditableTemplate = ({ data, onSave }) => {
    const [editedData, setEditedData] = useState(data.specificationProductData);

    const handleChange = (index, field, value) => {
        const newData = [...editedData];
        newData[index][field] = value;
        setEditedData(newData);
    };

    return (
        <div className="w-full p-4">
            <table className='w-full mt-4 border-collapse'>
                <tbody className="w-full">
                    {editedData.map((item, index) => (
                        <tr key={index} className="w-full">
                            <td className="p-2 border border-gray-300">{index + 1}</td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    value={item.itemName}
                                    onChange={(e) => handleChange(index, 'itemName', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="number"
                                    value={item.quantity}
                                    onChange={(e) => handleChange(index, 'quantity', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="number"
                                    value={item.unit}
                                    onChange={(e) => handleChange(index, 'unit', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="text"
                                    value={item.specification}
                                    onChange={(e) => handleChange(index, 'specification', e.target.value)}
                                />
                            </td>
                            <td className="p-2 border border-gray-300">
                                <input
                                    type="text"
                                    value={item.other}
                                    onChange={(e) => handleChange(index, 'other', e.target.value)}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <div className="w-full flex justify-end mt-4">
                <button
                    onClick={() => onSave(editedData)}
                    className='w-[100px] font-bold tracking-wider flex items-center justify-center border-2 border-primary bg-primary text-black rounded-lg px-6 py-3'
                >
                    Save
                </button>
            </div>
        </div>
    );
};

export default EditableTemplate