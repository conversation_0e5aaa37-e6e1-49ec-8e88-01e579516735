const isText = (value) => {
  return /^[a-zA-Z ]+$/.test(value);
};

const isEmail = (value) => {
  if (!value) return true;

  return /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(value);
};

const isPhoneNumber = (value) => {
  return /^[0-9]{10}$/.test(value);
};

const isUrl = (value) => {
  return /^(http|https):\/\/[^ "]+$/.test(value);
};

const isNumber = (value) => {
  return /^[0-9]+$/.test(value);
};

const isValidText = (value) => {
  return /^[a-zA-Z0-9\s,.'-]{3,}$/.test(value);
};

const isValidState = (value) => {
  return /^[a-zA-Z\s,.'-]{2,}$/.test(value);
};

const isValidYearBuilt = (year) => {
  const currentYear = new Date().getFullYear();
  return year >= 1800 && year <= currentYear;
};

export {
  isText,
  isEmail,
  isPhoneNumber,
  isUrl,
  isNumber,
  isValidYearBuilt,
  isValidText,
  isValidState,
};
