import company1 from "@/assets/png/company1.png";
import company2 from "@/assets/png/company2.png";
import company3 from "@/assets/png/company3.png";

const TrustedBySection = () => {
  return (
    <div className="flex flex-col justify-between items-center gap-16 bg-[#FBFBFB] -mt-16 py-10">
      <p className="font-nunito font-medium text-bodyLarge leading-6">
        {`We are `}
        <span className="text-green1 font-medium">{`trusted`}</span> {`by`}
      </p>
      <div className="flex items-center justify-center gap-10 flex-wrap">
        <img
          src={company1}
          alt="company 1"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company2}
          alt="company 2"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company1}
          alt="company 1"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company3}
          alt="company 3"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company1}
          alt="company 1"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company2}
          alt="company 2"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
        <img
          src={company1}
          alt="company 1"
          className="w-auto h-5 lg:h-8"
          loading="lazy"
        />
      </div>
    </div>
  );
};

export default TrustedBySection;
