import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createNotice,
  deleteNotice,
  getNotices,
  updateNotice,
  getTrainingSessions,
  createTrainingSession,
  updateTrainingSession,
  deleteTrainingSession,
  getTrainingSessionById,
  getAdminSpecifications,
  updateSpecificationStatus,
  getAdminSpecificationById,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getSubCategories,
  createSubCategory,
  updateSubCategory,
  deleteSubCategory
} from "./api";

// Notice queries
export function useGetNotices() {
  return useQuery({
    queryKey: ["getNotices"],
    queryFn: () => getNotices(),
  });
}

// Create notice mutation
export const useCreateNotice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (formData) => createNotice(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notices'] });
    },
  });
};

// Update notice mutation
export const useUpdateNotice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updateNotice(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notices'] });
    },
  });
};

export function useDeleteNotice() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteNotice(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getNotices"] });
    },
  });
}

// Training queries and mutations
export function useGetTrainingSessions() {
  return useQuery({
    queryKey: ["getTrainingSessions"],
    queryFn: () => getTrainingSessions(),
  });
}

export function useGetTrainingSessionById(id) {
  return useQuery({
    queryKey: ["getTrainingSession", id],
    queryFn: () => getTrainingSessionById(id),
    enabled: !!id,
  });
}

export const useCreateTrainingSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (trainingData) => createTrainingSession(trainingData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};

export const useUpdateTrainingSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updateTrainingSession(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};

export function useDeleteTrainingSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteTrainingSession(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};

// Specification queries
export function useGetAdminSpecifications(page = 1, perPage = 15) {
  return useQuery({
    queryKey: ["getAdminSpecifications", page, perPage],
    queryFn: () => getAdminSpecifications(page, perPage),
  });
}

export function useGetAdminSpecificationById(id) {
  return useQuery({
    queryKey: ["getAdminSpecificationById", id],
    queryFn: () => getAdminSpecificationById(id),
    enabled: !!id,
  });
}

export function useUpdateSpecificationStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, statusData }) => updateSpecificationStatus(id, statusData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getAdminSpecifications"] });
    },
  });
};

// Category queries
export function useGetCategories() {
  return useQuery({
    queryKey: ["getCategories"],
    queryFn: () => getCategories(),
  });
}

export function useCreateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryData) => createCategory(categoryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
    },
  });
}

export function useUpdateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, categoryData }) => updateCategory(id, categoryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
    },
  });
}

export function useDeleteCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
    },
  });
}

// Sub-category queries
export function useGetSubCategories() {
  return useQuery({
    queryKey: ["getSubCategories"],
    queryFn: () => getSubCategories(),
  });
}

export function useCreateSubCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (subCategoryData) => createSubCategory(subCategoryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
      queryClient.invalidateQueries({ queryKey: ["getSubCategories"] });
    },
  });
}

export function useUpdateSubCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, subCategoryData }) => updateSubCategory(id, subCategoryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
      queryClient.invalidateQueries({ queryKey: ["getSubCategories"] });
    },
  });
}

export function useDeleteSubCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteSubCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getCategories"] });
      queryClient.invalidateQueries({ queryKey: ["getSubCategories"] });
    },
  });
};