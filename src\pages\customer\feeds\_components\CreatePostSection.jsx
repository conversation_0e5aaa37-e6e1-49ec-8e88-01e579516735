import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { LuImage } from "react-icons/lu";
import { <PERSON><PERSON><PERSON>Check } from "react-icons/lu";
import { LuSend } from "react-icons/lu";

const CreatePostSection = () => {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: {
      message: "",
    },
  });

  const onSubmit = (data) => {
    console.log(data);
    reset();
  };

  return (
    <div className="border border-darkMedium rounded-xl shadow-sm">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex w-full p-2">
          <Avatar>
            <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <div className="w-full">
            <Controller
              name="message"
              control={control}
              render={({ field }) => (
                <>
                  <Textarea
                    {...field}
                    name="message"
                    id="message"
                    className="text-[0.8rem] border-none resize-none focus:ring-0 !ring-offset-0 focus-visible:!ring-0 focus-visible:!outline-none"
                    placeholder="Type a message"
                  />
                </>
              )}
            />
          </div>
        </div>
        <div className="bg-green4 flex justify-between rounded-b-xl">
          <div className="py-2">
            <Button className="bg-transparent text-black/60 font-nunito hover:text-black">
              <LuImage />
              Photo
            </Button>
            <Button className="bg-transparent text-black/60 font-nunito hover:text-black">
              <LuCopyCheck />
              Template
            </Button>
          </div>
          <div className="bg-green3 rounded-t-none rounded-bl-none rounded-br-xl">
            <Button
              type="submit"
              className="text-black/60 bg-transparent h-full font-nunito hover:bg-green1 hover:text-black rounded-t-none rounded-bl-none rounded-br-xl"
            >
              <LuSend className="mr-1" />
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreatePostSection;
