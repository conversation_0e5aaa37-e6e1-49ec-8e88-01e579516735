
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  AiOutlineHome,
  AiOutlineArrowLeft,
  AiOutlineSearch,
  AiOutlineReload,
  AiOutlineRocket
} from "react-icons/ai";
import { BiError } from "react-icons/bi";
import { FiMapPin } from "react-icons/fi";

const NotFound = () => {
  const navigate = useNavigate();
  const [isAnimated, setIsAnimated] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    setIsAnimated(true);
  }, []);

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/");
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // You can implement search functionality here
      navigate(`/?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const quickLinks = [
    { name: "Home", path: "/", icon: AiOutlineHome },
    { name: "Customer Login", path: "/customer/signin", icon: AiOutlineRocket },
    { name: "Vendor Login", path: "/vendor/signin", icon: AiOutlineRocket },
    { name: "About Us", path: "/about", icon: FiMapPin },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-green1/5 to-green2/10 flex items-center justify-center px-4 py-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Animated 404 Number */}
        <div className={`transition-all duration-1000 ${isAnimated ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="relative mb-8">
            {/* Background decorative elements */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-96 h-96 bg-green2/10 rounded-full blur-3xl animate-pulse"></div>
            </div>

            {/* Main 404 text */}
            <h1 className="relative text-[12rem] md:text-[16rem] font-poppins font-bold text-transparent bg-gradient-to-r from-green1 to-green2 bg-clip-text leading-none">
              404
            </h1>

            {/* Floating error icon */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <BiError className="text-6xl text-green1 animate-bounce" />
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className={`transition-all duration-1000 delay-300 ${isAnimated ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Title and Description */}
          <div className="mb-8">
            <h2 className="text-heading3 md:text-heading2 font-poppins font-bold text-navy mb-4">
              Oops! Page Not Found
            </h2>
            <p className="text-bodyLarge text-blueGray max-w-2xl mx-auto leading-relaxed">
              The page you're looking for seems to have wandered off into the digital wilderness.
              Don't worry, even the best explorers sometimes take a wrong turn!
            </p>
          </div>

          {/* Search Bar */}
          <div className="mb-8">
            <form onSubmit={handleSearch} className="max-w-md mx-auto">
              <div className="relative">
                <AiOutlineSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blueGray text-xl" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search for what you need..."
                  className="w-full pl-12 pr-4 py-3 rounded-xl border border-lightGray1 focus:border-green1 focus:outline-none focus:ring-2 focus:ring-green1/20 transition-all duration-300"
                />
                <Button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-green2 hover:bg-green1 text-darkNavy px-4 py-2 rounded-lg transition-all duration-300"
                >
                  Search
                </Button>
              </div>
            </form>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Button
              onClick={handleGoBack}
              className="bg-green2 hover:bg-green1 text-darkNavy font-nunito font-bold px-8 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
            >
              <AiOutlineArrowLeft className="text-lg" />
              Go Back
            </Button>

            <Link to="/">
              <Button className="bg-navy hover:bg-darkNavy text-white font-nunito font-bold px-8 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2">
                <AiOutlineHome className="text-lg" />
                Home Page
              </Button>
            </Link>

            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-green1 text-green1 hover:bg-green1 hover:text-white font-nunito font-bold px-8 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
            >
              <AiOutlineReload className="text-lg" />
              Refresh
            </Button>
          </div>

          {/* Quick Links */}
          <div className="mb-8">
            <h3 className="text-heading6 font-poppins font-semibold text-navy mb-4">
              Quick Links
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {quickLinks.map((link, index) => (
                <Link
                  key={index}
                  to={link.path}
                  className="group p-4 bg-white rounded-xl shadow-custom-shadow hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-lightGray1 hover:border-green1"
                >
                  <link.icon className="text-2xl text-green1 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
                  <p className="text-bodySmall font-nunito font-medium text-navy group-hover:text-green1 transition-colors duration-300">
                    {link.name}
                  </p>
                </Link>
              ))}
            </div>
          </div>

          {/* Help Text */}
          <div className="text-center">
            <p className="text-bodyMedium text-blueGray mb-2">
              Still can't find what you're looking for?
            </p>
            <Link
              to="/contact"
              className="text-green1 hover:text-green2 font-nunito font-semibold underline transition-colors duration-300"
            >
              Contact our support team
            </Link>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="fixed top-10 left-10 w-20 h-20 bg-green2/20 rounded-full blur-xl animate-pulse"></div>
        <div className="fixed bottom-10 right-10 w-32 h-32 bg-green1/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="fixed top-1/3 right-20 w-16 h-16 bg-yellow1/20 rounded-full blur-lg animate-pulse delay-500"></div>
      </div>
    </div>
  );
};

export default NotFound;
